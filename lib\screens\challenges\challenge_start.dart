import 'package:achawach/widgets/level_up_screen.dart';
import 'package:achawach/widgets/noLivesScreen.dart';
import 'package:achawach/widgets/questionsContent.dart';
import 'package:achawach/widgets/score_life_level.dart';
import 'package:achawach/widgets/full_page_loader.dart';
import 'package:flutter/material.dart';
import 'package:confetti/confetti.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:async';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/services/service_locator.dart';
import 'package:achawach/services/question_service.dart';
import 'package:achawach/services/timer_service.dart';
import 'package:achawach/services/life_service.dart';
import 'package:achawach/services/level_service.dart';
import 'package:achawach/services/media_service.dart';

class ChallengeStartScreen extends StatefulWidget {
  final Map<String, dynamic> selectedChallenge;
  final int categoryId;

  const ChallengeStartScreen(
      {super.key, required this.selectedChallenge, required this.categoryId});

  @override
  State<ChallengeStartScreen> createState() => _ChallengeStartScreenState();
}

class _ChallengeStartScreenState extends State<ChallengeStartScreen>
    with SingleTickerProviderStateMixin {
  // Animation controllers
  late AnimationController _animationController;
  late ConfettiController _confettiController;

  // Services
  late ServiceLocator _serviceLocator;
  late QuestionService _questionService;
  late TimerService _timerService;
  late LifeService _lifeService;
  late LevelService _levelService;
  late MediaService _mediaService;
  int lifeCount = 0;
  // State variables
  int currentQuestionIndex = 0;
  num score = 0;
  int currentScore = 0;
  bool isChallengeCompleted = false;
  String? selectedAnswer;
  bool _isLoading = true;
  String? _error;
  bool _isAnswerSubmitting = false;
  bool _hasReceivedSocketData = false;
  Timer? _loadingTimeoutTimer;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 2));

    // Initialize services
    _serviceLocator = ServiceLocator();
    _serviceLocator.initializeServices(
      context: context,
      challengeId: widget.selectedChallenge['id'],
      categoryId: widget.categoryId,
    );

    _questionService = _serviceLocator.questionService;
    _timerService = _serviceLocator.timerService;
    _lifeService = _serviceLocator.lifeService;
    _levelService = _serviceLocator.levelService;
    _mediaService = _serviceLocator.mediaService;

    // Start loading data
    _initializeChallenge();
    _loadQuestions();
    _animationController.forward();

    // Listen to timer service changes
    _timerService.addListener(_onTimerChanged);
  }

  void _onTimerChanged() {
    if (_timerService.hasExpired()) {
      _onTimeout();
    }
    setState(() {});
  }

  Future<void> _loadQuestions() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      await _questionService.loadQuestions();

      // Don't hide loading yet - wait for socket data
      if (_questionService.questions.isNotEmpty) {
        debugPrint(
            'Questions loaded: ${_questionService.questions.length} questions');

        // Initialize timer and media for the first question
        _timerService
            .resetTimer(_questionService.questions[currentQuestionIndex].time);
        _initializeMediaForCurrentQuestion();
        _timerService
            .startTimer(_questionService.questions[currentQuestionIndex].time);

        // Check if ready to show (questions loaded, now wait for socket data)
        debugPrint(
            'Checking if ready to show - hasReceivedSocketData: $_hasReceivedSocketData');
        _checkIfReadyToShow();
      } else {
        setState(() {
          _error = 'No questions available for this challenge';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  // Check if both questions and socket data are ready
  void _checkIfReadyToShow() {
    if (_hasReceivedSocketData && _questionService.questions.isNotEmpty) {
      _loadingTimeoutTimer?.cancel();
      setState(() {
        _isLoading = false;
      });
    } else if (_questionService.questions.isNotEmpty &&
        !_hasReceivedSocketData) {
      // Start timeout timer if questions are loaded but no socket data yet
      _startLoadingTimeout();
    }
  }

  // Start a timeout timer to show the screen even if socket data doesn't arrive
  void _startLoadingTimeout() {
    _loadingTimeoutTimer?.cancel();
    _loadingTimeoutTimer = Timer(const Duration(seconds: 3), () {
      if (!_hasReceivedSocketData && _questionService.questions.isNotEmpty) {
        debugPrint(
            'Loading timeout reached, showing screen without socket data');
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  Future<void> _initializeChallenge() async {
    try {
      // Check if this is a new challenge (status = 0 or 'New') or an existing one (status = 1 or 'Started')
      final dynamic status = widget.selectedChallenge['status'];
      await _questionService.initializeChallenge(status);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error initializing challenge: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const GameLoader();
    }

    if (_error != null) {
      return Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  'Error: $_error',
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _loadQuestions,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_questionService.questions.isEmpty) {
      return Scaffold(
        backgroundColor: AppColors.background,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.quiz_outlined, size: 64),
              const SizedBox(height: 16),
              Text(
                'No questions available',
                style: GoogleFonts.rubik(fontSize: 20),
              ),
            ],
          ),
        ),
      );
    }

    if (isChallengeCompleted) {
      return Scaffold(
        backgroundColor: AppColors.background,
        body: Stack(
          children: [
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ConfettiWidget(
                    confettiController: _confettiController,
                    blastDirection: -3.14 / 2,
                    emissionFrequency: 0.05,
                    numberOfParticles: 20,
                    gravity: 0.05,
                    shouldLoop: false,
                    colors: const [
                      Colors.green,
                      Colors.blue,
                      Colors.pink,
                      Colors.orange,
                      Colors.purple
                    ],
                  ),
                  const Icon(Icons.emoji_events, size: 80, color: Colors.amber),
                  const SizedBox(height: 24),
                  Text(
                    'Challenge Completed!',
                    style: GoogleFonts.rubik(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(26),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Your Score',
                          style: GoogleFonts.rubik(
                            fontSize: 20,
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          score.toString(),
                          style: GoogleFonts.rubik(
                            fontSize: 48,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                  ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('Back to Challenges'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    final currentQuestion = _questionService.questions[currentQuestionIndex];

    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => {
                          Navigator.of(context).pop(),
                          Navigator.of(context).pop()
                        },
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              ScoreLifeLevel(
                                challengeId: widget.selectedChallenge['id'],
                                onLevelUp:
                                    (oldLevel, newLevel, bonusPoints, score) {
                                  // Handle level up event using LevelService

                                  setState(() {
                                    currentScore = score;
                                  });

                                  _levelService.updateProgress({
                                    'level': newLevel,
                                    'score': score,
                                    'bonus': bonusPoints,
                                  }, true);
                                },
                                onLifeChange: (lifeData) async {
                                  debugPrint('Socket data received: $lifeData');

                                  // Mark that socket data has been received
                                  if (!_hasReceivedSocketData) {
                                    debugPrint(
                                        'First socket data received, marking as ready');
                                    _hasReceivedSocketData = true;
                                    _checkIfReadyToShow();
                                  }

                                  // Update life data using LifeService
                                  _lifeService.updateLifeData(lifeData);

                                  // Check if user has no lives left and show NoLivesScreen
                                  if (!_lifeService.hasLivesLeft()) {
                                    await _mediaService.stopSound();
                                    await _mediaService.playLost();
                                    if (mounted) {
                                      Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  NoLivesScreen(
                                                    onBuyLives: () => {},
                                                    categoryId:
                                                        widget.categoryId,
                                                    isStarted: true,
                                                  )));
                                    }
                                  }
                                  setState(() {});
                                },
                                mediaService: _mediaService,
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.white,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: LinearProgressIndicator(
                        value:
                            _timerService.remainingTime / currentQuestion.time,
                        backgroundColor: Colors.grey[200],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _timerService.remainingTime <= 5
                              ? Colors.red
                              : AppColors.primary,
                        ),
                        minHeight: 10,
                      ),
                    ),
                  ),
                  Text(
                    'Time: ${_timerService.remainingTime} seconds',
                    style: GoogleFonts.rubik(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  Card(
                    elevation: 5,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Container(
                      width: double.infinity,
                      constraints: BoxConstraints(
                        maxHeight: MediaQuery.of(context).size.height * 0.4,
                      ),
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: QuestionsContent(question: currentQuestion),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Expanded(
                    child: ListView.builder(
                      itemCount: currentQuestion.choices.length,
                      itemBuilder: (context, index) {
                        final choice = currentQuestion.choices[index];
                        final isSelected = selectedAnswer == choice.choice;

                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: AnimatedOpacity(
                            opacity: _isAnswerSubmitting ? 0.5 : 1.0,
                            duration: const Duration(milliseconds: 200),
                            child: ElevatedButton(
                              onPressed: _isAnswerSubmitting
                                  ? null
                                  : () => _submitAnswer(choice.id),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: isSelected
                                    ? AppColors.primary
                                    : Colors.white,
                                padding: const EdgeInsets.all(16),
                                elevation: isSelected ? 8 : 2,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    width: 32,
                                    height: 32,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: isSelected
                                          ? Colors.white
                                          : AppColors.primary.withAlpha(26),
                                    ),
                                    child: Center(
                                      child: Text(
                                        String.fromCharCode(65 + index),
                                        style: GoogleFonts.rubik(
                                          color: isSelected
                                              ? AppColors.primary
                                              : Colors.black87,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Text(
                                      choice.choice,
                                      style: GoogleFonts.rubik(
                                        color: isSelected
                                            ? Colors.white
                                            : Colors.black87,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            ConfettiWidget(
              confettiController: _confettiController,
              blastDirectionality: BlastDirectionality.explosive,
              particleDrag: 0.05,
              emissionFrequency: 0.05,
              numberOfParticles: 20,
              gravity: 0.05,
              shouldLoop: false,
              colors: const [
                Colors.green,
                Colors.blue,
                Colors.pink,
                Colors.orange,
                Colors.purple
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _loadingTimeoutTimer?.cancel();
    _timerService.removeListener(_onTimerChanged);
    _animationController.dispose();
    _confettiController.dispose();
    _mediaService.dispose();
    _serviceLocator.disposeServices();
  }

  Future<void> _submitAnswer(int choiceId) async {
    if (_isAnswerSubmitting) return;

    // Check if user has lives left
    if (!_lifeService.hasLivesLeft()) {
      await _mediaService.playLost();
      if (mounted) {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => NoLivesScreen(
                      onBuyLives: () => {},
                      categoryId: widget.categoryId,
                      isStarted: true,
                    )));
      }
      return;
    }

    setState(() {
      _isAnswerSubmitting = true;
      selectedAnswer = _questionService.questions[currentQuestionIndex].choices
          .firstWhere((choice) => choice.id == choiceId)
          .choice;
    });

    try {
      // Stop the timer while submitting answer
      _timerService.stopTimer();
      final int levelBeforeAnswer = _levelService.currentLevel;
      // Submit the answer using QuestionService
      final result = await _questionService.submitAnswer(
          _questionService.questions[currentQuestionIndex].id, choiceId);

      if (!mounted) return;

      if (result['isCorrect']) {
        await _mediaService.playCorrectSound();

        // 3. GET the new level AFTER the score has been updated.
        final int levelAfterAnswer = _levelService.currentLevel;

        // 4. THE CRITICAL CHECK: Show the level up screen ONLY if the level has increased.
        if (levelAfterAnswer > levelBeforeAnswer) {
          // A level up just occurred!
          await _mediaService.stopSound();
          await _mediaService.playlevelSound();
          _confettiController.play();

          // Pass the NEW level to the screen.
          await _showLevelUpScreen(levelAfterAnswer);
        } else {
          // No level up, just move to the next question.
          _moveToNextQuestion(false);
        }
      } else {
        // Wrong answer logic remains the same.
        await _mediaService.playWrongSound();
        _moveToNextQuestion();
      }
      // Play sound based on result using MediaService
      // if (result['isCorrect']) {
      //   await _mediaService.playCorrectSound();

      //   // Show level up screen if needed
      //   if (_levelService.isLevelUpPending) {
      //     await _mediaService.stopSound();
      //     await _mediaService.playlevelSound();
      //     _confettiController.play();
      //     await _showLevelUpScreen(_levelService.currentLevel);
      //   } else {
      //     _moveToNextQuestion(false);
      //   }
      // } else {
      //   await _mediaService.playWrongSound();
      //   _moveToNextQuestion();
      // }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error submitting answer: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAnswerSubmitting = false;
        });
      }
    }
  }

  Future<void> _showLevelUpScreen(int newLevel) async {
    // Mark level up as handled
//    _levelService.acknowledgeLevelUp();

    if (!mounted) return;

    // Calculate score needed for next level

    await Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        pageBuilder: (context, animation, secondaryAnimation) {
          return LevelUpScreen(
            newLevel: newLevel,
            bonusPoints: _levelService.levelUpBonusPoints,
            currentScore: currentScore,
            onContinue: () {
              Navigator.of(context).pop();
              _mediaService.stopSound();
              _moveToNextQuestion(false);
            },
          );
        },
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );
  }

  void _moveToNextQuestion([bool shouldDecreaseLife = true]) {
    _timerService.stopTimer();

    if (currentQuestionIndex < _questionService.questions.length - 1) {
      setState(() {
        currentQuestionIndex++;
        selectedAnswer = null;
      });

      // Initialize media and timer for the next question
      _initializeMediaForCurrentQuestion();
      _timerService
          .resetTimer(_questionService.questions[currentQuestionIndex].time);
      _timerService
          .startTimer(_questionService.questions[currentQuestionIndex].time);
    } else {
      setState(() {
        isChallengeCompleted = true;
      });
      _confettiController.play();
    }
  }

  Future<void> _initializeMediaForCurrentQuestion() async {
    final question = _questionService.questions[currentQuestionIndex];
    await _mediaService.initializeMediaForQuestion(question);
  }

  void _onTimeout() async {
    // Ensure the widget is still mounted before doing anything
    if (!mounted) return;

    final question = _questionService.questions[currentQuestionIndex];

    try {
      await _mediaService.stopSound();
      // Play the timeout sound
      await _mediaService.playTimeoutSound();

      // Call the API to decrease the user's life.
      // The UI will update automatically via your socket/callback mechanism.
      await _lifeService.decreaseLife(question.id);
    } catch (e) {
      // It's crucial to log errors so you know what's going wrong.
      // Never leave a catch block empty!

      // You could also show a message to the user here if needed.
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(content: Text('Could not sync with server.')),
      // );
    } finally {
      // The 'finally' block ALWAYS runs, whether the 'try' succeeded or failed.
      // This is the perfect place to move to the next question.
      // We check for 'mounted' again as the async operations above might take time.
      if (mounted) {
        _moveToNextQuestion();
      }
    }
  }
}
