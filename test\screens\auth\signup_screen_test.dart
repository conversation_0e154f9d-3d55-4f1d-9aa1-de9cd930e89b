import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:achawach/screens/auth/signup_screen.dart';

// Simple widget for testing signup screen functionality
class SimpleSignupWidget extends StatelessWidget {
  const SimpleSignupWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      home: SignupScreen(),
    );
  }
}

void main() {
  group('SignupScreen Tests', () {
    testWidgets('SignupScreen renders correctly', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleSignupWidget());

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify the widget renders without errors
      expect(find.byType(Scaffold), findsOneWidget);
    });

    testWidgets('SignupScreen has app bar', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleSignupWidget());

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify app bar is present
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('SignupScreen has interactive elements',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleSignupWidget());

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify interactive elements are present
      expect(find.byType(GestureDetector), findsAtLeastNWidgets(1));
      expect(find.byType(InkWell), findsAtLeastNWidgets(1));
    });
  });
}
