Project Plan



1. Create a public screen to display challenges and catgories 
# the Screen must be defult screen when user not sign in and when the user try to play the challenges it should redirect to login screen or sign up screen 
##  here is the backend APi and reponces 

### /public/challenges/top

it is reurns top challenges 
here is the example 
{
    "status": "success",
    "message": "Top challenges retrieved successfully",
    "data": [
        {
            "id": 13,
            "name": "Mystery Quest",
            "winning_rules": {
                "level": 2,
                "winning_points": 100,
                "rank": 1
            },
            "reward": 1000,
            "category_id": 0,
            "createdAt": "2025-03-15T10:56:59.000Z",
            "updatedAt": "2025-06-30T07:48:01.000Z",
            "user_life": 3,
            "category": null,
            "description": null,
            "status": 1,
            "start_date": "2025-04-21T14:09:56.000Z",
            "end_date": "2025-09-28T13:45:00.000Z"
        },
        {
            "id": 12,
            "name": "Survival Mode",
            "winning_rules": {
                "level": 2,
                "winning_points": 100,
                "rank": 1
            },
            "reward": 10000,
            "category_id": 0,
            "createdAt": "2025-03-15T10:56:28.000Z",
            "updatedAt": "2025-06-30T07:48:01.000Z",
            "user_life": 4,
            "category": null,
            "description": null,
            "status": 1,
            "start_date": "2025-04-21T14:09:56.000Z",
            "end_date": "2025-09-28T13:41:00.000Z"
        }
    ]
}

### /public/categories

it is reurns all categories 
here is the example 
{
    "status": "success",
    "message": "Categories retrieved successfully",
    "data": [
        {
            "id": 5,
            "name": "Community Challenge",
            "icon": "Icons.people_alt_rounded",
            "createdAt": "2025-03-07T12:50:47.000Z",
            "updatedAt": "2025-03-07 15:50:47"
        },
        {
            "id": 4,
            "name": "Daily Challenge",
            "icon": "Icons.today_rounded",
            "createdAt": "2025-03-07T12:48:35.000Z",
            "updatedAt": "2025-03-07 15:48:35"
        },
        {
            "id": 3,
            "name": "Special Challenge",
            "icon": "Icons.star_rounded",
            "createdAt": "2025-03-07T12:45:06.000Z",
            "updatedAt": "2025-03-07 15:45:06"
        },
        {
            "id": 2,
            "name": "Monthly Challenge",
            "icon": "Icons.calendar_month_rounded",
            "createdAt": "2025-03-07T12:44:12.000Z",
            "updatedAt": "2025-03-07 15:44:12"
        },
        {
            "id": 1,
            "name": "Weekly Challenge",
            "icon": "Icons.calendar_view_week",
            "createdAt": "2025-03-07T12:43:20.000Z",
            "updatedAt": "2025-03-07 15:43:20"
        }
    ]
}

### /public/categories/:categoryId/challenges
It is reurns all challenges for a specific category 
{
    "status": "success",
    "message": "Challenges for category retrieved successfully",
    "data": [
        {
            "id": 9,
            "name": "Weekends",
            "winning_rules": {
                "level": 3,
                "winning_points": 100,
                "rank": 1
            },
            "reward": 2000,
            "category_id": 1,
            "createdAt": "2025-03-11T09:28:06.000Z",
            "updatedAt": "2025-08-20T13:39:04.000Z",
            "user_life": 10,
            "category": {
                "id": 1,
                "name": "Weekly Challenge",
                "icon": "Icons.calendar_view_week"
            },
            "description": null,
            "status": 1,
            "start_date": "2025-04-21T14:09:56.000Z",
            "end_date": "2025-09-27T08:04:00.000Z"
        },
        {
            "id": 7,
            "name": "Holiday",
            "winning_rules": {
                "level": 5,
                "winning_points": 500,
                "rank": 1
            },
            "reward": 500,
            "category_id": 1,
            "createdAt": "2025-03-08T08:55:24.000Z",
            "updatedAt": "2025-08-20T13:39:04.000Z",
            "user_life": 10,
            "category": {
                "id": 1,
                "name": "Weekly Challenge",
                "icon": "Icons.calendar_view_week"
            },
            "description": null,
            "status": 1,
            "start_date": "2025-04-21T14:09:56.000Z",
            "end_date": "2025-09-28T09:22:00.000Z"
        },
        {
            "id": 6,
            "name": "Trivia Battle",
            "winning_rules": {
                "level": 5,
                "winning_points": 500,
                "rank": 1
            },
            "reward": 500,
            "category_id": 1,
            "createdAt": "2025-03-08T08:11:03.000Z",
            "updatedAt": "2025-08-20T13:39:04.000Z",
            "user_life": 10,
            "category": {
                "id": 1,
                "name": "Weekly Challenge",
                "icon": "Icons.calendar_view_week"
            },
            "description": null,
            "status": 1,
            "start_date": "2025-04-21T14:09:56.000Z",
            "end_date": "2025-09-28T09:21:00.000Z"
        },
}