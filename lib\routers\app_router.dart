import 'package:achawach/core/utils/page_transitions.dart';
import 'package:achawach/screens/auth/login_screen.dart';
import 'package:achawach/screens/auth/signup_screen.dart';
import 'package:achawach/screens/home/<USER>';
import 'package:achawach/screens/profile/profile_screen.dart';
import 'package:achawach/screens/search/search_screen.dart';
import 'package:achawach/screens/challenges/joined_challenges_screen.dart';
import 'package:achawach/screens/settings/settings_screen.dart';
import 'package:achawach/screens/analytics/analytics_screen.dart';
import 'package:achawach/screens/help/help_screen.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppRouter {
  static const String initialRoute = '/';
  static const String loginRoute = '/login';
  static const String signupRoute = '/signup';
  static const String homeRoute = '/home';
  static const String searchRoute = '/search';
  static const String joinedChallengesRoute = '/joined-challenges';
  static const String profileRoute = '/profile';
  static const String settingsRoute = '/settings';
  static const String analyticsRoute = '/analytics';
  static const String helpRoute = '/help';

  // Define routes with standard widget builders
  static Map<String, Widget Function(BuildContext)> routes = {
    initialRoute: (context) => const AuthenticationWrapper(),
    loginRoute: (context) => const LoginScreen(),
    signupRoute: (context) => const SignupScreen(),
    homeRoute: (context) => const HomeScreen(),
    searchRoute: (context) => const SearchScreen(),
    joinedChallengesRoute: (context) => const JoinedChallengesScreen(),
    profileRoute: (context) => const ProfileScreen(),
    settingsRoute: (context) => const SettingsScreen(),
    analyticsRoute: (context) => const AnalyticsScreen(),
    helpRoute: (context) => const HelpScreen(),
  };

  // Generate custom route transitions
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case homeRoute:
        return PageTransitions.scaleFade(
          page: const HomeScreen(),
          duration: const Duration(milliseconds: 600),
        );
      case loginRoute:
        return PageTransitions.fadeSlideDown(
          page: const LoginScreen(),
          duration: const Duration(milliseconds: 500),
        );
      case signupRoute:
        return PageTransitions.slideRight(
          page: const SignupScreen(),
          duration: const Duration(milliseconds: 400),
        );
      case profileRoute:
        return PageTransitions.fadeSlideUp(
          page: const ProfileScreen(),
          duration: const Duration(milliseconds: 400),
        );
      case searchRoute:
        return PageTransitions.fadeSlideUp(
          page: const SearchScreen(),
          duration: const Duration(milliseconds: 400),
        );
      case joinedChallengesRoute:
        return PageTransitions.fadeSlideUp(
          page: const JoinedChallengesScreen(),
          duration: const Duration(milliseconds: 400),
        );
      case settingsRoute:
        return PageTransitions.fadeSlideUp(
          page: const SettingsScreen(),
          duration: const Duration(milliseconds: 400),
        );
      case analyticsRoute:
        return PageTransitions.fadeSlideUp(
          page: const AnalyticsScreen(),
          duration: const Duration(milliseconds: 400),
        );
      case helpRoute:
        return PageTransitions.fadeSlideUp(
          page: const HelpScreen(),
          duration: const Duration(milliseconds: 400),
        );
      default:
        return MaterialPageRoute(
          builder: (_) => const Scaffold(
            body: Center(child: Text('Route not found')),
          ),
        );
    }
  }
}

class AuthenticationWrapper extends StatefulWidget {
  const AuthenticationWrapper({super.key});

  @override
  State<AuthenticationWrapper> createState() => _AuthenticationWrapperState();
}

class _AuthenticationWrapperState extends State<AuthenticationWrapper> {
  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');

    if (mounted) {
      if (token != null) {
        Navigator.of(context).pushReplacementNamed(AppRouter.homeRoute);
      } else {
        Navigator.of(context).pushReplacementNamed(AppRouter.loginRoute);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
