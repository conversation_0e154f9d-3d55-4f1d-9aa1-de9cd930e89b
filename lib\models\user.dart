class User {
  final int id;
  final String fullName;
  final String phoneNumber;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String profileImage;
  final String avatar;

  User({
    required this.id,
    required this.fullName,
    required this.phoneNumber,
    required this.createdAt,
    required this.updatedAt,
    required this.profileImage,
    required this.avatar,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      fullName: json['fullName'],
      phoneNumber: json['phoneNumber'],
      profileImage: json['profileImage'],
      avatar: json['avatar'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullName': fullName,
      'phoneNumber': phoneNumber,
       'profileImage': profileImage, 
       'avatar': avatar,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
