import 'package:achawach/core/constants/app_colors.dart';
import 'package:flutter/material.dart';

class Category {
  final int id;
  final String name;
  final String icon;
  final String color;
  final DateTime createdAt;
  final DateTime updatedAt;

  Category({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    try {
      return Category(
        id: json['id'] ?? 0,
        name: json['name'] ?? '',
        icon: json['icon'] ?? 'Icons.category',
        color: json['color'] ?? 'AppColors.primary',
        createdAt: json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : DateTime.now(),
        updatedAt: json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : DateTime.now(),
      );
    } catch (e) {
      print('Error parsing Category: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'color': color,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Helper method to convert string icon to IconData
  IconData getIconData() {
    // Remove the "Icons." prefix
    String iconName = icon.replaceAll('Icons.', '');

    // Map icon names to IconData
    switch (iconName) {
      case 'calendar_view_week':
        return Icons.calendar_view_week;
      case 'calendar_month_rounded':
        return Icons.calendar_month_rounded;
      case 'star_rounded':
        return Icons.star_rounded;
      case 'today_rounded':
        return Icons.today_rounded;
      case 'people_alt_rounded':
        return Icons.people_alt_rounded;
      default:
        return Icons.category; // Default icon
    }
  }

  // Helper method to convert string color to Color
  Color getColor() {
    if (color.startsWith('AppColors.')) {
      String colorName = color.replaceAll('AppColors.', '');
      switch (colorName) {
        case 'primary':
          return AppColors.primary;
        case 'secondary':
          return AppColors.secondary;
        case 'accent':
          return AppColors.accent;
        default:
          return Colors.blue;
      }
    } else if (color.contains('shade')) {
      // Handle color shades like 'Colors.purple.shade400'
      List<String> parts = color.split('.');
      if (parts.length >= 3) {
        String colorName = parts[1];
        int shade = int.tryParse(parts[2].replaceAll('shade', '')) ?? 500;

        switch (colorName) {
          case 'purple':
            return Colors.purple[shade]!;
          case 'blue':
            return Colors.blue[shade]!;
          default:
            return Colors.blue;
        }
      }
    }
    return Colors.blue; // Default color
  }
}
