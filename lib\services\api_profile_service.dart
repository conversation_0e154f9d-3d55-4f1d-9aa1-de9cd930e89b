// services/api_service.dart
import 'dart:io';
import 'package:achawach/models/questions_category.dart';
import 'package:achawach/services/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
// Assuming you have this model

class ApiProfileService {
  final Dio _dio = DioClient.instance;

  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  Future<void> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
  }

  // --- Existing getProfile (assuming it works) ---
  Future<Profile> getProfile() async {
    try {
      final response = await _dio.get('/profile');

      if (response.statusCode == 200 && response.data['status'] == 'success') {
        return Profile.fromJson(response.data['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Failed to load profile');
      }
    } on DioException catch (e) {
      // More specific error handling based on DioException
      if (e.response != null) {
        print(
            'Error fetching profile: ${e.response?.statusCode} ${e.response?.data}');
        throw Exception(e.response?.data['message'] ??
            'Failed to load profile (server error)');
      } else {
        print('Error fetching profile: ${e.message}');
        throw Exception('Failed to load profile (network error)');
      }
    } catch (e) {
      print('Error fetching profile: $e');
      throw Exception('An unexpected error occurred');
    }
  }

  // --- NEW: Get All Categories ---
  Future<List<QuestionsCategory>> getAllCategories() async {
    try {
      final response = await _dio.get('/settings/questions/categories');
      if (response.statusCode == 200 && response.data['status'] == 'success') {
        List<dynamic> categoryList = response.data['data'];
        return categoryList
            .map((json) => QuestionsCategory.fromJson(json))
            .toList();
      } else {
        throw Exception(
            response.data['message'] ?? 'Failed to load categories');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception(e.response?.data['message'] ??
            'Failed to load categories (server error: ${e.response?.statusCode})');
      } else {
        throw Exception(
            'Failed to load categories (network error: ${e.message})');
      }
    } catch (e) {
      throw Exception(
          'An unexpected error occurred while loading categories: $e');
    }
  }

  // --- NEW: updateProfile ---
  Future<Profile> updateProfile({
    required String fullName,
    File? profileImageFile, // Make File optional
    String? avatar, // Make avatar optional
    required bool useProfileImage, // Flag to decide
    // String? categories, // Add if needed by your API
    required List<int> categoryIds,
  }) async {
    try {
      String? token = await _getToken();
      if (token == null) {
        throw Exception("Not authenticated");
      }

      Map<String, dynamic> dataMap = {
        'fullName': fullName,
        'isProfileImageAvailable': useProfileImage ? 1 : 0,
        'categories': categoryIds,
        // 'categories': categories, // Add if you have this field
      };

      // Add avatar *only* if not using profile image
      if (!useProfileImage && avatar != null) {
        dataMap['avatar'] = avatar;
      }

      // Add profile image *only* if using it and file exists
      if (useProfileImage && profileImageFile != null) {
        String fileName = profileImageFile.path.split('/').last;
        String extension = fileName.split('.').last.toLowerCase();
        String contentType = 'image/jpeg'; // Default

        if (extension == 'png') {
          contentType = 'image/png';
        } else if (extension == 'jpg' || extension == 'jpeg') {
          contentType = 'image/jpeg';
        }
        dataMap['profileImage'] = await MultipartFile.fromFile(
          profileImageFile.path,
          filename: fileName,
          contentType: DioMediaType.parse(contentType),
        );
      } else if (useProfileImage && profileImageFile == null) {
        // Handle case where user *wants* image but didn't provide a *new* one
        // You might need a different API flag for "keep existing image"
        // For now, we assume if useProfileImage is true, a file *must* be sent,
        // or the API handles isProfileImageAvailable=1 without a file correctly.
        // If API requires *removing* image, send isProfileImageAvailable=0 and maybe avatar.
        print("Warning: useProfileImage is true, but no image file provided.");
        // If the intention is to *remove* the image, set useProfileImage to false earlier.
      }

      FormData formData = FormData.fromMap(dataMap);

      final response = await _dio.put(
        '/profile',
        data: formData,
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          contentType: 'multipart/form-data', // Important for file uploads
        ),
      );

      if (response.statusCode == 200 && response.data['status'] == 'success') {
        if (response.data['data'] != null &&
            response.data['data']['user'] != null) {
          return Profile.fromJson(response.data['data']['user']);
        } else {
          throw Exception("Invalid response format after update");
        }
      } else {
        throw Exception(response.data['message'] ?? 'Failed to update profile');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        print(
            'Error updating profile: ${e.response?.statusCode} ${e.response?.data}');
        throw Exception(e.response?.data['message'] ??
            'Failed to update profile (server error)');
      } else {
        print('Error updating profile: ${e.message}');
        throw Exception('Failed to update profile (network error)');
      }
    } catch (e) {
      print('Error updating profile: $e');
      throw Exception('An unexpected error occurred during update');
    }
  }
}

class Profile {
  final int id;
  final String fullName;
  final String phoneNumber;
  final bool isProfileImageAvailable; // Use bool for clarity
  final String? avatar; // Nullable
  final String? profileImagePath; // Nullable
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int? lives; // Added from PUT response
  final List<QuestionsCategory> categories;
  final double? totalEarned;

  Profile({
    required this.id,
    required this.fullName,
    required this.phoneNumber,
    required this.isProfileImageAvailable,
    this.avatar,
    this.profileImagePath,
    required this.createdAt,
    this.updatedAt,
    this.lives,
    required this.categories,
    this.totalEarned,
  });

  factory Profile.fromJson(Map<String, dynamic> json) {
    final data = json.containsKey('user') ? json['user'] : json;

    // Parse categories list
    List<QuestionsCategory> parsedCategories = [];
    if (data['categories'] != null && data['categories'] is List) {
      parsedCategories = (data['categories'] as List)
          .map((categoryJson) => QuestionsCategory.fromJson(categoryJson))
          .toList();
    }

    return Profile(
      id: data['id'],
      fullName: data['fullName'],
      phoneNumber: data['phoneNumber'],
      isProfileImageAvailable: data['isProfileImageAvailable'] == 1 ||
          data['isProfileImageAvailable'] == true,
      avatar: data['avatar'],
      profileImagePath: data['profileImagePath'] ?? data['profile_image_path'],
      createdAt: DateTime.parse(data['createdAt']),
      updatedAt:
          data['updatedAt'] != null ? DateTime.parse(data['updatedAt']) : null,
      lives: data['lives'],
      totalEarned: double.parse(data['totalEarned'] ?? '0.0'),
      categories: parsedCategories,
    );
  }
}
