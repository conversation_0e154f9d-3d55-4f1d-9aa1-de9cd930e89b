import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Simple widget for testing search screen functionality
class SimpleSearchWidget extends StatelessWidget {
  const SimpleSearchWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              // Search header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: const TextField(
                          decoration: InputDecoration(
                            hintText: 'Search challenges...',
                            prefixIcon: Icon(Icons.search, color: Colors.grey),
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: const Icon(Icons.filter_list),
                      onPressed: () {},
                    ),
                  ],
                ),
              ),
              
              // Filters section
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Minimum Reward'),
                    const SizedBox(height: 8),
                    Slider(
                      value: 0,
                      min: 0,
                      max: 1000,
                      divisions: 20,
                      label: '\$0',
                      onChanged: (value) {},
                    ),
                  ],
                ),
              ),
              
              // Search results
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.search_off, size: 48, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      const Text('No challenges found'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

void main() {
  group('SearchScreen Tests', () {
    testWidgets('SearchScreen renders correctly', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleSearchWidget());
      
      // Verify search field is displayed
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('Search challenges...'), findsOneWidget);
      
      // Verify filter button is displayed
      expect(find.byIcon(Icons.filter_list), findsOneWidget);
      
      // Verify filter section is displayed
      expect(find.text('Minimum Reward'), findsOneWidget);
      expect(find.byType(Slider), findsOneWidget);
      
      // Verify empty state is displayed
      expect(find.text('No challenges found'), findsOneWidget);
    });

    testWidgets('SearchScreen shows search icon', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleSearchWidget());
      
      // Verify search icon is displayed
      expect(find.byIcon(Icons.search), findsOneWidget);
    });

    testWidgets('SearchScreen shows empty state icon', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleSearchWidget());
      
      // Verify empty state icon is displayed
      expect(find.byIcon(Icons.search_off), findsOneWidget);
    });
  });
}
