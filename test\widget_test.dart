// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:achawach/main.dart';
import 'package:achawach/providers/challenge_provider.dart';
import 'package:achawach/providers/language_provider.dart';
import 'package:achawach/providers/settings_provider.dart';
import 'package:achawach/providers/terms_provider.dart';

void main() {
  testWidgets('App initializes correctly', (WidgetTester tester) async {
    // Build our app with all required providers
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => ChallengeProvider()),
          ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ChangeNotifierProvider(create: (_) => SettingsProvider()),
          ChangeNotifierProvider(create: (_) => TermsProvider()),
        ],
        child: const MyApp(initialRoute: '/login'),
      ),
    );

    // This is a basic smoke test to ensure the app initializes without crashing
    // Just verify that we don't get any errors
    expect(true, isTrue);
  });
}
