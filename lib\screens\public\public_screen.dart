import 'package:flutter/material.dart';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/services/api_service.dart';
import 'package:achawach/routers/app_router.dart';
import 'package:achawach/screens/public/public_category_challenges_screen.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class PublicScreen extends StatefulWidget {
  const PublicScreen({super.key});

  @override
  State<PublicScreen> createState() => _PublicScreenState();
}

class _PublicScreenState extends State<PublicScreen>
    with SingleTickerProviderStateMixin {
  final ApiService _apiService = ApiService();

  List<Map<String, dynamic>> _topChallenges = [];
  List<Map<String, dynamic>> _categories = [];

  bool _isLoadingTopChallenges = true;
  bool _isLoadingCategories = true;

  String? _topChallengesError;
  String? _categoriesError;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadData();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await Future.wait([
      _loadTopChallenges(),
      _loadCategories(),
    ]);
  }

  Future<void> _loadTopChallenges() async {
    try {
      setState(() {
        _isLoadingTopChallenges = true;
        _topChallengesError = null;
      });

      final topChallenges = await _apiService.getPublicTopChallenges();
      if (mounted) {
        setState(() {
          _topChallenges = topChallenges;
          _isLoadingTopChallenges = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _topChallengesError = e.toString();
          _isLoadingTopChallenges = false;
        });
      }
    }
  }

  Future<void> _loadCategories() async {
    try {
      setState(() {
        _isLoadingCategories = true;
        _categoriesError = null;
      });

      final categories = await _apiService.getPublicCategories();
      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoadingCategories = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _categoriesError = e.toString();
          _isLoadingCategories = false;
        });
      }
    }
  }

  void _navigateToCategoryScreen(Map<String, dynamic> category) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            PublicCategoryChallengesScreen(category: category),
      ),
    );
  }

  void _onChallengePressed() {
    // Redirect to login screen when user tries to play a challenge
    Navigator.of(context).pushNamed(AppRouter.loginRoute);
  }

  void _onSignInPressed() {
    Navigator.of(context).pushNamed(AppRouter.loginRoute);
  }

  void _onSignUpPressed() {
    Navigator.of(context).pushNamed(AppRouter.signupRoute);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            _buildTopChallengesSection(),
            _buildCategoriesSection(),
            const SliverToBoxAdapter(child: SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 160,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      automaticallyImplyLeading: false, // Remove back button
      title: Text(
        'Achawach',
        style: GoogleFonts.poppins(
          fontWeight: FontWeight.bold,
          color: AppColors.white,
          fontSize: 18,
        ),
      ),
      centerTitle: false,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [AppColors.primary, AppColors.secondary],
            ),
          ),
          child: Stack(
            children: [
              // Decorative circles
              Positioned(
                top: -50,
                right: -50,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.white.withAlpha(26),
                  ),
                ),
              ),
              Positioned(
                bottom: -30,
                left: -30,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.white.withAlpha(13),
                  ),
                ),
              ),
              // Content - only show when expanded
              Positioned(
                bottom: 40,
                left: 0,
                right: 0,
                child: Column(
                  children: [
                    Text(
                      'Welcome to Achawach',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: AppColors.white.withAlpha(204),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Play Smart Win Big!',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: AppColors.white.withAlpha(153),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _onSignInPressed,
          style: TextButton.styleFrom(
            foregroundColor: AppColors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          child: Text(
            'Sign In',
            style: GoogleFonts.poppins(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
              fontSize: 13,
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
          child: ElevatedButton(
            onPressed: _onSignUpPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.white,
              foregroundColor: AppColors.primary,
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: Text(
              'Sign Up',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 13,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTopChallengesSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Top Challenges',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.text,
              ),
            ),
            const SizedBox(height: 16),
            if (_isLoadingTopChallenges)
              const Center(child: CircularProgressIndicator())
            else if (_topChallengesError != null)
              _buildErrorWidget(_topChallengesError!, _loadTopChallenges)
            else
              _buildTopChallengesCarousel(),
          ],
        ),
      ),
    );
  }

  Widget _buildTopChallengesCarousel() {
    if (_topChallenges.isEmpty) {
      return Container(
        height: 200,
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: AppColors.grey100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.grey200),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.quiz_outlined,
              size: 48,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 12),
            Text(
              'No top challenges available',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Check back later for exciting challenges!',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppColors.grey500,
              ),
            ),
          ],
        ),
      );
    }

    return SizedBox(
      height: 220,
      child: PageView.builder(
        controller: PageController(viewportFraction: 0.9),
        itemCount: _topChallenges.length,
        itemBuilder: (context, index) {
          final challenge = _topChallenges[index];
          return TweenAnimationBuilder<double>(
            duration: Duration(milliseconds: 400 + (index * 100)),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(0, 20 * (1 - value)),
                child: Opacity(
                  opacity: value,
                  child: _buildChallengeCard(challenge),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildChallengeCard(Map<String, dynamic> challenge) {
    final winningRules = challenge['winning_rules'] as Map<String, dynamic>?;
    final endDate = challenge['end_date'] != null
        ? DateTime.parse(challenge['end_date'])
        : null;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primary, AppColors.secondary],
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withAlpha(51),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: _onChallengePressed,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        challenge['name'] ?? 'Unknown Challenge',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.white.withAlpha(51),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${NumberFormat("#,##0.00", "en_US").format(challenge['reward'] ?? 0)} ETB',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                if (winningRules != null) ...[
                  Row(
                    children: [
                      Icon(Icons.emoji_events,
                          color: AppColors.white, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'Level ${winningRules['level']} • ${winningRules['winning_points']} pts',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: AppColors.white.withAlpha(204),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                ],
                if (endDate != null) ...[
                  Row(
                    children: [
                      Icon(Icons.schedule, color: AppColors.white, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'Ends ${DateFormat('MMM dd').format(endDate)}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: AppColors.white.withAlpha(204),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Categories',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.text,
              ),
            ),
            const SizedBox(height: 16),
            if (_isLoadingCategories)
              const Center(child: CircularProgressIndicator())
            else if (_categoriesError != null)
              _buildErrorWidget(_categoriesError!, _loadCategories)
            else
              _buildCategoriesGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesGrid() {
    if (_categories.isEmpty) {
      return Container(
        height: 200,
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: AppColors.grey100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.grey200),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.category_outlined,
              size: 48,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 12),
            Text(
              'No categories available',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Categories will appear here soon!',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppColors.grey500,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _categories.length,
      itemBuilder: (context, index) {
        final category = _categories[index];
        return _buildCategoryCard(category);
      },
    );
  }

  Widget _buildCategoryCard(Map<String, dynamic> category) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Opacity(
            opacity: value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: AppColors.white,
                border: Border.all(
                  color: AppColors.grey200,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withAlpha(13),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () => _navigateToCategoryScreen(category),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.primary.withAlpha(26),
                                  AppColors.secondary.withAlpha(26),
                                ],
                              ),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              _getIconFromString(category['icon']),
                              size: 28,
                              color: AppColors.primary,
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Flexible(
                          child: Text(
                            category['name'] ?? 'Unknown Category',
                            textAlign: TextAlign.center,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: AppColors.text,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 3),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withAlpha(26),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            'Explore',
                            style: GoogleFonts.poppins(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: AppColors.primary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorWidget(String error, VoidCallback onRetry) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.error.withAlpha(26),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(Icons.error_outline, color: AppColors.error, size: 32),
          const SizedBox(height: 8),
          Text(
            'Something went wrong',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.error,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            error.replaceAll('Exception: ', ''),
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: onRetry,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.white,
            ),
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }

  IconData _getIconFromString(String? iconName) {
    if (iconName == null) return Icons.category;

    switch (iconName) {
      case 'Icons.people_alt_rounded':
        return Icons.people_alt_rounded;
      case 'Icons.today_rounded':
        return Icons.today_rounded;
      case 'Icons.star_rounded':
        return Icons.star_rounded;
      case 'Icons.calendar_month_rounded':
        return Icons.calendar_month_rounded;
      case 'Icons.calendar_view_week':
        return Icons.calendar_view_week;
      case 'Icons.science':
        return Icons.science;
      case 'Icons.history':
        return Icons.history;
      case 'Icons.sports_soccer':
        return Icons.sports_soccer;
      case 'Icons.computer':
        return Icons.computer;
      case 'Icons.palette':
        return Icons.palette;
      case 'Icons.public':
        return Icons.public;
      case 'Icons.music_note':
        return Icons.music_note;
      case 'Icons.movie':
        return Icons.movie;
      case 'Icons.restaurant':
        return Icons.restaurant;
      default:
        return Icons.category;
    }
  }
}
