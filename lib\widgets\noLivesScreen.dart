import 'package:achawach/core/constants/app_colors.dart';

import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:flutter/scheduler.dart';

class NoLivesScreen extends StatefulWidget {
  final Function onBuyLives;
  final int? categoryId;
  final bool isStarted;

  const NoLivesScreen(
      {super.key,
      required this.onBuyLives,
      required this.isStarted,
      this.categoryId});

  @override
  NoLivesScreenState createState() => NoLivesScreenState();
}

class NoLivesScreenState extends State<NoLivesScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;

  // Flag to track if we've already navigated
  bool _hasNavigated = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.elasticOut),
      ),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 1.0, curve: Curves.easeInOut),
      ),
    );

    // Schedule navigation for special challenges after the first frame
    if (widget.categoryId == null) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _navigateToHomeIfNeeded();
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // Handle navigation to home screen for special challenges
  void _navigateToHomeIfNeeded() {
    if (!mounted || _hasNavigated) return;
    _hasNavigated = true;
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        // Prevent going back to the game
        // Instead, navigate to home screen if categoryId is not provided
        if (widget.categoryId == null) {
          Navigator.of(context).popUntil((route) => route.isFirst);
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Empty heart animation
              AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: ShaderMask(
                      shaderCallback: (Rect bounds) {
                        return const LinearGradient(
                          colors: [AppColors.primary, AppColors.accent],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ).createShader(bounds);
                      },
                      child: const Icon(
                        Icons.favorite_border,
                        size: 120,
                        color: Colors.white,
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 40),
              // Message text
              TweenAnimationBuilder(
                tween: Tween<double>(begin: 0, end: 1),
                duration: const Duration(milliseconds: 800),
                builder: (context, double value, child) {
                  return Opacity(
                    opacity: value,
                    child: Transform.translate(
                      offset: Offset(0, 20 * (1 - value)),
                      child: child,
                    ),
                  );
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withAlpha(51),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: const Column(
                    children: [
                      Text(
                        "Out of Lives!",
                        style: TextStyle(
                          color: AppColors.text,
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        "Wait for lives to regenerate or buy more to continue playing.",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Color(
                              0xB3000000), // AppColors.text with 70% opacity
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 40),
              // Buy lives button with animation
              AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: ElevatedButton(
                      onPressed: () => widget.onBuyLives(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.secondary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                        elevation: 5,
                        shadowColor: AppColors.secondary.withAlpha(128),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: const [
                          Icon(Icons.add_circle_outline, size: 24),
                          SizedBox(width: 8),
                          Text(
                            "Buy Lives",
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 30),
              // Back to category button
              if (widget.categoryId != null)
                AnimatedBuilder(
                  animation: _controller,
                  builder: (context, child) {
                    return ElevatedButton(
                      onPressed: () {
                        // Navigate back to category details screen
                        // First pop the current screen to avoid stacking
                        if (widget.isStarted) {
                          Navigator.of(context).pop();
                          Navigator.of(context).pop();
                        } else {
                          Navigator.of(context).pop();
                        }

                        // Navigator.of(context).pop();
                        // Then navigate to the category details screen
                        // Navigator.of(context).push(
                        //   MaterialPageRoute(
                        //     builder: (context) => CategoryDetails(
                        //       selectedChallengeCategories: {
                        //         'id': widget.categoryId,
                        //         'name':
                        //             '', // The name will be loaded in the CategoryDetails screen
                        //       },
                        //     ),
                        //   ),
                        // );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                        elevation: 5,
                        shadowColor: AppColors.primary.withAlpha(128),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: const [
                          Icon(Icons.category_outlined, size: 24),
                          SizedBox(width: 8),
                          Text(
                            "Back to Categories",
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              const SizedBox(height: 40),
              // Floating hearts animation
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: _FloatingHeartsAnimation(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _FloatingHeartsAnimation extends StatefulWidget {
  @override
  _FloatingHeartsAnimationState createState() =>
      _FloatingHeartsAnimationState();
}

class _FloatingHeartsAnimationState extends State<_FloatingHeartsAnimation>
    with TickerProviderStateMixin {
  final List<HeartParticle> _hearts = [];
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 6),
    )..repeat();

    // Create some hearts
    for (int i = 0; i < 8; i++) {
      _hearts.add(HeartParticle(
        position: Offset(
            math.Random().nextDouble() * 300, math.Random().nextDouble() * 100),
        size: math.Random().nextDouble() * 20 + 10,
        speed: math.Random().nextDouble() * 2 + 1,
        opacity: math.Random().nextDouble() * 0.6 + 0.2,
        isOutlined: math.Random().nextBool(),
      ));
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, _) {
        return CustomPaint(
          painter: HeartsPainter(
            hearts: _hearts,
            progress: _animationController.value,
            primaryColor: AppColors.primary,
            accentColor: AppColors.accent,
          ),
          child: const SizedBox(
            height: 100,
            width: double.infinity,
          ),
        );
      },
    );
  }
}

class HeartParticle {
  Offset position;
  double size;
  double speed;
  double opacity;
  bool isOutlined;

  HeartParticle({
    required this.position,
    required this.size,
    required this.speed,
    required this.opacity,
    required this.isOutlined,
  });
}

class HeartsPainter extends CustomPainter {
  final List<HeartParticle> hearts;
  final double progress;
  final Color primaryColor;
  final Color accentColor;

  HeartsPainter({
    required this.hearts,
    required this.progress,
    required this.primaryColor,
    required this.accentColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (var heart in hearts) {
      // Update position based on progress and speed
      final yOffset = heart.speed * progress * size.height;
      final effectivePosition = heart.position.translate(
        math.sin(progress * 2 * math.pi + heart.position.dx) * 10,
        -yOffset % (size.height + 50) - 50,
      );

      // Draw the heart
      final paint = Paint()
        ..style = heart.isOutlined ? PaintingStyle.stroke : PaintingStyle.fill
        ..color = (heart.isOutlined ? primaryColor : accentColor).withAlpha(
            (heart.opacity * (1 - (progress * 0.8) % 1) * 255).toInt());

      if (heart.isOutlined) {
        paint.strokeWidth = 2;
      }

      final path = Path();
      final heartSize = heart.size;
      final centerX = effectivePosition.dx;
      final centerY = effectivePosition.dy;

      path.moveTo(centerX, centerY + heartSize / 4);
      path.cubicTo(
        centerX - heartSize / 2,
        centerY - heartSize / 2,
        centerX - heartSize,
        centerY + heartSize / 3,
        centerX,
        centerY + heartSize,
      );
      path.cubicTo(
        centerX + heartSize,
        centerY + heartSize / 3,
        centerX + heartSize / 2,
        centerY - heartSize / 2,
        centerX,
        centerY + heartSize / 4,
      );

      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
