import 'package:achawach/services/level_service.dart';
import 'package:achawach/services/service_locator.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:video_player/video_player.dart';
import 'package:confetti/confetti.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/core/utils/theme_utils.dart';
import 'package:achawach/models/question.dart';
import 'package:achawach/providers/challenge_provider.dart';
import 'package:achawach/providers/settings_provider.dart';
import 'package:achawach/widgets/questionsContent.dart';
import 'package:achawach/widgets/score_life_level.dart';
import 'package:achawach/widgets/level_up_screen.dart';
import 'package:achawach/widgets/noLivesScreen.dart';
import 'package:achawach/widgets/full_page_loader.dart';

import 'dart:async';
import 'package:achawach/services/media_service.dart';

class SpecialChallengeStartScreen extends StatefulWidget {
  final Map<String, dynamic> selectedChallenge;

  const SpecialChallengeStartScreen(
      {super.key, required this.selectedChallenge});

  @override
  State<SpecialChallengeStartScreen> createState() =>
      _SpecialChallengeStartScreenState();
}

class _SpecialChallengeStartScreenState
    extends State<SpecialChallengeStartScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late ConfettiController _confettiController;
  int currentQuestionIndex = 0;
  num score = 0;
  bool isChallengeCompleted = false;
  Timer? _timer;
  String? selectedAnswer;
  final AudioPlayer _soundPlayer = AudioPlayer();
  final AudioPlayer _questionAudioPlayer = AudioPlayer();
  VideoPlayerController? _videoController;
  late bool _soundEnabled;
  bool _isLoading = true;
  String? _error;
  late MediaService _mediaService;
  List<Question> _questions = [];
  Map<String, dynamic>? _summary;
  Map<String, dynamic>? _life;
  int _remainingTime = 0;
  bool _isAnswerSubmitting = false;
  final int _categoryId = 0;
  bool _isLevelUpPending = false;
  int _levelUpBonusPoints = 0;
  int _oldLevel = 0;
  int currentScore = 0;
  late ServiceLocator _serviceLocator;
  late LevelService _levelService;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _confettiController =
        ConfettiController(duration: const Duration(seconds: 2));
    _serviceLocator = ServiceLocator();
    _serviceLocator.initializeServices(
      context: context,
      challengeId: widget.selectedChallenge['id'],
      categoryId: 0,
    );
    _levelService = _serviceLocator.levelService;
    // Get sound setting from SettingsProvider
    _soundEnabled =
        Provider.of<SettingsProvider>(context, listen: false).isSoundEnabled;
    _mediaService = _serviceLocator.mediaService;
    _loadQuestions();
    _initializeChallenge(); // Initialize challenge data
    _animationController.forward();
    // _initializeAudio();
  }

  Future<void> _initializeChallenge() async {
    try {
      // Check if this is a new challenge (status = 0 or 'New') or an existing one (status = 1 or 'Started')
      final dynamic status = widget.selectedChallenge['status'];
      final bool isNewChallenge = status == 0 ||
          status == 'New' ||
          status?.toString().toLowerCase() == 'new';

      if (isNewChallenge) {
        // For new challenges, we need to ensure the life data is initialized
        // by the backend before proceeding
        await Future.delayed(const Duration(milliseconds: 1500));

        // For new challenges, we need to make sure the challenge is started on the server
        try {
          if (mounted) {
            final challengeProvider =
                Provider.of<ChallengeProvider>(context, listen: false);
            await challengeProvider.startChallenge(
                widget.selectedChallenge['id'], 0);
          }
        } catch (e) {
          // Continue anyway, as the challenge might already be started
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error initializing challenge: $e')),
        );
      }
    }
  }

  Future<void> _loadQuestions() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final challengeProvider =
          Provider.of<ChallengeProvider>(context, listen: false);
      await challengeProvider
          .fetchChallengeQuestions(widget.selectedChallenge['id']);

      setState(() {
        _questions = challengeProvider.questions;
        _summary = challengeProvider.questionsSummary;
        _isLoading = false;

        if (_questions.isNotEmpty) {
          _remainingTime = _questions[currentQuestionIndex].time;
          _initializeMediaForCurrentQuestion();
          _startTimer();
        } else {
          _error = 'No questions available for this challenge';
        }
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _initializeMediaForCurrentQuestion() async {
    final question = _questions[currentQuestionIndex];

    // Cleanup previous media
    await _videoController?.dispose();
    _videoController = null;
    await _questionAudioPlayer.stop();

    if (question.type.toLowerCase() == 'video' &&
        question.filePath.isNotEmpty) {
      try {
        _videoController =
            VideoPlayerController.networkUrl(Uri.parse(question.filePath))
              ..initialize().then((_) {
                setState(() {});
              }).catchError((error) {
                print('Error initializing video: $error');
              });
      } catch (e) {
        print('Error creating video controller: $e');
      }
    } else if (question.type.toLowerCase() == 'audio' &&
        question.filePath.isNotEmpty) {
      try {
        await _questionAudioPlayer.setSource(UrlSource(question.filePath));
      } catch (e) {
        print('Error setting audio source: $e');
      }
    }
  }

  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime > 0) {
        setState(() {
          _remainingTime--;
        });
      } else {
        _timer?.cancel();
        _onTimeout();
      }
    });
  }

  void _onTimeout() async {
    final question = _questions[currentQuestionIndex];

    // Check if this is the last life before decreasing
    final bool isLastLife = _life != null && (_life!['life'] ?? 0) <= 1;

    // Call the API to decrease life when timeout occurs
    try {
      if (mounted) {
        final challengeProvider =
            Provider.of<ChallengeProvider>(context, listen: false);
        await challengeProvider.decreaseChallengeLife(
            widget.selectedChallenge['id'], question.id);

        // We don't need to update _life here as it will be updated by the socket
        // The socket will receive the updated life data from the server
      }
    } catch (e) {
      print('Error decreasing life: $e');
    }

    if (isLastLife && mounted) {
      // The life will be updated via the ScoreLifeLevel widget's onLifeChange callback
      // which will trigger _showGameOverDialog() when life reaches 0
      // We don't need to move to the next question as the game is over
    } else {
      _moveToNextQuestion(
          false); // Pass false to indicate no life reduction needed
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;
          // Navigate to the home screen when back button is pressed
          Navigator.of(context).pushReplacementNamed('/home');
          // No need to return anything with onPopInvoked
        },
        child: const GameLoader(),
      );
    }

    if (_error != null) {
      return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;
          // Navigate to the home screen when back button is pressed
          Navigator.of(context).pushReplacementNamed('/home');
          // No need to return anything with onPopInvoked
        },
        child: Scaffold(
          backgroundColor: AppColors.background,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.home),
              onPressed: () =>
                  Navigator.of(context).pushReplacementNamed('/home'),
            ),
          ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    'Error: $_error',
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: _loadQuestions,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 32, vertical: 16),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (_questions.isEmpty) {
      return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;
          // Navigate to the home screen when back button is pressed
          Navigator.of(context).pushReplacementNamed('/home');
          // No need to return anything with onPopInvoked
        },
        child: Scaffold(
          backgroundColor: AppColors.background,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.home),
              onPressed: () =>
                  Navigator.of(context).pushReplacementNamed('/home'),
            ),
          ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.quiz_outlined, size: 64),
                const SizedBox(height: 16),
                Text(
                  'No questions available',
                  style: GoogleFonts.rubik(fontSize: 20),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (isChallengeCompleted) {
      return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;
          // Navigate to the home screen when back button is pressed
          Navigator.of(context).pushReplacementNamed('/home');
          // No need to return anything with onPopInvoked
        },
        child: Scaffold(
          backgroundColor: AppColors.background,
          body: Stack(
            children: [
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ConfettiWidget(
                      confettiController: _confettiController,
                      blastDirection: -3.14 / 2,
                      emissionFrequency: 0.05,
                      numberOfParticles: 20,
                      gravity: 0.05,
                      shouldLoop: false,
                      colors: const [
                        Colors.green,
                        Colors.blue,
                        Colors.pink,
                        Colors.orange,
                        Colors.purple
                      ],
                    ),
                    const Icon(Icons.emoji_events,
                        size: 80, color: Colors.amber),
                    const SizedBox(height: 24),
                    Text(
                      'Challenge Completed!',
                      style: GoogleFonts.rubik(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Text(
                            'Your Score',
                            style: GoogleFonts.rubik(
                              fontSize: 20,
                              color: Colors.grey[700],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            score.toString(),
                            style: GoogleFonts.rubik(
                              fontSize: 48,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 32),
                    ElevatedButton.icon(
                      onPressed: () =>
                          Navigator.of(context).pushReplacementNamed('/home'),
                      icon: const Icon(Icons.home),
                      label: const Text('Back to Home'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 32, vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    final currentQuestion = _questions[currentQuestionIndex];
    // Get current question

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        // Navigate to the home screen when back button is pressed
        Navigator.of(context).pushReplacementNamed('/home');
        // No need to return anything with onPopInvoked
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: SafeArea(
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.of(context)
                              .pushReplacementNamed('/home'),
                        ),
                        Expanded(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                ScoreLifeLevel(
                                  challengeId: widget.selectedChallenge['id'],
                                  onLevelUp: (oldLevel, newLevel, bonusPoints,
                                      score) async {
                                    // Handle level up event
                                    print('this is the level up score: $score');

                                    setState(() {
                                      _isLevelUpPending = true;
                                      _levelUpBonusPoints = bonusPoints;
                                      _oldLevel = oldLevel;
                                      currentScore = score;
                                    });
                                    _confettiController.play();

                                    await _mediaService.playlevelSound();

                                    // Immediately show the level up screen
                                    // This ensures it appears right after the level change is detected
                                    _showLevelUpScreen(newLevel);
                                  },
                                  onLifeChange: (lifeData) {
                                    // Update life data from socket
                                    setState(() {
                                      _life = lifeData;

                                      // Check if user has no lives left and show game over dialog
                                      if ((_life?['life'] ?? 0) <= 0) {
                                        _showGameOverDialog();
                                      }
                                    });
                                  },
                                  mediaService: _mediaService,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Display challenge dates if available
                    _buildChallengeDatesCard(),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.white,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: LinearProgressIndicator(
                          value: _remainingTime / currentQuestion.time,
                          backgroundColor: Colors.grey[200],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _remainingTime <= 5
                                ? Colors.red
                                : AppColors.primary,
                          ),
                          minHeight: 10,
                        ),
                      ),
                    ),
                    Text(
                      'Time: $_remainingTime seconds',
                      style: GoogleFonts.rubik(fontSize: 16),
                    ),
                    const SizedBox(height: 24),
                    Card(
                      elevation: 5,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Container(
                        width: double.infinity,
                        constraints: BoxConstraints(
                          maxHeight: MediaQuery.of(context).size.height * 0.4,
                        ),
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: QuestionsContent(question: currentQuestion),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Expanded(
                      child: ListView.builder(
                        itemCount: currentQuestion.choices.length,
                        itemBuilder: (context, index) {
                          final choice = currentQuestion.choices[index];
                          final isSelected = selectedAnswer == choice.choice;

                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: AnimatedOpacity(
                              opacity: _isAnswerSubmitting ? 0.5 : 1.0,
                              duration: const Duration(milliseconds: 200),
                              child: ElevatedButton(
                                onPressed: _isAnswerSubmitting
                                    ? null
                                    : () => _submitAnswer(choice.id),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: isSelected
                                      ? AppColors.primary
                                      : Colors.white,
                                  padding: const EdgeInsets.all(16),
                                  elevation: isSelected ? 8 : 2,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 32,
                                      height: 32,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: isSelected
                                            ? Colors.white
                                            : AppColors.primary
                                                .withValues(alpha: 0.1),
                                      ),
                                      child: Center(
                                        child: Text(
                                          String.fromCharCode(65 + index),
                                          style: GoogleFonts.rubik(
                                            color: isSelected
                                                ? AppColors.primary
                                                : Colors.black87,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Text(
                                        choice.choice,
                                        style: GoogleFonts.rubik(
                                          color: isSelected
                                              ? Colors.white
                                              : Colors.black87,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              ConfettiWidget(
                confettiController: _confettiController,
                blastDirectionality: BlastDirectionality.explosive,
                particleDrag: 0.05,
                emissionFrequency: 0.05,
                numberOfParticles: 20,
                gravity: 0.05,
                shouldLoop: false,
                colors: const [
                  Colors.green,
                  Colors.blue,
                  Colors.pink,
                  Colors.orange,
                  Colors.purple
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();
    _confettiController.dispose();
    _soundPlayer.dispose();
    _questionAudioPlayer.dispose();
    _videoController?.dispose();

    // Cancel stream subscriptions
    // _gameEndedSubscription?.cancel();
    // _winnerAnnouncementSubscription?.cancel();

    super.dispose();
  }

  // Show no lives screen when user has no lives left
  void _showGameOverDialog() {
    // Prevent showing multiple screens
    if (ModalRoute.of(context)?.isCurrent != true) return;

    // Navigate to NoLivesScreen with null categoryId to indicate it's a special challenge
    _mediaService.playLost();
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => NoLivesScreen(
                  onBuyLives: () => {},
                  categoryId: null,
                  isStarted: true, // null indicates it's a special challenge
                )));
  }

  void stopTimer() {
    _timer?.cancel();
  }

  Future<void> _submitAnswer(int choiceId) async {
    if (_isAnswerSubmitting) return;

    // Check if user has lives left
    if (_life != null && (_life!['life'] ?? 0) <= 0) {
      _showGameOverDialog();
      return;
    }

    setState(() {
      _isAnswerSubmitting = true;
      selectedAnswer = _questions[currentQuestionIndex]
          .choices
          .firstWhere((choice) => choice.id == choiceId)
          .choice;
    });

    try {
      final challengeProvider =
          Provider.of<ChallengeProvider>(context, listen: false);
      final result = await challengeProvider.submitAnswer(
          widget.selectedChallenge['id'],
          _questions[currentQuestionIndex].id,
          choiceId,
          _categoryId);

      if (!mounted) return;
      stopTimer();
      // Play sound based on result
      if (_soundEnabled) {
        if (result['isCorrect']) {
          await _mediaService.playCorrectSound();

          // Just move to the next question
          _moveToNextQuestion(false);
        } else {
          // try {
          //   await _soundPlayer.play(AssetSource('sounds/wrong.wav'));
          // } catch (e) {
          //   // Continue without sound
          // }
          await _mediaService.playWrongSound();
          _moveToNextQuestion();
        }
      } else {
        // No sound, just move to next question
        if (result['isCorrect']) {
          _moveToNextQuestion(false);
        } else {
          _moveToNextQuestion();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error submitting answer: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAnswerSubmitting = false;
        });
      }
    }
  }

  // Method to show the level up screen with score and level information
  Future<void> _showLevelUpScreen(int newLevel) async {
    // We don't need to set _isLevelUpPending to false anymore since we're showing the level up screen immediately

    if (!mounted) return;
    stopTimer();
    // Calculate score needed for next level

    await Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        pageBuilder: (context, animation, secondaryAnimation) {
          // Important: newLevel is the current level after leveling up
          // The LevelUpScreen expects the level that was just reached (not the next level)
          return LevelUpScreen(
            newLevel: newLevel,
            bonusPoints: _levelUpBonusPoints,
            currentScore: currentScore,
            onContinue: () async {
              await _mediaService.stopSound();
              Navigator.of(context).pop();
              _moveToNextQuestion(false);
            },
          );
        },
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );

    // The next question will be shown by the onContinue callback
  }

  void _moveToNextQuestion([bool shouldDecreaseLife = true]) {
    _timer?.cancel();

    if (currentQuestionIndex < _questions.length - 1) {
      setState(() {
        currentQuestionIndex++;
        _remainingTime = _questions[currentQuestionIndex].time;
        selectedAnswer = null;
      });
      _initializeMediaForCurrentQuestion();
      _startTimer();
    } else {
      setState(() {
        isChallengeCompleted = true;
      });

      // Play confetti for completion
      _confettiController.play();

      // End the game by emitting the endGame event

      // Wait a moment to show the completion screen, then return to home
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          // Navigate back to the home screen
          Navigator.of(context).pushReplacementNamed('/home');
        }
      });
    }
  }

  // End the game by emitting the endGame event

  // Build a card to display challenge dates
  Widget _buildChallengeDatesCard() {
    // Parse start_date and end_date if available
    DateTime? startDate;
    DateTime? endDate;

    if (widget.selectedChallenge['start_date'] != null) {
      startDate =
          DateTime.tryParse(widget.selectedChallenge['start_date'].toString());
    }

    if (widget.selectedChallenge['end_date'] != null) {
      endDate =
          DateTime.tryParse(widget.selectedChallenge['end_date'].toString());
    }

    // If no dates are available, return an empty container
    if (startDate == null && endDate == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.secondary.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.event_available_rounded,
            color: AppColors.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              _formatChallengeDates(startDate, endDate),
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: ThemeUtils.getTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  // Format challenge dates for display
  String _formatChallengeDates(DateTime? startDate, DateTime? endDate) {
    if (startDate != null && endDate != null) {
      // Format: "Jan 1, 2023 - Jan 15, 2023"
      return "${_formatDate(startDate)} - ${_formatDate(endDate)}";
    } else if (startDate != null) {
      // Format: "Starts: Jan 1, 2023"
      return "Starts: ${_formatDate(startDate)}";
    } else if (endDate != null) {
      // Format: "Ends: Jan 15, 2023"
      return "Ends: ${_formatDate(endDate)}";
    }
    return "Date not specified";
  }

  // Helper method to format a date
  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return "${months[date.month - 1]} ${date.day}, ${date.year}";
  }
}
