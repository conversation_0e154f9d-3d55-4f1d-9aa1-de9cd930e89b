import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:achawach/providers/challenge_provider.dart';
import 'package:achawach/providers/terms_provider.dart';
import 'package:achawach/models/challenge.dart';
import 'package:achawach/models/category.dart';
import 'package:achawach/models/question.dart';

// Create a proper implementation of ChallengeProvider for testing
class MockChallengeProvider implements ChallengeProvider {
  final List<Category> _categories = [];
  final List<Challenge> _challenges = [];
  final List<Challenge> _startedChallenges = [];
  final List<Challenge> _notStartedChallenges = [];
  final List<Question> _questions = [];
   Map<String, dynamic>? _questionsSummary;
  Map<String, dynamic>? _challengeLife;
  final _isLoading = false;
  String? _error;

  @override
  List<Category> get categories => _categories;

  @override
  List<Challenge> get challenges => _challenges;

  @override
  List<Challenge> get startedChallenges => _startedChallenges;

  @override
  List<Challenge> get notStartedChallenges => _notStartedChallenges;

  @override
  List<Question> get questions => _questions;

  @override
  Map<String, dynamic>? get questionsSummary => _questionsSummary;

  @override
  Map<String, dynamic>? get challengeLife => _challengeLife;

  @override
  bool get isLoading => _isLoading;

  @override
  String? get error => _error;

  @override
  Future<void> fetchCategories() async {}

  @override
  Future<void> fetchChallenges() async {}

  @override
  Future<void> fetchStartedChallenges(int categoryId) async {}

  @override
  Future<void> fetchNotStartedChallenges(int categoryId) async {}

  @override
  Future<void> fetchChallengeQuestions(int challengeId) async {}

  @override
  Future<Map<String, dynamic>> startChallenge(
      int challengeId, int categoryId) async {
    return {
      'status': 'success',
      'data': {'id': challengeId}
    };
  }

  @override
  Future<Map<String, dynamic>> submitAnswer(
      int challengeId, int questionId, int answerId, int categoryId) async {
    return {
      'isCorrect': true,
      'progress': {'level': 1, 'score': 10, 'life': 3}
    };
  }

  @override
  Future<void> getChallengeLife(int challengeId) async {}

  @override
  Future<Map<String, dynamic>> decreaseChallengeLife(
      int challengeId, int qId) async {
    return {'life': 2, 'max': 3};
  }

  // ChangeNotifier methods
  @override
  void addListener(VoidCallback listener) {}

  @override
  void dispose() {}

  @override
  bool get hasListeners => false;

  @override
  void notifyListeners() {}

  @override
  void removeListener(VoidCallback listener) {}
}

// Create a proper implementation of TermsProvider for testing
class MockTermsProvider implements TermsProvider {
  bool _hasAgreedToTerms = false;
  final bool _isLoading = false;
  String? _error;

  @override
  bool get hasAgreedToTerms => _hasAgreedToTerms;

  @override
  bool get isLoading => _isLoading;

  @override
  String? get error => _error;

  @override
  Future<void> setTermsAgreement(bool value) async {
    _hasAgreedToTerms = value;
  }

  // ChangeNotifier methods
  @override
  void addListener(VoidCallback listener) {}

  @override
  void dispose() {}

  @override
  bool get hasListeners => false;

  @override
  void notifyListeners() {}

  @override
  void removeListener(VoidCallback listener) {}
}

// A simplified mock version of SpecialChallengeScreen for testing
class MockSpecialChallengeScreen extends StatelessWidget {
  final Map<String, dynamic> selectedChallenge;

  const MockSpecialChallengeScreen({Key? key, required this.selectedChallenge})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isStarted =
        selectedChallenge['status']?.toLowerCase() == 'started';

    // Extract winning rules if available
    WinningRules? winningRules;
    if (selectedChallenge.containsKey('winning_rules')) {
      winningRules = WinningRules.fromJson(selectedChallenge['winning_rules']);
    }

    // Parse start_date and end_date if available
    DateTime? startDate;
    DateTime? endDate;

    if (selectedChallenge['start_date'] != null) {
      startDate = DateTime.tryParse(selectedChallenge['start_date'].toString());
    }

    if (selectedChallenge['end_date'] != null) {
      endDate = DateTime.tryParse(selectedChallenge['end_date'].toString());
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(selectedChallenge['name'] ?? 'Special Challenge'),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Challenge name
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              selectedChallenge['name'] ?? 'Special Challenge',
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
          ),

          // Challenge status
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              'Status: ${selectedChallenge['status'] ?? 'New'}',
              style: const TextStyle(fontSize: 16),
            ),
          ),

          // Challenge dates if available
          if (startDate != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                'Start Date: ${startDate.toString().substring(0, 10)}',
                style: const TextStyle(fontSize: 16),
              ),
            ),

          if (endDate != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                'End Date: ${endDate.toString().substring(0, 10)}',
                style: const TextStyle(fontSize: 16),
              ),
            ),

          // Challenge stats
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    const Text('Reward'),
                    Text('${selectedChallenge['reward'] ?? 0} Birr'),
                  ],
                ),
                Column(
                  children: [
                    const Text('Winning Points'),
                    Text(winningRules != null
                        ? '${winningRules.winningPoints}'
                        : '${selectedChallenge['winning_points'] ?? 0}'),
                  ],
                ),
                Column(
                  children: [
                    const Text('Lives'),
                    Text('${selectedChallenge['user_life'] ?? 3}'),
                  ],
                ),
              ],
            ),
          ),

          // Start/Continue button
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton(
              onPressed: () {},
              child: Text(isStarted ? 'Continue Challenge' : 'Start Challenge'),
            ),
          ),
        ],
      ),
    );
  }
}

void main() {
  group('SpecialChallengeScreen Tests', () {
    testWidgets(
        'SpecialChallengeScreen displays challenge information correctly',
        (WidgetTester tester) async {
      // Create a mock challenge data with start_date and end_date
      final mockChallenge = {
        'id': 1,
        'name': 'Test Challenge',
        'winning_points': 500,
        'reward': 100,
        'status': 'Started',
        'score': 250,
        'user_life': 3,
        'start_date': '2023-06-01T00:00:00.000Z',
        'end_date': '2023-06-30T23:59:59.000Z',
        'winning_rules': {
          'level': 5,
          'winning_points': 500,
          'rank': 1,
        },
      };

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: MockSpecialChallengeScreen(selectedChallenge: mockChallenge),
        ),
      );

      // Verify challenge name is displayed
      expect(find.text('Test Challenge'), findsAtLeastNWidgets(1));

      // Verify status is displayed
      expect(find.text('Status: Started'), findsOneWidget);

      // Verify dates are displayed
      expect(find.text('Start Date: 2023-06-01'), findsOneWidget);
      expect(find.text('End Date: 2023-06-30'), findsOneWidget);

      // Verify winning points are displayed
      expect(find.text('500'), findsAtLeastNWidgets(1));

      // Verify reward is displayed
      expect(find.text('100 Birr'), findsOneWidget);

      // Verify lives are displayed
      expect(find.text('3'), findsAtLeastNWidgets(1));

      // Verify the button text for a started challenge
      expect(find.text('Continue Challenge'), findsOneWidget);
    });

    testWidgets(
        'SpecialChallengeScreen displays correct button for new challenges',
        (WidgetTester tester) async {
      // Create a mock challenge data for a new challenge
      final mockChallenge = {
        'id': 2,
        'name': 'New Challenge',
        'winning_points': 500,
        'reward': 100,
        'status': 'New',
        'score': 0,
        'user_life': 3,
        'start_date': '2023-07-01T00:00:00.000Z',
        'end_date': '2023-07-31T23:59:59.000Z',
      };

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: MockSpecialChallengeScreen(selectedChallenge: mockChallenge),
        ),
      );

      // Verify the button text for a new challenge
      expect(find.text('Start Challenge'), findsOneWidget);
    });

    testWidgets('SpecialChallengeScreen handles missing dates gracefully',
        (WidgetTester tester) async {
      // Create a mock challenge data without dates
      final mockChallenge = {
        'id': 3,
        'name': 'Challenge Without Dates',
        'winning_points': 300,
        'reward': 50,
        'status': 'New',
        'score': 0,
        'user_life': 3,
      };

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: MockSpecialChallengeScreen(selectedChallenge: mockChallenge),
        ),
      );

      // Verify challenge name is displayed
      expect(find.text('Challenge Without Dates'), findsAtLeastNWidgets(1));

      // Verify dates are not displayed
      expect(find.textContaining('Start Date:'), findsNothing);
      expect(find.textContaining('End Date:'), findsNothing);
    });
  });
}
