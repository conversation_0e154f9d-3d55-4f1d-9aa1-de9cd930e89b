import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Mock classes for testing network interruptions
class NetworkInterruptibleSocketService {
  final StreamController<Map<String, dynamic>> gameEndedController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> winnerAnnouncementController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<bool> connectionStatusController =
      StreamController<bool>.broadcast();

  String? endGameToken;
  String? endGameChallengeId;
  bool _isConnected = false;
  bool _isReconnecting = false;
  int reconnectAttempts = 0;
  int maxReconnectAttempts = 5;

  Stream<Map<String, dynamic>> get gameEndedStream =>
      gameEndedController.stream;
  Stream<Map<String, dynamic>> get winnerAnnouncementStream =>
      winnerAnnouncementController.stream;
  Stream<bool> get connectionStatusStream => connectionStatusController.stream;

  bool get isConnected => _isConnected;
  bool get isReconnecting => _isReconnecting;

  void connect(String token) {
    _isConnected = true;
    connectionStatusController.add(true);
  }

  void disconnect() {
    _isConnected = false;
    connectionStatusController.add(false);
  }

  Future<bool> reconnect(String token) async {
    _isReconnecting = true;
    reconnectAttempts++;

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    if (reconnectAttempts <= maxReconnectAttempts) {
      _isConnected = true;
      _isReconnecting = false;
      connectionStatusController.add(true);
      return true;
    } else {
      _isReconnecting = false;
      connectionStatusController.add(false);
      return false;
    }
  }

  void endGame(String token, String challengeId) {
    if (!_isConnected) {
      throw Exception('Cannot end game: not connected');
    }
    endGameToken = token;
    endGameChallengeId = challengeId;
  }

  void simulateNetworkInterruption() {
    _isConnected = false;
    connectionStatusController.add(false);
  }

  void simulateNetworkRecovery() {
    _isConnected = true;
    connectionStatusController.add(true);
  }

  void dispose() {
    gameEndedController.close();
    winnerAnnouncementController.close();
    connectionStatusController.close();
  }
}

// Network-aware challenge screen
class NetworkAwareChallengeScreen extends StatefulWidget {
  final NetworkInterruptibleSocketService socketService;
  final int challengeId;
  final String challengeName;

  const NetworkAwareChallengeScreen({
    super.key,
    required this.socketService,
    required this.challengeId,
    required this.challengeName,
  });

  @override
  State<NetworkAwareChallengeScreen> createState() =>
      _NetworkAwareChallengeScreenState();
}

class _NetworkAwareChallengeScreenState
    extends State<NetworkAwareChallengeScreen> {
  StreamSubscription? _gameEndedSubscription;
  StreamSubscription? _winnerAnnouncementSubscription;
  StreamSubscription? _connectionStatusSubscription;
  int score = 0;
  int level = 1;
  int lives = 3;
  bool isConnected = true;
  bool isReconnecting = false;

  @override
  void initState() {
    super.initState();
    _setupSubscriptions();
  }

  void _setupSubscriptions() {
    // Cancel any existing subscriptions
    _gameEndedSubscription?.cancel();
    _winnerAnnouncementSubscription?.cancel();
    _connectionStatusSubscription?.cancel();

    // Listen for game ended events
    _gameEndedSubscription =
        widget.socketService.gameEndedStream.listen((data) {
     
      if (mounted) {
        // Handle game end
        _showGameEndDialog(data);
      }
    });

    // Listen for winner announcement events
    _winnerAnnouncementSubscription =
        widget.socketService.winnerAnnouncementStream.listen((data) {
      print('Received winnerAnnouncement event: $data');
      if (mounted) {
        // Handle winner announcement
        _showWinnerAnnouncementDialog(data);
      }
    });

    // Listen for connection status changes
    _connectionStatusSubscription =
        widget.socketService.connectionStatusStream.listen((connected) {
      if (mounted) {
        setState(() {
          isConnected = connected;
          isReconnecting = widget.socketService.isReconnecting;
        });

        if (connected) {
          _showReconnectedSnackBar();
        } else {
          _showDisconnectedSnackBar();
        }
      }
    });
  }

  void _showGameEndDialog(Map<String, dynamic> data) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(data['isWinner'] == true ? 'You Won!' : 'Game Over'),
        content: Text(data['message'] ?? 'The game has ended.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Return to previous screen
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  void _showWinnerAnnouncementDialog(Map<String, dynamic> data) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Winner Announcement'),
        content: Text(data['message'] ?? 'A winner has been announced.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showDisconnectedSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Connection lost. Attempting to reconnect...'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Retry',
          onPressed: () async {
            final prefs = await SharedPreferences.getInstance();
            final token = prefs.getString('auth_token') ?? '';
            widget.socketService.reconnect(token);
          },
        ),
      ),
    );
  }

  void _showReconnectedSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Connection restored!'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
      ),
    );
  }

  void _answerQuestion(bool isCorrect) {
    if (!isConnected) {
      _showCannotAnswerDialog();
      return;
    }

    setState(() {
      if (isCorrect) {
        score += 100;
        if (score % 500 == 0) {
          level++;
        }
      } else {
        lives--;
        if (lives <= 0) {
          // End game when out of lives
          _endGame();
        }
      }
    });
  }

  void _showCannotAnswerDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Connection Required'),
        content: const Text(
            'You cannot answer questions while disconnected. Please wait for the connection to be restored.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _endGame() async {
    if (!isConnected) {
      _showCannotEndGameDialog();
      return;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';
      widget.socketService.endGame(token, widget.challengeId.toString());
    } catch (e) {
      print('Error ending game: $e');
      _showEndGameErrorDialog();
    }
  }

  void _showCannotEndGameDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Connection Required'),
        content: const Text(
            'Cannot end the game while disconnected. Please wait for the connection to be restored.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showEndGameErrorDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: const Text('Failed to end the game. Please try again.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _simulateNetworkInterruption() {
    widget.socketService.simulateNetworkInterruption();
  }

  void _simulateNetworkRecovery() {
    widget.socketService.simulateNetworkRecovery();
  }

  @override
  void dispose() {
    _gameEndedSubscription?.cancel();
    _winnerAnnouncementSubscription?.cancel();
    _connectionStatusSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.challengeName),
        actions: [
          // Connection status indicator
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Icon(
              isConnected ? Icons.wifi : Icons.wifi_off,
              color: isConnected ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Connection status banner
            if (!isConnected)
              Container(
                color: Colors.red.shade100,
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    const Icon(Icons.error_outline, color: Colors.red),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        isReconnecting
                            ? 'Reconnecting... (Attempt ${widget.socketService.reconnectAttempts})'
                            : 'Disconnected. Tap to reconnect.',
                        style: const TextStyle(color: Colors.red),
                      ),
                    ),
                    if (!isReconnecting)
                      TextButton(
                        onPressed: () async {
                          final prefs = await SharedPreferences.getInstance();
                          final token = prefs.getString('auth_token') ?? '';
                          widget.socketService.reconnect(token);
                        },
                        child: const Text('Reconnect'),
                      ),
                  ],
                ),
              ),

            // Score, level, and lives display
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text('Score: $score'),
                  Text('Level: $level'),
                  Text('Lives: $lives'),
                ],
              ),
            ),

            // Mock question
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'What is the capital of France?',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),

            // Answer buttons
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  ElevatedButton(
                    onPressed: () => _answerQuestion(true),
                    child: const Text('Paris'),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => _answerQuestion(false),
                    child: const Text('London'),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => _answerQuestion(false),
                    child: const Text('Berlin'),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => _answerQuestion(false),
                    child: const Text('Madrid'),
                  ),
                ],
              ),
            ),

            // End game button
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton(
                onPressed: _endGame,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                ),
                child: const Text('End Game'),
              ),
            ),

            // Network simulation buttons
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: _simulateNetworkInterruption,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                    ),
                    child: const Text('Simulate Disconnection'),
                  ),
                  ElevatedButton(
                    onPressed: _simulateNetworkRecovery,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                    ),
                    child: const Text('Simulate Reconnection'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Mock App
class NetworkTestApp extends StatelessWidget {
  final NetworkInterruptibleSocketService socketService;

  const NetworkTestApp({
    Key? key,
    required this.socketService,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Network Test App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: Builder(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text('Network Test'),
          ),
          body: Center(
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => NetworkAwareChallengeScreen(
                      socketService: socketService,
                      challengeId: 123,
                      challengeName: 'Network Test Challenge',
                    ),
                  ),
                );
              },
              child: const Text('Start Challenge'),
            ),
          ),
        ),
      ),
    );
  }
}

void main() {
  group('Network Interruption Tests', () {
    late NetworkInterruptibleSocketService socketService;

    setUp(() {
      socketService = NetworkInterruptibleSocketService();
      socketService.connect('test-token');
      SharedPreferences.setMockInitialValues({'auth_token': 'test-token'});
    });

    tearDown(() {
      socketService.dispose();
    });

    testWidgets('Shows disconnection UI when network is lost',
        (WidgetTester tester) async {
      // Build the app
      await tester.pumpWidget(
        NetworkTestApp(
          socketService: socketService,
        ),
      );

      // Start a challenge
      await tester.tap(find.text('Start Challenge'));
      await tester.pumpAndSettle();

      // Verify we're on the challenge screen
      expect(find.text('Network Test Challenge'), findsOneWidget);

      // Verify connection status is good
      expect(find.byIcon(Icons.wifi), findsOneWidget);

      // Simulate network interruption
      await tester.tap(find.text('Simulate Disconnection'));
      await tester.pumpAndSettle();

      // Verify disconnection UI is shown
      expect(find.byIcon(Icons.wifi_off), findsOneWidget);
      expect(find.text('Disconnected. Tap to reconnect.'), findsOneWidget);

      // Try to answer a question while disconnected
      await tester.tap(find.text('Paris'));
      await tester.pumpAndSettle();

      // Verify error dialog is shown
      expect(find.text('Connection Required'), findsOneWidget);
      expect(
          find.text(
              'You cannot answer questions while disconnected. Please wait for the connection to be restored.'),
          findsOneWidget);

      // Dismiss the dialog
      await tester.tap(find.text('OK'));
      await tester.pumpAndSettle();

      // Try to end the game while disconnected
      await tester.tap(find.text('End Game'));
      await tester.pumpAndSettle();

      // Verify error dialog is shown
      expect(find.text('Connection Required'), findsOneWidget);
      expect(
          find.text(
              'Cannot end the game while disconnected. Please wait for the connection to be restored.'),
          findsOneWidget);
    });

    testWidgets('Reconnects and resumes game when network is restored',
        (WidgetTester tester) async {
      // Build the app
      await tester.pumpWidget(
        NetworkTestApp(
          socketService: socketService,
        ),
      );

      // Start a challenge
      await tester.tap(find.text('Start Challenge'));
      await tester.pumpAndSettle();

      // Simulate network interruption
      await tester.tap(find.text('Simulate Disconnection'));
      await tester.pumpAndSettle();

      // Verify disconnection UI is shown
      expect(find.byIcon(Icons.wifi_off), findsOneWidget);

      // Simulate network recovery
      // Use warnIfMissed: false to prevent warnings about hit testing
      await tester.tap(find.text('Simulate Reconnection'), warnIfMissed: false);
      await tester.pumpAndSettle();

      // Manually call the recovery method since the tap might not work in tests
      socketService.simulateNetworkRecovery();
      await tester.pumpAndSettle();

      // Verify connection is restored
      expect(find.byIcon(Icons.wifi), findsOneWidget);

      // Verify game can continue
      await tester.tap(find.text('Paris')); // Correct answer
      await tester.pump();

      // Verify score increased
      expect(find.text('Score: 100'), findsOneWidget);
    });

    testWidgets('Handles game end events during reconnection',
        (WidgetTester tester) async {
      // Build the app
      await tester.pumpWidget(
        NetworkTestApp(
          socketService: socketService,
        ),
      );

      // Start a challenge
      await tester.tap(find.text('Start Challenge'));
      await tester.pumpAndSettle();

      // Simulate network interruption
      await tester.tap(find.text('Simulate Disconnection'));
      await tester.pumpAndSettle();

      // Simulate network recovery
      // Use warnIfMissed: false to prevent warnings about hit testing
      await tester.tap(find.text('Simulate Reconnection'), warnIfMissed: false);
      await tester.pumpAndSettle();

      // Manually call the recovery method since the tap might not work in tests
      socketService.simulateNetworkRecovery();
      await tester.pumpAndSettle();

      // Simulate receiving a game ended event right after reconnection
      socketService.gameEndedController.add({
        'isWinner': true,
        'message': 'You won!',
        'finalState': {'score': 500, 'level': 2},
      });

      // Wait for the event to be processed
      await tester.pumpAndSettle();

      // Verify game end dialog is shown
      expect(find.text('You Won!'), findsOneWidget);
      expect(find.text('You won!'), findsOneWidget);
    });

    testWidgets('Handles multiple disconnections and reconnections',
        (WidgetTester tester) async {
      // Build the app
      await tester.pumpWidget(
        NetworkTestApp(
          socketService: socketService,
        ),
      );

      // Start a challenge
      await tester.tap(find.text('Start Challenge'));
      await tester.pumpAndSettle();

      // Cycle through multiple disconnections and reconnections
      for (int i = 0; i < 3; i++) {
        // Simulate network interruption
        await tester.tap(find.text('Simulate Disconnection'));
        await tester.pumpAndSettle();

        // Verify disconnection UI is shown
        expect(find.byIcon(Icons.wifi_off), findsOneWidget);

        // Simulate network recovery
        // Use warnIfMissed: false to prevent warnings about hit testing
        await tester.tap(find.text('Simulate Reconnection'),
            warnIfMissed: false);
        await tester.pumpAndSettle();

        // Manually call the recovery method since the tap might not work in tests
        socketService.simulateNetworkRecovery();
        await tester.pumpAndSettle();

        // Verify connection is restored
        expect(find.byIcon(Icons.wifi), findsOneWidget);

        // Answer a question to verify game is still functional
        await tester.tap(find.text('Paris')); // Correct answer
        await tester.pump();

        // Verify score increased
        expect(find.text('Score: ${100 * (i + 1)}'), findsOneWidget);
      }
    });
  });
}
