import 'package:flutter/material.dart';

/// A utility class to replace GoogleFonts with local fonts
/// This helps avoid network requests to Google Fonts servers
class AppFonts {
  /// Returns a TextStyle with Poppins font
  static TextStyle poppins({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
    TextDecoration? decoration,
    List<Shadow>? shadows,
  }) {
    return TextStyle(
      fontFamily: 'Poppins',
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      letterSpacing: letterSpacing,
      decoration: decoration,
      shadows: shadows,
    );
  }

  /// Returns a TextStyle with Rubik font (fallback to Poppins)
  static TextStyle rubik({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
    TextDecoration? decoration,
    List<Shadow>? shadows,
  }) {
    return TextStyle(
      fontFamily: 'Poppins', // Fallback to Poppins since we don't have Rubik locally
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      letterSpacing: letterSpacing,
      decoration: decoration,
      shadows: shadows,
    );
  }
}
