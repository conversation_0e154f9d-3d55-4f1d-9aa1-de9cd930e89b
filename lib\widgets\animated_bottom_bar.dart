// import 'dart:ui';
// import 'package:achawach/core/constants/app_colors.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';

// class AnimatedBottomBar extends StatefulWidget {
//   final String? currentRoute;

//   const AnimatedBottomBar({
//     super.key,
//     this.currentRoute,
//   });

//   @override
//   _AnimatedBottomBarState createState() => _AnimatedBottomBarState();
// }

// class _AnimatedBottomBarState extends State<AnimatedBottomBar>
//     with TickerProviderStateMixin {
//   late int _selectedIndex;
//   final double _iconSize = 28.0;
//   final double _indicatorWidth = 60.0;
//   final double _indicatorHeight = 60.0;

//   final List<IconData> _icons = [
//     Icons.home_rounded,
//     Icons.search_rounded,
//     Icons.assignment_turned_in_rounded,
//     Icons.person_rounded,
//   ];

//   void _handleNavigation(BuildContext context, int index) {
//     switch (index) {
//       case 0: // Home
//         if (widget.currentRoute != '/home') {
//           Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
//         }
//         break;
//       case 1: // Search
//         if (widget.currentRoute != '/search') {
//           Navigator.pushNamed(context, '/search');
//         }
//         break;
//       case 2: // Joined Challenges
//         if (widget.currentRoute != '/joined-challenges') {
//           Navigator.pushNamed(context, '/joined-challenges');
//         }
//         break;
//       case 3: // Profile
//         if (widget.currentRoute != '/profile') {
//           Navigator.pushNamed(context, '/profile');
//         }
//         break;
//     }
//   }

//   double _calculateAlignmentX(int index) {
//     final count = _icons.length;
//     if (count <= 1) return 0.0;
//     final position = index / (count - 1);
//     return lerpDouble(-1.0, 1.0, position) ?? 0.0;
//   }

//   late AnimationController _indicatorController;
//   late Animation<double> _indicatorScaleAnimation;
//   late Animation<double> _indicatorBlurAnimation;

//   late AnimationController _iconController;
//   late Animation<double> _iconScaleAnimation;

//   @override
//   void initState() {
//     super.initState();
//     _selectedIndex = _getInitialIndex();

//     // Indicator animations
//     _indicatorController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 500),
//     );
//     _indicatorScaleAnimation = Tween<double>(
//       begin: 1.0,
//       end: 1.2,
//     ).animate(CurvedAnimation(
//       parent: _indicatorController,
//       curve: Curves.elasticOut,
//       reverseCurve: Curves.easeInOut,
//     ));
//     _indicatorBlurAnimation = Tween<double>(
//       begin: 15.0,
//       end: 25.0,
//     ).animate(CurvedAnimation(
//       parent: _indicatorController,
//       curve: Curves.easeInOut,
//     ));

//     // Icon animations
//     _iconController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 400),
//     );
//     _iconScaleAnimation = Tween<double>(
//       begin: 1.0,
//       end: 1.3,
//     ).animate(CurvedAnimation(
//       parent: _iconController,
//       curve: Curves.elasticOut,
//       reverseCurve: Curves.easeInOut,
//     ));
//   }

//   @override
//   void dispose() {
//     _indicatorController.dispose();
//     _iconController.dispose();
//     super.dispose();
//   }

//   void _handleTap(int index) {
//     if (_selectedIndex == index) return;

//     setState(() {
//       _selectedIndex = index;
//     });

//     HapticFeedback.lightImpact();

//     // Play animations
//     _indicatorController.forward().then((_) => _indicatorController.reverse());
//     _iconController.forward().then((_) => _iconController.reverse());

//     _handleNavigation(context, index);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
//       child: ClipRRect(
//         borderRadius: BorderRadius.circular(35),
//         child: BackdropFilter(
//           filter: ImageFilter.blur(sigmaX: 25, sigmaY: 25),
//           child: Container(
//             height: 70,
//             decoration: BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [
//                   Colors.white.withValues(alpha: 0.30),
//                   Colors.white.withValues(alpha: 0.08),
//                 ],
//                 begin: Alignment.topLeft,
//                 end: Alignment.bottomRight,
//               ),
//               borderRadius: BorderRadius.circular(35),
//               border: Border.all(
//                 color: Colors.white.withValues(alpha: 0.25),
//                 width: 1.8,
//               ),
//             ),
//             child: Stack(
//               alignment: Alignment.center,
//               children: [
//                 AnimatedAlign(
//                   alignment: Alignment(_calculateAlignmentX(_selectedIndex), 0),
//                   duration: const Duration(milliseconds: 600),
//                   curve: Curves.easeOutCubic,
//                   child: ScaleTransition(
//                     scale: _indicatorScaleAnimation,
//                     child: AnimatedBuilder(
//                       animation: _indicatorBlurAnimation,
//                       builder: (context, child) => Container(
//                         width: _indicatorWidth,
//                         height: _indicatorHeight,
//                         decoration: BoxDecoration(
//                           shape: BoxShape.circle,
//                           gradient: LinearGradient(
//                             colors: [
//                               AppColors.primary.withValues(alpha: 0.5),
//                               AppColors.secondary.withValues(alpha: 0.3),
//                             ],
//                             begin: Alignment.topLeft,
//                             end: Alignment.bottomRight,
//                           ),
//                           border: Border.all(
//                             color: Colors.white.withValues(alpha: 0.5),
//                             width: 2,
//                           ),
//                           boxShadow: [
//                             BoxShadow(
//                               color: AppColors.secondary.withValues(alpha: 0.3),
//                               blurRadius: _indicatorBlurAnimation.value,
//                               spreadRadius: 3,
//                               offset: const Offset(0, 2),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                   children: List.generate(_icons.length, (index) {
//                     final isSelected = _selectedIndex == index;
//                     return Expanded(
//                       child: GestureDetector(
//                         onTap: () => _handleTap(index),
//                         child: Column(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             ScaleTransition(
//                               scale: isSelected
//                                   ? _iconScaleAnimation
//                                   : const AlwaysStoppedAnimation(1.0),
//                               child: ShaderMask(
//                                 shaderCallback: (bounds) => LinearGradient(
//                                   colors: isSelected
//                                       ? [AppColors.primary, AppColors.secondary]
//                                       : [Colors.grey[400]!, Colors.grey[400]!],
//                                 ).createShader(bounds),
//                                 child: Icon(
//                                   _icons[index],
//                                   size: _iconSize,
//                                   color: Colors.white,
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     );
//                   }),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   int _getInitialIndex() {
//     switch (widget.currentRoute) {
//       case '/home':
//         return 0;
//       case '/search':
//         return 1;
//       case '/joined-challenges':
//         return 2;
//       case '/profile':
//         return 3;
//       default:
//         return 0;
//     }
//   }
// }

import 'dart:ui';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AnimatedBottomBar extends StatefulWidget {
  final String? currentRoute;

  const AnimatedBottomBar({
    super.key,
    this.currentRoute,
  });

  @override
  _AnimatedBottomBarState createState() => _AnimatedBottomBarState();
}

class _AnimatedBottomBarState extends State<AnimatedBottomBar>
    with TickerProviderStateMixin {
  late int _selectedIndex;

  final List<BottomNavItem> _navItems = [
    BottomNavItem(
        icon: Icons.home_outlined, activeIcon: Icons.home, label: 'Home'),
    BottomNavItem(
        icon: Icons.search_outlined, activeIcon: Icons.search, label: 'Search'),
    BottomNavItem(
        icon: Icons.bookmark_border,
        activeIcon: Icons.bookmark,
        label: 'Challenges'),
    BottomNavItem(
        icon: Icons.person_outline, activeIcon: Icons.person, label: 'Profile'),
  ];

  late AnimationController _animationController;
  late AnimationController _rippleController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _rippleAnimation;

  @override
  void initState() {
    super.initState();
    _selectedIndex = _getInitialIndex();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _rippleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _rippleController.dispose();
    super.dispose();
  }

  void _handleNavigation(BuildContext context, int index) {
    switch (index) {
      case 0:
        if (widget.currentRoute != '/home') {
          Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
        }
        break;
      case 1:
        if (widget.currentRoute != '/search') {
          Navigator.pushNamed(context, '/search');
        }
        break;
      case 2:
        if (widget.currentRoute != '/joined-challenges') {
          Navigator.pushNamed(context, '/joined-challenges');
        }
        break;
      case 3:
        if (widget.currentRoute != '/profile') {
          Navigator.pushNamed(context, '/profile');
        }
        break;
    }
  }

  void _onItemTapped(int index) {
    if (_selectedIndex == index) return;

    setState(() {
      _selectedIndex = index;
    });

    HapticFeedback.selectionClick();
    _rippleController.forward().then((_) => _rippleController.reset());
    _handleNavigation(context, index);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 1),
            end: Offset.zero,
          ).animate(_slideAnimation),
          child: Container(
            height: 75,
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[900]?.withOpacity(0.95)
                  : Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 40,
                  offset: const Offset(0, 20),
                  spreadRadius: 0,
                ),
              ],
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[700]!.withOpacity(0.3)
                    : Colors.grey[200]!.withOpacity(0.5),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(25),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(_navItems.length, (index) {
                    return _buildNavItem(index);
                  }),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index) {
    final isSelected = _selectedIndex == index;
    final item = _navItems[index];

    return Expanded(
      child: GestureDetector(
        onTap: () => _onItemTapped(index),
        behavior: HitTestBehavior.opaque,
        child: SizedBox(
          height: 75,
          child: Stack(
            children: [
              // Ripple effect - positioned behind everything
              if (isSelected)
                Positioned.fill(
                  child: Center(
                    child: AnimatedBuilder(
                      animation: _rippleAnimation,
                      builder: (context, child) {
                        return Container(
                          width: 60 * _rippleAnimation.value,
                          height: 60 * _rippleAnimation.value,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.primary.withOpacity(
                              0.15 * (1 - _rippleAnimation.value),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),

              // Active background indicator - bigger circle to contain icon + label
              if (isSelected)
                Positioned.fill(
                  child: Center(
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      width: 68,
                      height: 68,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColors.primary,
                            AppColors.secondary,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.25),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

              // Content - Icon and label perfectly centered
              Positioned.fill(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Icon container with consistent sizing
                    Container(
                      width: 24,
                      height: 24,
                      alignment: Alignment.center,
                      child: AnimatedScale(
                        scale: isSelected ? 1.0 : 1.0,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                        child: Icon(
                          isSelected ? item.activeIcon : item.icon,
                          size: 24,
                          color: isSelected
                              ? Colors.white
                              : Theme.of(context).brightness == Brightness.dark
                                  ? Colors.grey[400]
                                  : Colors.grey[600],
                        ),
                      ),
                    ),

                    const SizedBox(height: 4),

                    // Label with consistent styling
                    SizedBox(
                      height: 12,
                      child: AnimatedDefaultTextStyle(
                        duration: const Duration(milliseconds: 300),
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w500,
                          color: isSelected
                              ? Colors.white
                              : Theme.of(context).brightness == Brightness.dark
                                  ? Colors.grey[400]
                                  : Colors.grey[600],
                          letterSpacing: 0.1,
                        ),
                        child: Text(
                          item.label,
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int _getInitialIndex() {
    switch (widget.currentRoute) {
      case '/home':
        return 0;
      case '/search':
        return 1;
      case '/joined-challenges':
        return 2;
      case '/profile':
        return 3;
      default:
        return 0;
    }
  }
}

class BottomNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  BottomNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}
