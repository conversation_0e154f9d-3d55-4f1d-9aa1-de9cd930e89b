// models/category.dart
import 'package:flutter/material.dart';

// Helper function/map to get IconData from String
IconData getIconFromString(String? iconName) {
  if (iconName == null) return Icons.category; // Default icon

  switch (iconName) {
    case 'Icons.science':
      return Icons.science_outlined; // Use outlined icons for consistency?
    case 'Icons.history':
      return Icons.history_edu_outlined;
    case 'Icons.sports_soccer':
      return Icons.sports_soccer_outlined;
    case 'Icons.computer':
      return Icons.computer_outlined;
    case 'Icons.palette':
      return Icons.palette_outlined;
    case 'Icons.public':
      return Icons.public_outlined;
    case 'Icons.movie':
      return Icons.movie_filter_outlined;
    // Add more mappings as needed
    default:
      print('Warning: Icon mapping not found for "$iconName"');
      return Icons.category; // Default fallback
  }
}

class QuestionsCategory {
  final int id;
  final String name;
  final String? description;
  final String? iconString; // Store the raw string from API
  final IconData iconData; // Store the actual IconData

  QuestionsCategory({
    required this.id,
    required this.name,
    this.description,
    this.iconString,
  }) : iconData = getIconFromString(iconString); // Initialize iconData here

  factory QuestionsCategory.fromJson(Map<String, dynamic> json) {
    return QuestionsCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      iconString: json['icon'], // Get the string name
    );
  }

  // Optional: For comparing categories if needed (e.g., in Sets)
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuestionsCategory && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}