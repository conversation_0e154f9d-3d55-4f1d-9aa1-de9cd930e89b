class RecentActivity {
  final int id;
  final int userId;
  final String activity;
  final DateTime createdAt;
  final DateTime updateAt;

  RecentActivity({
    required this.id,
    required this.userId,
    required this.activity,
    required this.createdAt,
    required this.updateAt,
  });

  factory RecentActivity.fromJson(Map<String, dynamic> json) {
    return RecentActivity(
      id: json['id'] ?? 0,
      userId: json['userId'] ?? '',
      activity: json['activity'] ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updateAt: json['updateAt'] != null
          ? DateTime.parse(json['updateAt'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'activity': activity,
      'createdAt': createdAt.toIso8601String(),
      'updateAt': updateAt.toIso8601String(),
    };
  }
}
