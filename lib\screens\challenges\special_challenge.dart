import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/core/utils/theme_utils.dart';
import 'package:achawach/providers/terms_provider.dart';
import 'package:achawach/screens/challenges/special_challenge_start.dart';
import 'package:achawach/widgets/terms_and_conditions_dialog.dart';
import 'package:achawach/widgets/winning_rules_card.dart';
import 'package:achawach/widgets/full_page_loader.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:achawach/models/leaderboard_entry.dart';
import 'package:achawach/services/socket_service.dart';
import 'dart:async';
import 'package:achawach/services/api_service.dart';
import 'dart:math';
import 'package:provider/provider.dart';
import 'package:achawach/providers/challenge_provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:achawach/models/challenge.dart';

class SpecialChallengeScreen extends StatefulWidget {
  final Map<String, dynamic> selectedChallenge;

  const SpecialChallengeScreen({super.key, required this.selectedChallenge});

  @override
  State<SpecialChallengeScreen> createState() => _SpecialChallengeScreenState();
}

class _SpecialChallengeScreenState extends State<SpecialChallengeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  Map<String, dynamic>? _life;
  bool _isLoadingLife = false;
  List<LeaderboardEntry> _leaderboardEntries = [];
  bool _isLoadingLeaderboard = false;
  bool _isInitialLoading = true;
  Timer? _leaderboardTimer;
  final ApiService _apiService = ApiService();
  StreamSubscription? _gameStateSubscription;
  StreamSubscription? _errorSubscription;
  final SocketService _socketService = SocketService.instance;

  @override
  void initState() {
    super.initState();
    if (mounted) {
      _setupAnimations();
      _initializeScreen();
    }
  }

  // Initialize screen with loading sequence
  Future<void> _initializeScreen() async {
    setState(() => _isInitialLoading = true);

    try {
      // Run initialization tasks
      await Future.wait([
        _loadLifeData(),
        _loadLeaderboard(),
      ]);
      _setupSocketConnection();

      // Set up periodic leaderboard updates
      _leaderboardTimer = Timer.periodic(const Duration(seconds: 30), (_) {
        if (mounted) {
          _loadLeaderboard();
        }
      });

      // Add minimum loading time for smooth UX
      await Future.delayed(const Duration(milliseconds: 1500));
    } catch (e) {
      debugPrint('Error during screen initialization: $e');
    } finally {
      if (mounted) {
        setState(() => _isInitialLoading = false);
      }
    }
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
      ),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.35),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
      ),
    );
    _animationController.forward();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadLifeData(); // Reload life data when dependencies change
  }

  Future<void> _loadLifeData() async {
    if (!mounted) return;
    final bool isStarted =
        widget.selectedChallenge['status'] != 1 ? true : false;
    if (!isStarted) return;

    setState(() => _isLoadingLife = true);

    try {
      final challengeId = widget.selectedChallenge['id'];
      final lifeData = await _apiService.getChallengeLife(challengeId);
      if (mounted) {
        setState(() {
          _life = lifeData;
          _isLoadingLife = false;
        });
      }
    } catch (e) {
      print('Error loading life data: $e');
      if (mounted) {
        setState(() => _isLoadingLife = false);
      }
    }
  }

  void _setupSocketConnection() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? prefs.getString('token');

      if (token == null || token.isEmpty) {
        print('No token found for socket connection');
        return;
      }

      final challengeId = widget.selectedChallenge['id'];
      await _socketService.connect(token, challengeId.toString());

      // Listen for game state updates
      _gameStateSubscription = _socketService.gameStateStream.listen((data) {
        if (data['challengeId'] == challengeId) {
          if (mounted) {
            setState(() {
              if (data.containsKey('life')) {
                _life = data['life'];
              }
            });
          }
        }
      });

      // Listen for errors
      _errorSubscription = _socketService.errorStream.listen((error) {
        print('Socket error: $error');
      });
    } catch (e) {
      print('Error setting up socket connection: $e');
    }
  }

  Future<void> _loadLeaderboard() async {
    if (!mounted) return;

    setState(() => _isLoadingLeaderboard = true);

    try {
      // Use mockup data instead of API call
    } catch (e) {
      print('Error loading leaderboard: $e');
      if (mounted) {
        setState(() => _isLoadingLeaderboard = false);
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _leaderboardTimer?.cancel();
    _gameStateSubscription?.cancel();
    _errorSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Show initial loading screen
    if (_isInitialLoading) {
      return const ChallengeLoader();
    }

    final challenge = widget.selectedChallenge;
    final bool isStarted = challenge['status'] != 1 ? true : false;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Stack(
        children: [
          CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              SliverAppBar(
                expandedHeight: 80,
                pinned: true,
                backgroundColor: AppColors.primary,
                flexibleSpace: FlexibleSpaceBar(
                  centerTitle: true,
                  title: Text(
                    challenge['name'] ?? 'Special Challenge',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              SliverPadding(
                padding: const EdgeInsets.all(16.0),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    _buildChallengeCard(challenge),
                    if (isStarted) ...[
                      const SizedBox(height: 24),
                      _buildLeaderboardSection(),
                    ],
                    const SizedBox(height: 100),
                  ]),
                ),
              ),
            ],
          ),
          _buildStartButton(isStarted),
        ],
      ),
    );
  }

  Widget _buildChallengeCard(Map<String, dynamic> challenge) {
    return Container(
      decoration: BoxDecoration(
        color: ThemeUtils.getCardColor(context),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 100,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [AppColors.primary, AppColors.secondary],
              ),
            ),
            child: Center(
              child: Icon(
                Icons.emoji_events_rounded,
                size: 48,
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        challenge['name'] ?? 'Special Challenge',
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: ThemeUtils.getTextColor(context),
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: challenge['status'] == 'Started'
                            ? _getStatusColor(1)
                            : _getStatusColor(challenge['status']),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        'New',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  challenge['description'] ?? 'No description available',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color:
                        ThemeUtils.getSecondaryTextColor(context, opacity: 0.8),
                  ),
                ),
                const SizedBox(height: 16),
                _buildChallengeStats(challenge),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChallengeStats(Map<String, dynamic> challenge) {
    // Extract winning rules if available
    WinningRules? winningRules;
    if (challenge.containsKey('winning_rules')) {
      winningRules = WinningRules.fromJson(challenge['winning_rules']);
    }

    // Get current user rank from leaderboard
    int? currentRank;
    for (var entry in _leaderboardEntries) {
      // Find the current user in the leaderboard
      if (entry.id == _getUserId()) {
        currentRank = entry.rank;
        break;
      }
    }

    // Parse start_date and end_date if available
    DateTime? startDate;
    DateTime? endDate;

    if (challenge['start_date'] != null) {
      startDate = DateTime.tryParse(challenge['start_date'].toString());
    }

    if (challenge['end_date'] != null) {
      endDate = DateTime.tryParse(challenge['end_date'].toString());
    }

    return Column(
      children: [
        // Display challenge dates if available
        if (startDate != null || endDate != null) ...[
          Container(
            margin: const EdgeInsets.only(bottom: 20),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.calendar_today_rounded,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    _formatChallengeDates(startDate, endDate),
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: ThemeUtils.getTextColor(context),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ],

        // Use Wrap instead of Row to handle overflow on smaller screens
        Wrap(
          alignment: WrapAlignment.spaceAround,
          spacing: 16, // horizontal spacing
          runSpacing: 16, // vertical spacing when wrapped
          children: [
            _buildStatItem(
              icon: Icons.emoji_events,
              value:
                  '${NumberFormat("#,##0.00", "en_US").format(challenge['reward'] ?? 0)} ETB',
              label: 'Total Prepared Reward',
              color: Colors.amber,
            ),
            _buildStatItem(
              icon: Icons.favorite,
              value: '${challenge['user_life'] ?? 3}',
              label: 'Lives/day',
              color: Colors.red,
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildPrizeDistributionCard(),
        const SizedBox(height: 24),
        if (winningRules != null) ...[
          const SizedBox(height: 24),
          WinningRulesCard(
            winningRules: winningRules,
            currentScore: challenge['score'] ?? 0,
            currentLevel: _life?['level'] ?? 1,
            currentRank: currentRank,
          ),
        ],
      ],
    );
  }

  Widget _buildPrizeDistributionCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Prize Distribution',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          _buildPrizeRow('🥇', '1st Place', '45% of the prize pool'),
          const SizedBox(height: 12),
          _buildPrizeRow('🥈', '2nd Place', '25% of the prize pool'),
          const SizedBox(height: 12),
          _buildPrizeRow('🥉', '3rd Place', '15% of the prize pool'),
          const SizedBox(height: 12),
          _buildPrizeRow('🏆', 'Top 20%', 'Share of the remaining 15%'),
        ],
      ),
    );
  }

  Widget _buildPrizeRow(String icon, String title, String subtitle) {
    return Row(
      children: [
        Text(icon, style: const TextStyle(fontSize: 24)),
        const SizedBox(width: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper method to get the current user ID
  int _getUserId() {
    // This should be implemented to return the current user's ID
    // For now, return a placeholder value
    return 0; // Replace with actual implementation
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return SizedBox(
      width: 100, // Fixed width to ensure consistent sizing
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 14, // Slightly smaller font
              fontWeight: FontWeight.bold,
              color: ThemeUtils.getTextColor(context),
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: ThemeUtils.getSecondaryTextColor(context, opacity: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Method to handle starting or continuing a special challenge
  Future<void> _startSpecialChallenge(bool isStarted) async {
    if (!mounted) return;

    try {
      final challengeProvider =
          Provider.of<ChallengeProvider>(context, listen: false);

      // Show loading indicator
      if (!mounted) return;

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        },
      );

      // Call the start challenge API only for new challenges
      if (!isStarted) {
        await challengeProvider.startChallenge(
            widget.selectedChallenge['id'], 0);
        // Update the status locally
        widget.selectedChallenge['status'] = 'Started';
      }

      // Close loading indicator
      if (mounted) {
        Navigator.of(context).pop();
      } else {
        return;
      }

      // Navigate to start screen with updated data
      if (mounted) {
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SpecialChallengeStartScreen(
              selectedChallenge: widget.selectedChallenge,
            ),
          ),
        );
      } else {
        return;
      }

      // After returning from SpecialChallengeStartScreen
      if (!mounted) return;

      // Refresh life data
      await challengeProvider.getChallengeLife(widget.selectedChallenge['id']);

      // Get updated challenge data directly from API
      final challengeId = widget.selectedChallenge['id'];
      final lifeData = await _apiService.getChallengeLife(challengeId);

      if (!mounted) return;

      // Update the selected challenge with new data
      setState(() {
        _life = lifeData;
        // Update score from life data
        if (lifeData.containsKey('score')) {
          widget.selectedChallenge['score'] = lifeData['score'] ?? 0;
        }
        _loadLeaderboard(); // Refresh leaderboard data
      });

      // Return to the home screen to refresh the featured challenges
      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/home',
          (route) => false,
        );
      }
    } catch (e) {
      // Close loading indicator if it's showing
      if (mounted) {
        if (Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start challenge: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildStartButton(bool isStarted) {
    return Positioned(
      bottom: 24,
      left: 24,
      right: 24,
      child: SafeArea(
        child: ElevatedButton(
          onPressed: () async {
            try {
              final termsProvider =
                  Provider.of<TermsProvider>(context, listen: false);

              // Check if user has already agreed to terms
              if (!isStarted && !termsProvider.hasAgreedToTerms) {
                // Show terms and conditions dialog
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    return TermsAndConditionsDialog(
                      onAgree: () async {
                        // Close the terms dialog
                        Navigator.pop(context);

                        // Continue with starting the challenge
                        await _startSpecialChallenge(isStarted);
                      },
                      onDecline: () {
                        // Close the terms dialog and do nothing
                        Navigator.pop(context);
                      },
                    );
                  },
                );
              } else {
                // User has already agreed to terms, proceed directly
                await _startSpecialChallenge(isStarted);
              }
            } catch (e) {
              // Show error message
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to start challenge: ${e.toString()}'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
            elevation: 5,
          ),
          child: Text(
            isStarted ? 'Continue Challenge' : 'Join Challenge',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLeaderboardSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Leaderboard',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: ThemeUtils.getTextColor(context),
              ),
            ),
            Text(
              'Top Players',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppColors.text.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _isLoadingLeaderboard
            ? const Center(child: CircularProgressIndicator())
            : _leaderboardEntries.isEmpty
                ? Center(
                    child: Text(
                      'No leaderboard entries yet',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: AppColors.text.withValues(alpha: 0.6),
                      ),
                    ),
                  )
                : _buildLeaderboardList(),
      ],
    );
  }

  Widget _buildLeaderboardList() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const SizedBox(width: 36),
                Expanded(
                  child: Text(
                    'Player',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                Text(
                  'Score',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: min(10, _leaderboardEntries.length),
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final entry = _leaderboardEntries[index];
              final isTopThree = index < 3;

              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: ListTile(
                  leading: Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: _getLeaderboardRankColor(index),
                      shape: BoxShape.circle,
                      boxShadow: isTopThree
                          ? [
                              BoxShadow(
                                color: _getLeaderboardRankColor(index)
                                    .withValues(alpha: 0.4),
                                blurRadius: 8,
                                spreadRadius: 1,
                              )
                            ]
                          : null,
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  title: Text(
                    entry.fullName,
                    style: GoogleFonts.poppins(
                      fontWeight:
                          isTopThree ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: isTopThree
                          ? _getLeaderboardRankColor(index)
                              .withValues(alpha: 0.2)
                          : Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${entry.score} pts',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.bold,
                        color: isTopThree
                            ? _getLeaderboardRankColor(index)
                            : AppColors.text,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(int status) {
    switch (status) {
      case 1:
        return Colors.green;
      case 2:
        return Colors.blue;
      case 0:
      default:
        return AppColors.primary;
    }
  }

  Color _getLeaderboardRankColor(int rank) {
    switch (rank) {
      case 0:
        return Colors.amber; // Gold
      case 1:
        return Colors.grey[400]!; // Silver
      case 2:
        return Colors.brown[300]!; // Bronze
      default:
        return AppColors.primary.withValues(alpha: 0.7);
    }
  }

  // Format challenge dates for display
  String _formatChallengeDates(DateTime? startDate, DateTime? endDate) {
    if (startDate != null && endDate != null) {
      // Format: "Jan 1, 2023 - Jan 15, 2023"
      return "${_formatDate(startDate)} - ${_formatDate(endDate)}";
    } else if (startDate != null) {
      // Format: "Starts: Jan 1, 2023"
      return "Starts: ${_formatDate(startDate)}";
    } else if (endDate != null) {
      // Format: "Ends: Jan 15, 2023"
      return "Ends: ${_formatDate(endDate)}";
    }
    return "Date not specified";
  }

  // Helper method to format a date
  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return "${months[date.month - 1]} ${date.day}, ${date.year}";
  }
}
