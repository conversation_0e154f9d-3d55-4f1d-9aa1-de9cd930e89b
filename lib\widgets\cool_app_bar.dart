import 'package:achawach/core/constants/app_colors.dart';

import 'package:flutter/material.dart';
import 'dart:ui';

class CoolAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions; // Optional actions for the right side
  final Widget? leading; // Optional leading widget for the left side
  final PreferredSizeWidget? bottom;
  const CoolAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.bottom,
  });

  @override
  Size get preferredSize =>
      const Size.fromHeight(kToolbarHeight + 10); // Slightly taller AppBar

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      // borderRadius: const BorderRadius.vertical(bottom: Radius.circular(20)), // Rounded bottom corners
      child: BackdropFilter(
        filter:
            ImageFilter.blur(sigmaX: 8, sigmaY: 8), // Increased blur for AppBar
        child: Container(
          padding: const EdgeInsets.only(
              bottom: 10), // Add some padding at the bottom
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary, // Lighter glass for AppBar
                AppColors.primary,
              ],
            ),
            border: Border(
              bottom: BorderSide(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 1.5), // Stronger bottom border
            ),
          ),
          child: AppBar(
            leading: leading, // Use the provided leading widget
            title: Text(
              title,
              style: const TextStyle(
                color: AppColors.text,
                fontWeight: FontWeight.bold,
                fontSize: 22, // Slightly larger title
              ),
            ),
            actions: actions, // Use the provided actions
            backgroundColor:
                Colors.transparent, // Make AppBar background transparent
            elevation: 0, // Remove default AppBar shadow
            centerTitle: true, // Center the title
            bottom: bottom, // Add the bottom widget
          ),
        ),
      ),
    );
  }
}
