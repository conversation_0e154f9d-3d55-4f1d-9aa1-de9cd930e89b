import 'package:flutter/material.dart';
import 'package:achawach/core/constants/app_colors.dart';

import 'package:lottie/lottie.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:confetti/confetti.dart';

class LevelUpScreen extends StatefulWidget {
  final int newLevel;
  final int bonusPoints;
  final VoidCallback onContinue;
  final int? scoreToNextLevel; // Score needed for next level
  final int? currentScore; // Current user score

  const LevelUpScreen({
    Key? key,
    required this.newLevel,
    required this.bonusPoints,
    required this.onContinue,
    this.scoreToNextLevel,
    this.currentScore,
  }) : super(key: key);

  @override
  State<LevelUpScreen> createState() => _LevelUpScreenState();
}

class _LevelUpScreenState extends State<LevelUpScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late ConfettiController _confettiController;
  bool _showBonusPoints = false;

  @override
  void initState() {
    super.initState();

    // Debug print to verify bonus points value
   

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.elasticOut,
      ),
    );

    _confettiController = ConfettiController(
      duration: const Duration(seconds: 5),
    );

    // Start animations with a slight delay
    Future.delayed(const Duration(milliseconds: 300), () {
      _animationController.forward();
      _confettiController.play();

      // Show bonus points with a delay for dramatic effect
      Future.delayed(const Duration(milliseconds: 1200), () {
        setState(() {
          _showBonusPoints = true;
        });
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.85),
      body: Stack(
        alignment: Alignment.center,
        children: [
          // Background confetti
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirection: 3.14 / 2, // downward
              emissionFrequency: 0.05,
              numberOfParticles: 20,
              maxBlastForce: 20,
              minBlastForce: 10,
              gravity: 0.2,
              colors: const [
                Colors.green,
                Colors.blue,
                Colors.pink,
                Colors.orange,
                Colors.purple,
                Colors.yellow,
              ],
            ),
          ),

          // Main content
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Level up title with scale animation
              ScaleTransition(
                scale: _scaleAnimation,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withValues(alpha: 0.5),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Text(
                    'LEVEL UP!',
                    style: GoogleFonts.rubik(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 2,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 40),

              // Level animation
              Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.amber.withValues(alpha: 0.5),
                      blurRadius: 30,
                      spreadRadius: 10,
                    ),
                  ],
                ),
                child: Center(
                  child: Lottie.asset(
                    'assets/animations/trophy.json', // Local trophy animation
                    width: 150,
                    height: 150,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      // Fallback if animation asset is missing
                      return const Icon(
                        Icons.emoji_events,
                        size: 100,
                        color: Colors.amber,
                      );
                    },
                  ),
                ),
              ),

              const SizedBox(height: 40),

              // New level information
              Text(
                'You reached Level ${widget.newLevel}',
                style: GoogleFonts.rubik(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),

              // Display score information if available
              if (widget.currentScore != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    'Current Score: ${widget.currentScore}',
                    style: GoogleFonts.rubik(
                      fontSize: 18,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ),

              // Display score needed for next level if available
              // if (widget.scoreToNextLevel != null)
              //   Padding(
              //     padding: const EdgeInsets.only(top: 4.0),
              //     child: Text(
              //       'Score for Level ${widget.newLevel + 1}: ${widget.scoreToNextLevel}',
              //       style: GoogleFonts.rubik(
              //         fontSize: 16,
              //         color: Colors.white.withValues(alpha: 0.8),
              //       ),
              //     ),
              //   ),

              const SizedBox(height: 20),

              // Bonus points with animated appearance
              AnimatedOpacity(
                opacity: _showBonusPoints ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 500),
                child: Column(
                  children: [
                    Text(
                      'BONUS',
                      style: GoogleFonts.rubik(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.amber,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 30, vertical: 15),
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.amber.withValues(alpha: 0.5),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Text(
                        '+${widget.bonusPoints} POINTS',
                        style: GoogleFonts.rubik(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 50),

              // Continue button
              ElevatedButton(
                onPressed: widget.onContinue,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: AppColors.primary,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                  elevation: 5,
                ),
                child: Text(
                  'CONTINUE',
                  style: GoogleFonts.rubik(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
