import 'category.dart';

class WinningRules {
  final int level;
  final int winningPoints;
  final int rank;

  WinningRules({
    required this.level,
    required this.winningPoints,
    required this.rank,
  });

  factory WinningRules.fromJson(Map<String, dynamic> json) {
    return WinningRules(
      level: json['level'] ?? 1,
      winningPoints: json['winning_points'] ?? 0,
      rank: json['rank'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'level': level,
      'winning_points': winningPoints,
      'rank': rank,
    };
  }
}

class Challenge {
  final int id;
  final String name;
  final int winningPoints; // Kept for backward compatibility
  final dynamic reward;
  final int categoryId;
  final int? score;
  final int? status;
  final int? level;
  final int? rank;
  final Category? category;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? startDate; // Use DateTime? for null safety
  final DateTime? endDate; // Use DateTime? for null safety
  final WinningRules? winningRules; // New field for winning rules

  Challenge({
    required this.id,
    required this.name,
    required this.winningPoints,
    required this.reward,
    required this.categoryId,
    this.score,
    this.status,
    this.level,
    this.rank,
    this.category,
    required this.createdAt,
    required this.updatedAt,
    this.startDate,
    this.endDate,
    this.winningRules,
  });

  factory Challenge.fromJson(Map<String, dynamic> json) {
    try {
      // Parse winning_rules if available
      WinningRules? winningRules;
      if (json['winning_rules'] != null) {
        winningRules = WinningRules.fromJson(json['winning_rules']);
      }

      return Challenge(
        id: json['id'] ?? 0,
        name: json['name'] ?? '',
        // For backward compatibility, use winning_rules.winning_points if available
        winningPoints: json['winning_rules'] != null
            ? json['winning_rules']['winning_points'] ??
                json['winning_points'] ??
                0
            : json['winning_points'] ?? 0,
        reward: json['reward'] ?? 0,
        categoryId: json['category_id'] ?? 0,
        score: json['score'],
        status: json['status'],
        level: json['level'],
        rank: json['rank'],
        startDate: json['start_date'] != null
            ? DateTime.tryParse(json['start_date'])
            : null,
        endDate: json['end_date'] != null
            ? DateTime.tryParse(json['end_date'])
            : null,
        category: json['category'] != null
            ? Category.fromJson(json['category'])
            : null,
        createdAt: json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : DateTime.now(),
        updatedAt: json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : DateTime.now(),
        winningRules: winningRules,
      );
    } catch (e) {
      print('Error parsing Challenge: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'winning_points': winningPoints,
      'reward': reward,
      'category_id': categoryId,
      'score': score,
      'status': status,
      'level': level,
      'rank': rank,
      'start_date': startDate?.toIso8601String() ?? '',
      'end_date': endDate?.toIso8601String() ?? '',
      'category': category?.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'winning_rules': winningRules?.toJson(),

    };
  }
}
