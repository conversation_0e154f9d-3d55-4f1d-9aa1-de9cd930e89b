import 'package:achawach/core/constants/constants.dart';
import 'package:achawach/main.dart';
import 'package:achawach/routers/app_router.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DioClient {
  static final String _baseUrl =
      AppConstants.apiBaseUrl; // <-- IMPORTANT: Set your base URL here

  // Private constructor
  DioClient._();

  static final Dio _dio = _createDio();

  static Dio get instance => _dio;

  static Dio _createDio() {
    final dio = Dio(BaseOptions(
      baseUrl: _baseUrl,
      receiveTimeout: const Duration(milliseconds: 15000), // 15 seconds
      connectTimeout: const Duration(milliseconds: 15000),
      sendTimeout: const Duration(milliseconds: 15000),
    ));

    // Add the interceptor
    dio.interceptors.add(
      InterceptorsWrapper(
        // Step 1: Add the token to every request
        onRequest: (options, handler) async {
          final prefs = await SharedPreferences.getInstance();
          final token = prefs.getString('auth_token');
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          return handler.next(options);
        },
        // Step 2: Handle errors globally
        onError: (DioException e, handler) async {
          // Check for 401 Unauthorized status code
          if (e.response?.statusCode == 401) {
            debugPrint("Dio Interceptor: Token expired (401). Logging out.");
            await _performLogout();
            // We can resolve the error to prevent it from propagating
            // further and causing an unhandled exception in the UI.
            // handler.resolve(Response(requestOptions: e.requestOptions, data: null, statusCode: 200));
            // Or just let it continue as an error, which is often better.
            return handler.next(e);
          }
          // For all other errors, just continue.
          return handler.next(e);
        },
      ),
    );

    return dio;
  }

  static Future<void> _performLogout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');

    navigatorKey.currentState?.pushNamedAndRemoveUntil(
      AppRouter.loginRoute,
      (Route<dynamic> route) => false,
    );
  }
}
