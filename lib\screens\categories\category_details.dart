import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/models/challenge.dart';
import 'package:achawach/providers/challenge_provider.dart';
import 'package:achawach/screens/challenges/challenge.dart';
import 'package:achawach/widgets/cool_app_bar.dart';
import 'package:achawach/widgets/drawer.dart';
import 'package:achawach/widgets/full_page_loader.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

class CategoryDetails extends StatefulWidget {
  final Map<String, dynamic> selectedChallengeCategories;
  const CategoryDetails({super.key, required this.selectedChallengeCategories});

  @override
  State<CategoryDetails> createState() => _CategoryDetailsState();
}

class _CategoryDetailsState extends State<CategoryDetails>
    with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late TabController _tabController;
  late AnimationController _tabAnimationController;

  int _selectedTabIndex = 0;
  int _categoryId = 0;
  final DateFormat _dateFormatter = DateFormat('MMM d, yyyy');
  bool _isInitialLoading = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _tabController = TabController(
      length: 2,
      vsync: this,
      animationDuration: const Duration(milliseconds: 300),
    );
    _tabController.addListener(_handleTabSelection);
    _categoryId = widget.selectedChallengeCategories['id'];
    _loadChallenges();
  }

  void _handleTabSelection() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _selectedTabIndex = _tabController.index;
      });
      _tabAnimationController.forward(from: 0.0);
    }
  }

  // In _CategoryDetailsState class

  Future<void> _loadChallenges() async {
    final challengeProvider =
        Provider.of<ChallengeProvider>(context, listen: false);
    try {
      // 1. START the loading process. This shows the spinner.
      challengeProvider.prepareForRefresh();

      // 2. FETCH all necessary data. The provider is now silent and won't cause intermediate redraws.
      await challengeProvider.fetchNotStartedChallenges(_categoryId);
      if (mounted) {
        await challengeProvider.fetchStartedChallenges(_categoryId);
      }

      // Add minimum loading time for smooth UX on initial load
      if (_isInitialLoading) {
        await Future.delayed(const Duration(milliseconds: 1500));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading challenges: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      // If there's an error, we should still stop the loading indicator
    } finally {
      // 3. STOP the loading process. This hides the spinner and shows the final data.
      if (mounted) {
        challengeProvider.stopLoading();
        setState(() {
          _isInitialLoading = false;
        });
      }
    }
  }

  // Future<void> _loadChallenges() async {
  //   try {
  //     final challengeProvider =
  //         Provider.of<ChallengeProvider>(context, listen: false);

  //     // Load challenges sequentially to avoid race conditions
  //     await challengeProvider.fetchNotStartedChallenges(_categoryId);
  //     if (mounted) {
  //       await challengeProvider.fetchStartedChallenges(_categoryId);
  //     }
  //   } catch (e) {
  //     if (mounted) {
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         SnackBar(
  //           content: Text('Error loading challenges: ${e.toString()}'),
  //           backgroundColor: Colors.red,
  //         ),
  //       );
  //     }
  //   }
  // }

  void _setupAnimations() {
    // Main content animations
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.35),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    // Tab indicator animations
    _tabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabAnimationController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Show initial loading screen
    if (_isInitialLoading) {
      return const FullPageLoader(
        title: 'Loading Category...',
        subtitle: 'Preparing your challenge collection',
        icon: Icons.category_rounded,
      );
    }

    // Validate required data
    if (widget.selectedChallengeCategories['id'] == null ||
        widget.selectedChallengeCategories['name'] == null) {
      return Scaffold(
        appBar: CoolAppBar(
          title: 'Error',
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: AppColors.text),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
              const SizedBox(height: 16),
              Text(
                'Invalid category data',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      key: _scaffoldKey,
      appBar: CoolAppBar(
        title: widget.selectedChallengeCategories['name'] ?? '',
        leading: IconButton(
          icon: const Icon(Icons.menu, color: AppColors.text),
          onPressed: () {
            _scaffoldKey.currentState?.openDrawer();
          },
        ),
      ),
      drawer: const AppDrawer(),
      body: Consumer<ChallengeProvider>(
        builder: (context, challengeProvider, child) {
          if (challengeProvider.isLoading && !_isInitialLoading) {
            return const DataLoader(
              message: 'Refreshing challenges...',
            );
          }

          if (challengeProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48,
                    color: Colors.red.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Oops! Something went wrong',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      challengeProvider.error!,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: _loadChallenges,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Try Again'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue.shade50,
                  Colors.purple.shade50,
                ],
              ),
            ),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        _buildTabButton(0, 'New Quests', Icons.star_border),
                        _buildTabButton(1, 'Your Journey', Icons.route),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildChallengeList(
                        challengeProvider.notStartedChallenges,
                        'No new quests available',
                        'Be the first to embark on new adventures!',
                      ),
                      _buildChallengeList(
                        challengeProvider.startedChallenges,
                        'No active quests',
                        'Start a new challenge to begin your journey!',
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTabButton(int index, String title, IconData icon) {
    final isSelected = _selectedTabIndex == index;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          _tabController.animateTo(index);
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary : Colors.transparent,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.white : Colors.grey,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.poppins(
                  color: isSelected ? Colors.white : Colors.grey,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChallengeList(
      List<Challenge> challenges, String emptyTitle, String emptySubtitle) {
    if (challenges.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.explore,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              emptyTitle,
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              emptySubtitle,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ListView.builder(
          padding: const EdgeInsets.only(
              left: 16,
              right: 16,
              top: 16,
              bottom: 80), // Add bottom padding if needed
          itemCount: challenges.length,
          itemBuilder: (context, index) {
            return _buildChallengeCard(
                challenges[index]); // Call the updated card builder
          },
        ),
      ),
    );
  }

  // New implementation with consolidated winning conditions
  Widget _buildChallengeCard(Challenge challenge) {
    final bool isNotStarted = challenge.status == null;
    final bool isStarted = challenge.status == 1;
    final bool isCompleted = challenge.status == 2;
    final DateTime now = DateTime.now();

    String statusText;
    if (isNotStarted) {
      statusText = 'New';
    } else if (isStarted) {
      statusText = 'Started';
    } else {
      statusText = 'Finished';
    }

    // Calculate Time Remaining
    String timeRemainingText = '';
    if (isStarted &&
        challenge.endDate != null &&
        challenge.endDate!.isAfter(now)) {
      Duration remaining = challenge.endDate!.difference(now);
      if (remaining.inDays > 0) {
        timeRemainingText = '${remaining.inDays}d left';
      } else if (remaining.inHours > 0) {
        timeRemainingText = '${remaining.inHours}h left';
      } else if (remaining.inMinutes > 0) {
        timeRemainingText = '${remaining.inMinutes}m left';
      } else {
        timeRemainingText = 'Ends soon';
      }
    } else if (isNotStarted &&
        challenge.startDate != null &&
        challenge.startDate!.isAfter(now)) {
      Duration timeUntilStart = challenge.startDate!.difference(now);
      if (timeUntilStart.inDays > 1) {
        timeRemainingText = 'Starts in ${timeUntilStart.inDays}d';
      } else if (timeUntilStart.inHours > 0) {
        timeRemainingText = 'Starts in ${timeUntilStart.inHours}h';
      } else if (timeUntilStart.inMinutes > 0) {
        timeRemainingText = 'Starts soon';
      }
    } else if (challenge.endDate != null && challenge.endDate!.isBefore(now)) {
      timeRemainingText = 'Ended';
    }

    return Hero(
      tag: 'challenge_${challenge.id}',
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.white.withValues(alpha: 0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () async {
              // Navigate to the detail screen and wait for the user to return.
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ChallengeScreen(
                    selectedChallenge: {
                      'id': challenge.id,
                      'name': challenge.name,
                      'reward': challenge.reward,
                      'status': challenge.status,
                      'score': challenge.score ?? 0,
                      'categoryId': _categoryId,
                      'startDate': challenge.startDate?.toIso8601String(),
                      'endDate': challenge.endDate?.toIso8601String(),
                      'rank': challenge.rank ?? 0,
                      'level': challenge.level ?? 0,
                      'winning_rules': {
                        'level': challenge.winningRules!.level,
                        'winning_points': challenge.winningRules!.winningPoints,
                        'rank': challenge.winningRules!.rank
                      },
                    },
                  ),
                ),
              );

              // THIS CODE RUNS WHEN YOU COME BACK to the CategoryDetails screen.

              // 2. Asynchronously fetch the fresh data. The provider will handle
              //    updating the UI when the data arrives.
              _loadChallenges();

              // Your existing logic to switch tabs can remain.
              if (result != null &&
                  result is Map<String, dynamic> &&
                  result['challengeStarted'] == true) {
                _tabController.animateTo(1);
              }
            },
            borderRadius: BorderRadius.circular(24),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Challenge name and status
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          challenge.name,
                          style: GoogleFonts.poppins(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black.withValues(alpha: 0.9),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 8),
                      _buildStatusChip(statusText, timeRemainingText),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Date information (if available)
                  if (challenge.startDate != null || challenge.endDate != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Row(
                        children: [
                          if (challenge.startDate != null)
                            Expanded(
                              child: _buildDateItem(
                                Icons.calendar_today_outlined,
                                isNotStarted ? "Starts" : "Started",
                                _dateFormatter.format(challenge.startDate!),
                                Colors.blueGrey,
                              ),
                            ),
                          if (challenge.startDate != null &&
                              challenge.endDate != null)
                            const SizedBox(width: 12),
                          if (challenge.endDate != null)
                            Expanded(
                              child: _buildDateItem(
                                Icons.timer_off_outlined,
                                "Ends",
                                _dateFormatter.format(challenge.endDate!),
                                Colors.redAccent,
                              ),
                            ),
                        ],
                      ),
                    ),

                  // Reward and Score section
                  Row(
                    children: [
                      Expanded(
                        child: _buildDetailItem(
                          "💰 Reward",
                          "${NumberFormat("#,##0.00", "en_US").format(challenge.reward ?? 0)} ETB",
                          Colors.green.withValues(alpha: 0.8),
                        ),
                      ),
                      const SizedBox(width: 12),
                      if (isStarted || isCompleted)
                        Expanded(
                          child: _buildDetailItem(
                            "📊 Score",
                            (challenge.score ?? 0).toString(),
                            Colors.blue.withValues(alpha: 0.8),
                          ),
                        )
                      else
                        Expanded(
                          child: Container(),
                        ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Consolidated winning conditions section
                  _buildWinningConditions(
                    winningPoints: challenge.winningRules!.winningPoints,
                    levelReq: challenge.winningRules!.level,
                    rankReq: challenge.winningRules!.rank,
                    currentScore: challenge.score ?? 0,
                    isStarted: isStarted,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // New Helper Widget for Date Display
  Widget _buildDateItem(
      IconData icon, String label, String value, Color iconColor) {
    return Row(
      mainAxisSize: MainAxisSize.min, // Take minimum space needed
      children: [
        Icon(icon, size: 16, color: iconColor.withValues(alpha: 0.8)),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 11, // Smaller label
                color: Colors.black.withValues(alpha: 0.6),
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 13, // Slightly smaller date value
                fontWeight: FontWeight.w600,
                color: Colors.black.withValues(alpha: 0.85),
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _buildDetailItem(String label, String value, Color accentColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            accentColor.withValues(alpha: 0.1),
            accentColor.withValues(alpha: 0.15),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: accentColor.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: accentColor.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: accentColor.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Text(
              label.split(' ').last.length > 1
                  ? label.split(' ').last[0]
                  : label[0],
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: accentColor,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    color: Colors.black.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  value,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black.withValues(alpha: 0.95),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Modify Status Chip to optionally include time remaining
  Widget _buildStatusChip(String status, String timeRemaining) {
    Color color;
    IconData icon;
    switch (status.toLowerCase()) {
      case 'new':
        color = Colors.green;
        icon = Icons.fiber_new_rounded;
        break;
      case 'started':
        color = Colors.orange;
        icon = Icons.play_circle_filled_rounded;
        break;
      default: // Completed or Ended
        color = Colors.blue;
        icon = Icons.check_circle_rounded;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
          horizontal: 10, vertical: 5), // Adjust padding
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16), // Slightly smaller icon
          const SizedBox(width: 4),
          Text(
            status,
            style: GoogleFonts.poppins(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12, // Slightly smaller text
            ),
          ),
          // Show time remaining if available
          if (timeRemaining.isNotEmpty) ...[
            Text(
              ' • $timeRemaining', // Separator
              style: GoogleFonts.poppins(
                color: color.withValues(alpha: 0.9),
                fontWeight: FontWeight.w500, // Less emphasis
                fontSize: 11, // Smaller text
              ),
            ),
          ]
        ],
      ),
    );
  }

  // New method to build consolidated winning conditions
  Widget _buildWinningConditions({
    required int winningPoints,
    required int levelReq,
    required int rankReq,
    required int currentScore,
    required bool isStarted,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.shade50,
            Colors.purple.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with trophy icon
          Row(
            children: [
              Icon(
                Icons.emoji_events_outlined,
                color: Colors.amber.shade700,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Winning Conditions',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Winning conditions in a row
          Row(
            children: [
              _buildConditionChip(
                icon: Icons.star,
                label: 'Points',
                value: '$winningPoints',
                color: Colors.amber,
              ),
              const SizedBox(width: 8),
              _buildConditionChip(
                icon: Icons.trending_up,
                label: 'Level',
                value: '$levelReq',
                color: Colors.purple,
              ),
              const SizedBox(width: 8),
              _buildConditionChip(
                icon: Icons.emoji_events,
                label: 'Rank',
                value: '$rankReq',
                color: Colors.indigo,
              ),
            ],
          ),

          // Progress bar (only for started challenges)
          if (isStarted) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Text(
                  'Progress: ',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  '$currentScore/$winningPoints',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: LinearProgressIndicator(
                value: currentScore / winningPoints.toDouble(),
                backgroundColor: Colors.grey[200],
                valueColor: AlwaysStoppedAnimation<Color>(
                  Colors.blue.shade400,
                ),
                minHeight: 8,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Helper method for condition chips in the winning conditions section
  Widget _buildConditionChip({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color.withValues(alpha: 0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
