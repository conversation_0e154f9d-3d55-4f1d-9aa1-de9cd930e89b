import 'package:audioplayers/audioplayers.dart';
import 'package:video_player/video_player.dart';
import 'package:flutter/material.dart';
import 'package:achawach/models/question.dart';
import 'package:achawach/providers/settings_provider.dart';
import 'package:provider/provider.dart';

class MediaService {
  final BuildContext context;
  final AudioPlayer soundPlayer = AudioPlayer();
  final AudioPlayer questionAudioPlayer = AudioPlayer();
  VideoPlayerController? videoController;
  bool _soundEnabled = true;

  MediaService({required this.context}) {
    _initializeAudio();
  }

  // Getters
  bool get soundEnabled => _soundEnabled;
  VideoPlayerController? get currentVideoController => videoController;

  // Initialize audio settings
  Future<void> _initializeAudio() async {
    try {
      final settingsProvider =
          Provider.of<SettingsProvider>(context, listen: false);
      _soundEnabled = settingsProvider.isSoundEnabled;

      // Pre-load common sound effects
      if (_soundEnabled) {
        try {
          final soundPlayer = AudioPlayer();
          await soundPlayer.setSourceAsset('sounds/correct.wav');
        } catch (e) {
          // Continue without sound
        }
      }
    } catch (e) {
      // Continue without sound effects
      _soundEnabled = false;
    }
  }

  // Initialize media for a specific question
  Future<void> initializeMediaForQuestion(Question question) async {
    // Dispose previous media
    await disposeMedia();

    // Handle audio questions
    if (question.type == 'audio' && question.filePath.isNotEmpty) {
      try {
        await questionAudioPlayer.setSourceAsset(question.filePath);
      } catch (e) {
        // Handle audio initialization error
      }
    }

    // Handle video questions
    else if (question.type == 'video' && question.filePath.isNotEmpty) {
      try {
        videoController = VideoPlayerController.asset(question.filePath);
        await videoController?.initialize();
        videoController?.setLooping(true);
        videoController?.play();
      } catch (e) {
        // Handle video initialization error
      }
    }
  }

  // Play correct answer sound
  Future<void> playCorrectSound() async {
    if (_soundEnabled) {
      try {
        final soundPlayer = AudioPlayer();
        await soundPlayer.play(AssetSource('sounds/correct.wav'));
      } catch (e) {
        // Continue without sound
      }
    }
  }

  Future<void> playEndGameSound() async {
    if (_soundEnabled) {
      try {
        final soundPlayer = AudioPlayer();
        await soundPlayer.play(AssetSource('sounds/endGame.mp3'));
      } catch (e) {
        // Continue without sound
      }
    }
  }

  // stop answer sound
  Future<void> stopSound() async {
    if (_soundEnabled) {
      try {
        final soundPlayer = AudioPlayer();
        await soundPlayer.stop();
      } catch (e) {
        // Continue without sound
      }
    }
  }

  // Play wrong answer sound
  Future<void> playWrongSound() async {
    if (_soundEnabled) {
      try {
        final soundPlayer = AudioPlayer();
        await soundPlayer.play(AssetSource('sounds/wrong.wav'));
      } catch (e) {
        // Continue without sound
      }
    }
  }

  Future<void> playLost() async {
    if (_soundEnabled) {
      try {
        final soundPlayer = AudioPlayer();
        await soundPlayer.play(AssetSource('sounds/lost.wav'));
      } catch (e) {
        // Continue without sound
      }
    }
  }

  // Play wrong answer sound
  Future<void> playlevelSound() async {
    if (_soundEnabled) {
      try {
        final soundPlayer = AudioPlayer();
        await soundPlayer.play(AssetSource('sounds/level.mp3'));
      } catch (e) {
        // Continue without sound
      }
    }
  }

  // Play timeout sound
  Future<void> playTimeoutSound() async {
    if (_soundEnabled) {
      try {
        final soundPlayer = AudioPlayer();
        await soundPlayer.play(AssetSource('sounds/timeout.mp3'));
      } catch (e) {
        // Continue without sound
      }
    }
  }

  // Dispose media resources
  Future<void> disposeMedia() async {
    try {
      await questionAudioPlayer.stop();
      videoController?.pause();
      videoController?.dispose();
      videoController = null;
    } catch (e) {
      // Handle disposal error
    }
  }

  // Dispose all resources
  Future<void> dispose() async {
    try {
      await soundPlayer.dispose();
      await questionAudioPlayer.dispose();
      await disposeMedia();
    } catch (e) {
      // Handle disposal error
    }
  }
}
