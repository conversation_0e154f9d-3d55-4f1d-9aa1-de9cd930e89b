import 'package:flutter/material.dart';

class LevelService extends ChangeNotifier {
  int _currentLevel = 1;
  int _score = 0;
  bool _isLevelUpPending = false;
  int _levelUpBonusPoints = 0;
  int _oldLevel = 1;
  int _bonus = 0;

  // Getters
  int get currentLevel => _currentLevel;
  int get score => _score;
  int get bonus => _bonus;
  bool get isLevelUpPending => _isLevelUpPending;
  int get levelUpBonusPoints => _levelUpBonusPoints;
  int get oldLevel => _oldLevel;

  // Update level and score based on answer result
  void updateProgress(Map<String, dynamic> progress, bool leveledUp) {
    _currentLevel = progress['level'] ?? _currentLevel;
    _score = progress['score'] ?? _score;

    // Properly handle the bonus value with type checking
    if (progress.containsKey('bonus')) {
      if (progress['bonus'] is int) {
        _levelUpBonusPoints = progress['bonus'];
        _bonus = progress['bonus']; // Also update the regular bonus field
      } else if (progress['bonus'] != null) {
        // Try to parse it as an int if it's not null but not an int
        _levelUpBonusPoints = int.tryParse(progress['bonus'].toString()) ?? 0;
        _bonus = _levelUpBonusPoints;
      }
      print('LevelService: Updated bonus to $_levelUpBonusPoints');
    }

    if (leveledUp) {
      _isLevelUpPending = true;
      _oldLevel =
          _currentLevel - 1; // Assuming level has already been incremented
      print(
          'LevelService: Level up pending. Old level: $_oldLevel, New level: $_currentLevel, Bonus: $_levelUpBonusPoints');
    }

    notifyListeners();
  }

  // Mark level up as handled
  void acknowledgeLevelUp() {
    _isLevelUpPending = false;
    notifyListeners();
  }
}
