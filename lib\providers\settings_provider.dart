import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider extends ChangeNotifier {
  bool _isDarkMode = false;
  bool _isSoundEnabled = true;
  
  // Keys for SharedPreferences
  static const String _darkModeKey = 'dark_mode';
  static const String _soundEnabledKey = 'sound_enabled';
  
  // Getters
  bool get isDarkMode => _isDarkMode;
  bool get isSoundEnabled => _isSoundEnabled;
  
  // Constructor - Load settings from SharedPreferences
  SettingsProvider() {
    _loadSettings();
  }
  
  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _isDarkMode = prefs.getBool(_darkModeKey) ?? false;
    _isSoundEnabled = prefs.getBool(_soundEnabledKey) ?? true;
    notifyListeners();
  }
  
  // Toggle dark mode
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_darkModeKey, _isDarkMode);
    notifyListeners();
  }
  
  // Toggle sound
  Future<void> toggleSound() async {
    _isSoundEnabled = !_isSoundEnabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_soundEnabledKey, _isSoundEnabled);
    notifyListeners();
  }
  
  // Set dark mode
  Future<void> setDarkMode(bool value) async {
    if (_isDarkMode != value) {
      _isDarkMode = value;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_darkModeKey, _isDarkMode);
      notifyListeners();
    }
  }
  
  // Set sound enabled
  Future<void> setSoundEnabled(bool value) async {
    if (_isSoundEnabled != value) {
      _isSoundEnabled = value;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_soundEnabledKey, _isSoundEnabled);
      notifyListeners();
    }
  }
}
