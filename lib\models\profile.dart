class Profile {
  final int id;
  final String fullName;
  final String phoneNumber;
  final int? isProfileImageAvailable;
  final String? avatar;
  final String? profileImagePath;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final double? totalEarned;

  Profile(
      {required this.id,
      required this.fullName,
      required this.phoneNumber,
      required this.isProfileImageAvailable,
      required this.avatar,
      this.profileImagePath,
      required this.createdAt,
      this.updatedAt,
      this.totalEarned});

  factory Profile.fromJson(Map<String, dynamic> json) {
    return Profile(
      id: json['id'],
      fullName: json['fullName'],
      phoneNumber: json['phoneNumber'],
      isProfileImageAvailable: json['isProfileImageAvailable'],
      avatar: json['avatar'],
      profileImagePath: json['profileImagePath'],
      createdAt: DateTime.parse(json['createdAt']),
      totalEarned: json['totalEarned'] != null
          ? double.tryParse(json['totalEarned'].toString())
          : null,
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullName': fullName,
      'phoneNumber': phoneNumber,
      'isProfileImageAvailable': isProfileImageAvailable,
      'avatar': avatar,
      'profileImagePath': profileImagePath,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'totalEarned': totalEarned,
    };
  }
}
