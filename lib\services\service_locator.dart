import 'package:flutter/material.dart';
import 'package:achawach/services/question_service.dart';
import 'package:achawach/services/timer_service.dart';
import 'package:achawach/services/life_service.dart';
import 'package:achawach/services/level_service.dart';
import 'package:achawach/services/media_service.dart';
import 'package:achawach/services/game_end_service.dart';

class ServiceLocator {
  // Singleton instance
  static final ServiceLocator _instance = ServiceLocator._internal();

  factory ServiceLocator() {
    return _instance;
  }

  ServiceLocator._internal();

  // Services
  QuestionService? _questionService;
  TimerService? _timerService;
  LifeService? _lifeService;
  LevelService? _levelService;
  MediaService? _mediaService;
  GameEndService? _gameEndService;

  // Initialize services for a challenge
  void initializeServices({
    required BuildContext context,
    required int challengeId,
    required int categoryId,
  }) {
    _questionService = QuestionService(
      context: context,
      challengeId: challengeId,
      categoryId: categoryId,
    );

    _timerService = TimerService();

    _lifeService = LifeService(
      context: context,
      challengeId: challengeId,
      categoryId: categoryId,
    );

    _levelService = LevelService();

    _mediaService = MediaService(context: context);

    // Initialize the game end service
    _gameEndService = GameEndService.instance;
    _gameEndService?.initialize();
  }

  // Get services
  QuestionService get questionService {
    if (_questionService == null) {
      throw Exception(
          'QuestionService not initialized. Call initializeServices first.');
    }
    return _questionService!;
  }

  TimerService get timerService {
    if (_timerService == null) {
      throw Exception(
          'TimerService not initialized. Call initializeServices first.');
    }
    return _timerService!;
  }

  LifeService get lifeService {
    if (_lifeService == null) {
      throw Exception(
          'LifeService not initialized. Call initializeServices first.');
    }
    return _lifeService!;
  }

  LevelService get levelService {
    if (_levelService == null) {
      throw Exception(
          'LevelService not initialized. Call initializeServices first.');
    }
    return _levelService!;
  }

  MediaService get mediaService {
    if (_mediaService == null) {
      throw Exception(
          'MediaService not initialized. Call initializeServices first.');
    }
    return _mediaService!;
  }

  GameEndService get gameEndService {
    if (_gameEndService == null) {
      throw Exception(
          'GameEndService not initialized. Call initializeServices first.');
    }
    return _gameEndService!;
  }

  // Dispose all services
  void disposeServices() {
    _timerService?.dispose();
    _lifeService?.dispose();
    _levelService?.dispose();
    _mediaService?.dispose();
    _gameEndService?.dispose();

    _questionService = null;
    _timerService = null;
    _lifeService = null;
    _levelService = null;
    _mediaService = null;
    _gameEndService = null;
  }
}
