import 'dart:async';
import 'package:achawach/services/media_service.dart';
import 'package:achawach/widgets/game_end_screen.dart';
import 'package:flutter/material.dart';
import 'package:achawach/services/socket_service.dart';
import 'package:shared_preferences/shared_preferences.dart';


class GameEndService {
  final SocketService _socketService;
  StreamSubscription? _gameEndedSubscription;

  // Singleton pattern
  static GameEndService? _instance;
  static GameEndService get instance {
    _instance ??= GameEndService._();
    return _instance!;
  }

  // Private constructor
  GameEndService._() : _socketService = SocketService.instance;

  // For testing and dependency injection
  GameEndService.withSocketService(this._socketService);

  // Flag to track if game end screen is currently showing
  bool _isGameEndScreenShowing = false;

  /// Initialize the service by setting up listeners for game end events
  ///
  /// This method sets up listeners for gameEnded and winnerAnnouncement events.
  /// When these events are received, they will be processed and stored for later use.
  /// The actual display of the game end screen is handled by the showGameEndScreen method,
  /// which is called by the UI components when they receive these events.
  void initialize() {
    // Cancel any existing subscriptions to prevent memory leaks
    _gameEndedSubscription?.cancel();

    // Listen for gameEnded events (personal win/loss)
    _gameEndedSubscription = _socketService.gameEndedStream.listen((data) {
      // Data validation is handled by the socket service
      // We can assume data is already a Map<String, dynamic>

      // Process the data - in this case, we're just logging it
      // The actual display of the game end screen is handled by the UI components
      // that call showGameEndScreen when they receive this event

      // Log the game end event details
    });
  }

  /// Show the game end screen based on the received data
  ///
  /// This method handles displaying the game end screen with the provided data.
  /// It prevents multiple game end screens from being shown simultaneously.
 
  // In GameEndService::showGameEndScreen
  void showGameEndScreen(BuildContext context, Map<String, dynamic> data,
      MediaService mediaService,
      {bool isWinnerAnnouncement = false}) {
    // Prevent showing multiple screens
    if (_isGameEndScreenShowing) return;
    _isGameEndScreenShowing = true;

    // Safely extract data with defaults
    final bool isWinner =
        isWinnerAnnouncement ? false : data['isWinner'] ?? false;
    final String message = data['message'] ?? 'Game has ended!';
    final Map<String, dynamic> finalState = data['finalState'] is Map
        ? Map<String, dynamic>.from(data['finalState'])
        : {};

    int? winnerId;
    try {
      winnerId = data['winnerId'] != null
          ? int.parse(data['winnerId'].toString())
          : null;
    } catch (e) {
      debugPrint('Error parsing winnerId: $e');
    }

    int? challengeId;
    try {
      challengeId = data['challengeId'] != null
          ? int.parse(data['challengeId'].toString())
          : null;
    } catch (e) {
      debugPrint('Error parsing challengeId: $e');
    }

    int? winnerScore;
    try {
      winnerScore = data['winnerScore'] != null
          ? int.parse(data['winnerScore'].toString())
          : null;
    } catch (e) {
      debugPrint('Error parsing winnerScore: $e');
    }

    int? winnerRank;
    try {
      winnerRank = data['winnerRank'] != null
          ? int.parse(data['winnerRank'].toString())
          : null;
    } catch (e) {
      debugPrint('Error parsing winnerRank: $e');
    }

    int? winnerLevel;
    try {
      winnerLevel = data['winnerLevel'] != null
          ? int.parse(data['winnerLevel'].toString())
          : null;
    } catch (e) {
      debugPrint('Error parsing winnerLevel: $e');
    }

    mediaService.playEndGameSound();
    // Navigate to the GameEndScreen using a regular push
    Navigator.of(context)
        .push(
      MaterialPageRoute(
        builder: (context) => GameEndScreen(
          isWinner: isWinner,
          message: message,
          finalState: finalState,
          winnerMessage: data['winnerMessage'],
          loserMessage: data['loserMessage'],
          winnerId: winnerId,
          challengeId: challengeId,
          winnerScore: winnerScore,
          winnerRank: winnerRank,
          winnerLevel: winnerLevel,
          onContinue: () {
            _isGameEndScreenShowing = false;
            mediaService.stopSound();
            Navigator.of(context).pop();
            Navigator.of(context).pop();
            Navigator.of(context).pushReplacementNamed('/home');
          },
        ),
      ),
    )
        .then((_) {
      // This block executes when the GameEndScreen is popped
      _isGameEndScreenShowing = false;
    });
  }

  /// End the current game by emitting the endGame event
  Future<void> endGame(int challengeId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? prefs.getString('token');

      if (token == null || token.isEmpty) {
        debugPrint('GameEndService: No token found for ending game');
        return;
      }

      // Log that the game has been explicitly ended
      debugPrint('GameEndService: Game has been explicitly ended');

      _socketService.endGame(token, challengeId.toString());
    } catch (e) {
      debugPrint('GameEndService: Error ending game: $e');
    }
  }

  /// Dispose the service by canceling subscriptions
  void dispose() {
    _gameEndedSubscription?.cancel();
    _isGameEndScreenShowing = false; // Reset the flag
    _instance = null;
  }
}
