// import 'package:dio/dio.dart';
// import 'package:flutter/material.dart';
// import 'package:achawach/core/constants/constants.dart';
// import 'package:achawach/core/utils/tokenManager.dart';
// import 'package:achawach/main.dart';

// class ApiClient {
//    final Dio dio = Dio(
//     BaseOptions(
//       baseUrl: AppConstants.apiBaseUrl, // Replace with your API URL
//       connectTimeout: const Duration(seconds: 10),
//       receiveTimeout: const Duration(seconds: 10),
//     ),
//   );

//   ApiClient() {
//     dio.interceptors.add(InterceptorsWrapper(
//       onRequest: (options, handler) async {
//         // Attach the token to headers
//         String? token = await TokenManager.getToken();
//         options.headers['Authorization'] = 'Bearer $token';
//               return handler.next(options);
//       },
//       onResponse: (response, handler) {
//         return handler.next(response);
//       },
//       onError: (DioException error, handler) async {
//         if (error.response?.statusCode == 401) {
//           // Token expired, clear token and redirect to login
//           await TokenManager.clearToken();
//           navigatorKey.currentState?.pushNamedAndRemoveUntil(
//               '/login', (Route<dynamic> route) => false);
//         }
//         return handler.next(error);
//       },
//     ));
//   }
// }
