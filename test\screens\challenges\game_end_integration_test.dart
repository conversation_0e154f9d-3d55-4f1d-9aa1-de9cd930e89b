import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:achawach/services/socket_service.dart';
import 'package:achawach/services/game_end_service.dart';
import 'package:achawach/widgets/game_end_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Import the manually created mock
import 'game_end_integration_test.mocks.dart';

// A simple widget that sets up game end listeners
class GameEndListenerWidget extends StatefulWidget {
  final SocketService socketService;
  final GameEndService gameEndService;

  const GameEndListenerWidget({
    super.key,
    required this.socketService,
    required this.gameEndService,
  });

  @override
  State<GameEndListenerWidget> createState() => _GameEndListenerWidgetState();
}

class _GameEndListenerWidgetState extends State<GameEndListenerWidget> {
  StreamSubscription? _gameEndedSubscription;
  StreamSubscription? _winnerAnnouncementSubscription;

  @override
  void initState() {
    super.initState();
    _setupGameEndListeners();
  }

  void _setupGameEndListeners() {
    // Cancel any existing subscriptions
    _gameEndedSubscription?.cancel();
    _winnerAnnouncementSubscription?.cancel();

    // Listen for game ended events
    _gameEndedSubscription =
        widget.socketService.gameEndedStream.listen((data) {
     
      if (mounted) {
        // Show the game end screen
        // widget.gameEndService.showGameEndScreen(context, data);
      }
    });

    // Listen for winner announcement events
    _winnerAnnouncementSubscription =
        widget.socketService.winnerAnnouncementStream.listen((data) {
     
      if (mounted) {
        // Show the winner announcement screen
        // widget.gameEndService
        //     .showGameEndScreen(context, data, isWinnerAnnouncement: true);
      }
    });
  }

  @override
  void dispose() {
    _gameEndedSubscription?.cancel();
    _winnerAnnouncementSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Game End Listener')),
      body: const Center(child: Text('Waiting for game end events...')),
    );
  }
}

void main() {
  group('Game End Integration Tests', () {
    late MockSocketService mockSocketService;
    late GameEndService gameEndService;

    setUp(() {
      // Create mock socket service
      mockSocketService = MockSocketService();

      // Create the service with the mock socket
      gameEndService = GameEndService.withSocketService(mockSocketService);

      // Setup SharedPreferences mock
      SharedPreferences.setMockInitialValues({'auth_token': 'test-token'});
    });

    tearDown(() {
      // Clean up
      mockSocketService.dispose();
    });

    testWidgets(
        'GameEndListenerWidget shows game end screen when gameEnded event is received',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: GameEndListenerWidget(
            socketService: mockSocketService,
            gameEndService: gameEndService,
          ),
        ),
      );

      // Verify that the listener widget is displayed
      expect(find.text('Waiting for game end events...'), findsOneWidget);

      // Create test data for a winner
      final winnerData = {
        'isWinner': true,
        'message': 'You won!',
        'finalState': {'score': 500, 'level': 3},
        'winnerMessage': 'Congratulations!',
        'loserMessage': 'Better luck next time!',
        'winnerId': 123,
        'challengeId': 456,
        'winnerScore': 500,
        'winnerRank': 1,
        'winnerLevel': 3,
      };

      // Add data to the stream to simulate receiving a gameEnded event
      mockSocketService.gameEndedController.add(winnerData);

      // Wait for the event to be processed
      await tester.pump(const Duration(milliseconds: 500));

      // Call the showGameEndScreen method directly to test the UI
      // gameEndService.showGameEndScreen(
      //     tester.element(find.byType(MaterialApp)), winnerData);

      // Wait for the UI to update
      await tester.pump(const Duration(milliseconds: 500));

      // Verify that the game end screen is shown
      expect(find.byType(GameEndScreen), findsOneWidget);
      expect(find.text('Congratulations!'), findsOneWidget);
    });

    testWidgets(
        'GameEndListenerWidget shows winner announcement screen when winnerAnnouncement event is received',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: GameEndListenerWidget(
            socketService: mockSocketService,
            gameEndService: gameEndService,
          ),
        ),
      );

      // Verify that the listener widget is displayed
      expect(find.text('Waiting for game end events...'), findsOneWidget);

      // Create test data for a winner announcement
      final winnerAnnouncementData = {
        'message': 'User 123 won the challenge!',
        'finalState': {'score': 500, 'level': 3},
        'winnerMessage': 'Congratulations!',
        'loserMessage': 'Better luck next time!',
        'winnerId': 123,
        'challengeId': 456,
        'winnerScore': 500,
        'winnerRank': 1,
        'winnerLevel': 3,
      };

      // Add data to the stream to simulate receiving a winnerAnnouncement event
      mockSocketService.winnerAnnouncementController
          .add(winnerAnnouncementData);

      // Wait for the event to be processed
      await tester.pump(const Duration(milliseconds: 500));

      // Call the showGameEndScreen method directly to test the UI
      // gameEndService.showGameEndScreen(
      //     tester.element(find.byType(MaterialApp)), winnerAnnouncementData,
      //     isWinnerAnnouncement: true);

      // Wait for the UI to update
      await tester.pump(const Duration(milliseconds: 500));

      // Verify that the game end screen is shown
      expect(find.byType(GameEndScreen), findsOneWidget);
      expect(find.text('User 123 won the challenge!'), findsOneWidget);
    });

    testWidgets('endGame method calls socket service with correct parameters',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () {
                  gameEndService.endGame(123);
                },
                child: const Text('End Game'),
              );
            },
          ),
        ),
      );

      // Tap the button to call endGame
      await tester.tap(find.text('End Game'));
      await tester.pump();

      // Verify that endGame was called on the socket service with correct parameters
      expect(mockSocketService.endGameToken, equals('test-token'));
      expect(mockSocketService.endGameChallengeId, equals('123'));
    });
  });
}
