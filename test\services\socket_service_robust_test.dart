import 'package:flutter_test/flutter_test.dart';
import 'package:achawach/services/socket_service.dart';

void main() {
  group('SocketService Robust Tests', () {
    late SocketService socketService;

    setUp(() {
      socketService = SocketService.instance;
    });

    tearDown(() {
      socketService.dispose();
    });

    test('SocketService handles disconnect properly when socket is null', () {
      // This test verifies that calling disconnect when socket is null doesn't throw
      socketService.disconnect();
      // If we get here without exceptions, the test passes
      expect(true, isTrue);
    });

    test('SocketService handles endGame properly when not connected', () {
      // This test verifies that calling endGame when not connected doesn't throw
      socketService.endGame('test-token', '123');
      // If we get here without exceptions, the test passes
      expect(true, isTrue);
    });

    test('SocketService handles requestLeaderboardRefresh properly when not connected', () {
      // This test verifies that calling requestLeaderboardRefresh when not connected doesn't throw
      socketService.requestLeaderboardRefresh('test-token', '123');
      // If we get here without exceptions, the test passes
      expect(true, isTrue);
    });

    test('SocketService properly exposes stream getters', () {
      // This test verifies that all stream getters are accessible
      expect(socketService.gameStateStream, isNotNull);
      expect(socketService.errorStream, isNotNull);
      expect(socketService.gameleaderBoardStream, isNotNull);
      expect(socketService.gameEndedStream, isNotNull);
      expect(socketService.winnerAnnouncementStream, isNotNull);
    });

    test('SocketService properly handles multiple dispose calls', () {
      // This test verifies that calling dispose multiple times doesn't throw
      socketService.dispose();
      socketService.dispose(); // Second call should not throw
      // If we get here without exceptions, the test passes
      expect(true, isTrue);
    });

    test('SocketService properly handles isConnected getter', () {
      // This test verifies that isConnected getter works properly
      expect(socketService.isConnected, isFalse);
    });
  });
}
