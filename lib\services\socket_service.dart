import 'package:achawach/core/constants/constants.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'dart:async';

// Remove shared_preferences import if not used elsewhere in this file
// import 'package:shared_preferences/shared_preferences.dart';

class SocketService {
  static SocketService? _instance;
  IO.Socket? _socket;

  // Use broadcast controllers so multiple widgets can listen if needed
  final _gameStateController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _errorController = StreamController<String>.broadcast();
  final _gameleaderBoardController = // Corrected typo: leaderBoard
      StreamController<Map<String, dynamic>>.broadcast();
  final _gameEndedController =
      StreamController<Map<String, dynamic>>.broadcast();
  final _winnerAnnouncementController =
      StreamController<Map<String, dynamic>>.broadcast();

  bool _isConnected = false;
  // Removed unused tokenKey constant
  // static const String tokenKey = 'auth_token';

  // Public streams
  Stream<Map<String, dynamic>> get gameStateStream =>
      _gameStateController.stream;
  Stream<String> get errorStream => _errorController.stream;
  Stream<Map<String, dynamic>> get gameleaderBoardStream =>
      _gameleaderBoardController.stream;
  Stream<Map<String, dynamic>> get gameEndedStream =>
      _gameEndedController.stream;
  Stream<Map<String, dynamic>> get winnerAnnouncementStream =>
      _winnerAnnouncementController.stream;

  // Connection status getter
  bool get isConnected => _isConnected;

  // Singleton pattern instance getter
  static SocketService get instance {
    _instance ??= SocketService._();
    return _instance!;
  }

  // Private constructor for Singleton
  SocketService._();

  /// Establishes connection to the socket server for a specific challenge.
  ///
  /// Emits 'joinGame' upon successful connection.
  /// Listens for 'gameState', 'gameleaderboardData', 'gameOver', and errors.
  Future<void> connect(String token, String challengeId) async {
    // Prevent multiple connection attempts if already connected/connecting
    // This check might need refinement depending on desired behavior on reconnect
    if (_socket?.connected ?? false) {
      // If already connected, just emit joinGame again for the new challenge
      // This allows reusing the same socket connection for different challenges
      _socket?.emit('joinGame', {'token': token, 'challengeId': challengeId});
      // We're not calling endGame here anymore to prevent the backend from emitting a challengeEnded event
      // endGame(token, challengeId);
      return;
    }

    // If there's a socket instance but it's not connected, just dispose it without disconnecting
    if (_socket != null) {
      try {
        _socket!.dispose();
      } catch (e) {}
      _socket = null;
    }

    try {
      _socket = IO.io(AppConstants.apiBaseIP, <String, dynamic>{
        'transports': ['websocket', 'polling'], // Prioritize websocket
        'autoConnect': false, // We manually call connect()
        'forceNew': true, // Consider if you need a fresh connection each time
        'auth': {'token': token}, // Send token for authentication
        'query': {
          'challengeId': challengeId
        }, // Optionally send challengeId in query too
        // 'debug': true, // Enable for verbose logging during development
      });

      // --- Setup Event Listeners BEFORE connecting ---

      _socket?.onConnect((_) {
        _isConnected = true;

        // Only emit joinGame. Server will handle sending initial state & leaderboard.
        _socket?.emit('joinGame', {'token': token, 'challengeId': challengeId});
        // We're not calling endGame here anymore to prevent the backend from emitting a challengeEnded event
        // endGame(token, challengeId);
        // *** REMOVED: Explicit leaderboard emit is no longer needed ***
        // _socket?.emit('leaderboard', {'token': token, 'challengeId': challengeId});
      });

      _socket?.on('gameState', (data) {
        if (data is Map) {
          // Ensure controller is not closed before adding
          if (!_gameStateController.isClosed) {
            _gameStateController.add(Map<String, dynamic>.from(data));
          }
        } else {}
      });

      _socket?.on('gameleaderboardData', (data) {
        if (data is Map) {
          // Ensure controller is not closed before adding
          if (!_gameleaderBoardController.isClosed) {
            try {
              // Safely convert to Map<String, dynamic>
              _gameleaderBoardController.add(Map<String, dynamic>.from(data));
            } catch (e) {
              // If conversion fails, create a new map with the data
              _gameleaderBoardController.add({'leaderboardData': []});
            }
          }
        } else {
          // Send an empty leaderboard if data is not in expected format
          if (!_gameleaderBoardController.isClosed) {
            _gameleaderBoardController.add({'leaderboardData': []});
          }
        }
      });

      _socket?.on('gameOver', (data) {
        // You might want a separate stream for game over events
        // For now, maybe add it to the error stream or handle differently?
        if (!_errorController.isClosed) {
          _errorController.add(
              'Game Over: ${data?['message'] ?? ''} Score: ${data?['finalScore'] ?? ''}');
        }
        // Optionally disconnect or perform other cleanup after game over
        // disconnect();
      });

      // Listen for gameEnded event (winner/loser status)
      _socket?.on('challengeEnded', (data) {
        if (data is Map) {
          if (!_gameEndedController.isClosed) {
            _gameEndedController.add(Map<String, dynamic>.from(data));
          }
        } else {}
      });

      _socket?.onConnectError((error) {
        _isConnected = false;

        if (!_errorController.isClosed) {
          _errorController.add('Connection error: $error');
        }
      });

      _socket?.on('connect_timeout', (timeout) {
        _isConnected = false;

        if (!_errorController.isClosed) {
          _errorController.add('Connection timeout.');
        }
      });

      _socket?.onError((error) {
        // General socket errors

        if (!_errorController.isClosed) {
          _errorController.add('Socket error: ${error.toString()}');
        }
      });

      _socket?.onDisconnect((reason) {
        _isConnected = false;

        if (!_errorController.isClosed) {
          // Avoid flooding with errors on intentional disconnects
          if (reason != 'io client disconnect') {
            _errorController.add('Socket disconnected: $reason');
          }
        }
      });

      // --- Attempt Connection ---

      _socket?.connect();
    } catch (e) {
      if (!_errorController.isClosed) {
        _errorController.add('Initialization error: $e');
      }
      // Ensure socket is null if initialization fails
      _socket = null;
      _isConnected = false;
    }
  }

  /// Disconnects the socket and removes listeners.
  ///
  /// This method should only be called when you're sure you want to completely
  /// disconnect the socket, such as when the app is closing or when the user
  /// is logging out. For normal navigation between screens, you should NOT
  /// call this method as it will break the socket connection.
  void disconnect() {
    // Check if socket exists before disconnecting
    if (_socket != null) {
      try {
        // First check if the socket is actually connected before disconnecting
        if (_socket!.connected) {
          _socket!.disconnect();
        } else {}

        // Always dispose resources regardless of connection state
        _socket!.dispose();
      } catch (e) {
      } finally {
        // Always clean up the socket reference and update connection state
        _socket = null;
        _isConnected = false;
      }
    } else {
      // This is not an error, just a log message for debugging
    }
  }

  /// Cleans up resources, closes streams, and disconnects the socket.
  /// Should be called when the service is no longer needed (e.g., app closing).
  void dispose() {
    disconnect(); // Ensure socket is disconnected

    // Close stream controllers to prevent memory leaks
    if (!_gameStateController.isClosed) {
      _gameStateController.close();
    }
    if (!_gameleaderBoardController.isClosed) {
      _gameleaderBoardController.close();
    }
    if (!_errorController.isClosed) {
      _errorController.close();
    }
    if (!_gameEndedController.isClosed) {
      _gameEndedController.close();
    }
    if (!_winnerAnnouncementController.isClosed) {
      _winnerAnnouncementController.close();
    }

    // If using Singleton, consider how to handle re-initialization if needed
    _instance = null; // Allow recreation if instance getter is called again
  }

  // Optional: Add a method for explicit leaderboard refresh if needed
  void requestLeaderboardRefresh(String token, String challengeId) {
    if (_socket?.connected ?? false) {
      _socket
          ?.emit('leaderboard', {'token': token, 'challengeId': challengeId});
    } else {
      if (!_errorController.isClosed) {
        _errorController.add('Cannot refresh leaderboard: Not connected.');
      }
    }
  }

  /// Emits the endGame event to the server
  void endGame(String token, String challengeId) {
    if (_socket?.connected ?? false) {
      _socket?.emit('endGame', {'token': token, 'challengeId': challengeId});
    } else {
      if (!_errorController.isClosed) {
        _errorController.add('Cannot end game: Not connected.');
      }
    }
  }
}
