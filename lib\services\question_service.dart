import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:achawach/models/question.dart';
import 'package:achawach/providers/challenge_provider.dart';

class QuestionService {
  final BuildContext context;
  final int challengeId;
  final int categoryId;
  
  List<Question> _questions = [];
  Map<String, dynamic>? _summary;
  String? _error;
  bool _isLoading = false;
  
  QuestionService({
    required this.context,
    required this.challengeId,
    required this.categoryId,
  });
  
  // Getters
  List<Question> get questions => _questions;
  Map<String, dynamic>? get summary => _summary;
  String? get error => _error;
  bool get isLoading => _isLoading;
  
  // Load questions from the challenge provider
  Future<void> loadQuestions() async {
    try {
      _isLoading = true;
      _error = null;
      
      final challengeProvider = Provider.of<ChallengeProvider>(context, listen: false);
      await challengeProvider.fetchChallengeQuestions(challengeId);
      
      _questions = challengeProvider.questions;
      _summary = challengeProvider.questionsSummary;
      _isLoading = false;
      
      if (_questions.isEmpty) {
        _error = 'No questions available for this challenge';
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
    }
  }
  
  // Submit an answer to a question
  Future<Map<String, dynamic>> submitAnswer(int questionId, int choiceId) async {
    final challengeProvider = Provider.of<ChallengeProvider>(context, listen: false);
    return await challengeProvider.submitAnswer(
      challengeId,
      questionId,
      choiceId,
      categoryId,
    );
  }
  
  // Initialize a new challenge
  Future<void> initializeChallenge(dynamic challengeStatus) async {
    try {
      // Check if this is a new challenge (status = 0 or 'New') or an existing one (status = 1 or 'Started')
      final bool isNewChallenge = challengeStatus == 0 ||
          challengeStatus == 'New' ||
          challengeStatus?.toString().toLowerCase() == 'new';

      if (isNewChallenge) {
        // For new challenges, we need to ensure the life data is initialized
        // by the backend before proceeding
        await Future.delayed(const Duration(milliseconds: 1500));

        // For new challenges, we need to make sure the challenge is started on the server
        try {
          final challengeProvider = Provider.of<ChallengeProvider>(context, listen: false);
          await challengeProvider.startChallenge(challengeId, categoryId);
        } catch (e) {
          // Continue anyway, as the challenge might already be started
        }
      }
    } catch (e) {
      // Handle initialization error
      _error = 'Error initializing challenge: $e';
    }
  }
}
