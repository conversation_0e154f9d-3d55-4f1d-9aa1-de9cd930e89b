# Challenge Screen Tests

This directory contains tests for the Challenge-related screens in the application.

## ChallengeScreen Test

The `challenge_screen_test.dart` file contains tests for the ChallengeScreen widget. Due to the complexity of the actual ChallengeScreen implementation and its dependencies on external services (ApiService, ApiRefillService, SocketService), we've created a simplified mock version of the ChallengeScreen for testing purposes.

### Test Approach

1. **Mock Implementation**: Instead of testing the actual ChallengeScreen, we've created a MockChallengeScreen that mimics the UI structure and behavior of the real ChallengeScreen without the external dependencies.

2. **Test Cases**:
   - Verify that challenge information (name, winning points, reward, status) is displayed correctly
   - Verify that winning conditions are displayed correctly
   - Verify that the progress section is displayed for started challenges
   - Verify that the correct button (Start/Continue Challenge) is displayed based on the challenge status
   - Verify that the button interaction works as expected

### Future Improvements

In a more comprehensive test suite, we would:

1. **Mock External Dependencies**: Create proper mocks for ApiService, ApiRefillService, SocketService, and other dependencies.

2. **Test Actual Implementation**: Test the actual ChallengeScreen implementation with mocked dependencies.

3. **Test Navigation**: Verify that navigation to other screens (ChallengeStartScreen, NoLivesScreen) works correctly.

4. **Test Error Handling**: Verify that error states are handled correctly.

5. **Test Life Cycle**: Verify that the screen properly cleans up resources (e.g., cancels subscriptions) when disposed.

## Running the Tests

To run the tests, use the following command:

```bash
flutter test test/screens/challenges/challenge_screen_test.dart
```
