import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:achawach/providers/challenge_provider.dart';

class LifeService extends ChangeNotifier {
  final BuildContext context;
  final int challengeId;
  final int categoryId;

  Map<String, dynamic>? _lifeData = {'life': 3, 'max': 10};

  LifeService({
    required this.context,
    required this.challengeId,
    required this.categoryId,
  });

  // Getters
  Map<String, dynamic>? get lifeData => _lifeData;
  int get currentLife => _lifeData?['life'] ?? 0;
  int get maxLife => _lifeData?['max'] ?? 10;

  // Update life data
  void updateLifeData(Map<String, dynamic> newLifeData) {
    _lifeData = newLifeData;
    notifyListeners();
  }

  // Check if the user has lives left
  bool hasLivesLeft() {
    return currentLife > 0;
  }

  // Decrease life when a question times out or is answered incorrectly
  Future<Map<String, dynamic>> decreaseLife(int questionId) async {
    try {
      final challengeProvider =
          Provider.of<ChallengeProvider>(context, listen: false);

      // Call the API to decrease life
      await challengeProvider.decreaseChallengeLife(challengeId, questionId);

      // // The actual life data will be updated via the socket connection
      // // For now, we'll just decrease the local life data for immediate feedback
      // if (_lifeData != null && _lifeData!.containsKey('life')) {
      //   _lifeData!['life'] = math.max(0, (_lifeData!['life'] as int) - 1);
      //   notifyListeners();
      // }

      return _lifeData ?? {'life': 0, 'max': 10};
    } catch (e) {
      // Handle error
      return {'error': e.toString()};
    }
  }

  // Check life status from the server
  // Note: This is a placeholder method since the actual API might not exist yet
  Future<void> checkLifeStatus() async {
    try {
      // This is a placeholder implementation
      // In a real implementation, you would call an API to check life status

      // For now, we'll just use the current life data
      notifyListeners();
    } catch (e) {
      // Handle error
    }
  }
}
