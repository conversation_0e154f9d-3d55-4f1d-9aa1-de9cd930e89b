import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TermsProvider extends ChangeNotifier {
  bool _hasAgreedToTerms = false;
  
  // Key for SharedPreferences
  static const String _termsAgreedKey = 'terms_agreed';
  
  // Getter
  bool get hasAgreedToTerms => _hasAgreedToTerms;
  
  // Constructor - Load settings from SharedPreferences
  TermsProvider() {
    _loadTermsStatus();
  }
  
  // Load terms agreement status from SharedPreferences
  Future<void> _loadTermsStatus() async {
    final prefs = await SharedPreferences.getInstance();
    _hasAgreedToTerms = prefs.getBool(_termsAgreedKey) ?? false;
    notifyListeners();
  }
  
  // Set terms agreement status
  Future<void> setTermsAgreement(bool value) async {
    if (_hasAgreedToTerms != value) {
      _hasAgreedToTerms = value;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_termsAgreedKey, _hasAgreedToTerms);
      notifyListeners();
    }
  }
}
