import 'dart:async';
import 'package:flutter/foundation.dart';

class TimerService extends ChangeNotifier {
  Timer? _timer;
  int _remainingTime = 0;
  bool _isActive = false;
  
  // Getters
  int get remainingTime => _remainingTime;
  bool get isActive => _isActive;
  
  // Start the timer with a given duration in seconds
  void startTimer(int durationInSeconds) {
    _remainingTime = durationInSeconds;
    _isActive = true;
    
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime > 0) {
        _remainingTime--;
        notifyListeners();
      } else {
        stopTimer();
        notifyListeners();
      }
    });
  }
  
  // Stop the timer
  void stopTimer() {
    _timer?.cancel();
    _isActive = false;
    notifyListeners();
  }
  
  // Reset the timer to a specific duration without starting it
  void resetTimer(int durationInSeconds) {
    _timer?.cancel();
    _remainingTime = durationInSeconds;
    _isActive = false;
    notifyListeners();
  }
  
  // Check if the timer has expired
  bool hasExpired() {
    return _remainingTime <= 0;
  }
  
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
