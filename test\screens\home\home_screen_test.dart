import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Simple widget for testing home screen functionality
class SimpleHomeWidget extends StatelessWidget {
  const SimpleHomeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Achawach Challenges'),
        ),
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Featured Challenges Section
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Icon(Icons.emoji_events, color: Colors.amber),
                    SizedBox(width: 8),
                    Text('Featured Challenges', 
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  ],
                ),
              ),
              
              // Categories Section
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Icon(Icons.category),
                    SizedBox(width: 8),
                    Text('Challenge Categories', 
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  ],
                ),
              ),
              
              // Mock Categories
              SizedBox(
                height: 160,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: [
                    _buildCategoryCard('Sports', Icons.sports_basketball),
                    _buildCategoryCard('Science', Icons.science),
                    _buildCategoryCard('History', Icons.history_edu),
                  ],
                ),
              ),
              
              // Recent Activity Section
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Icon(Icons.history, color: Colors.teal),
                    SizedBox(width: 8),
                    Text('Recent Activity', 
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  ],
                ),
              ),
              
              // Mock Recent Activity
              Container(
                height: 120,
                margin: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.history_outlined, size: 40, color: Colors.grey),
                      SizedBox(height: 8),
                      Text('No recent activity'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  static Widget _buildCategoryCard(String name, IconData icon) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(left: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 40),
          const SizedBox(height: 8),
          Text(name),
        ],
      ),
    );
  }
}

void main() {
  group('HomeScreen Tests', () {
    testWidgets('HomeScreen renders correctly', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleHomeWidget());
      
      // Verify section headers are displayed
      expect(find.text('Featured Challenges'), findsOneWidget);
      expect(find.text('Challenge Categories'), findsOneWidget);
      expect(find.text('Recent Activity'), findsOneWidget);
      
      // Verify category cards are displayed
      expect(find.text('Sports'), findsOneWidget);
      expect(find.text('Science'), findsOneWidget);
      expect(find.text('History'), findsOneWidget);
      
      // Verify recent activity placeholder is displayed
      expect(find.text('No recent activity'), findsOneWidget);
    });

    testWidgets('HomeScreen shows category cards', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleHomeWidget());
      
      // Verify category icons are displayed
      expect(find.byIcon(Icons.sports_basketball), findsOneWidget);
      expect(find.byIcon(Icons.science), findsOneWidget);
      expect(find.byIcon(Icons.history_edu), findsOneWidget);
    });

    testWidgets('HomeScreen shows section icons', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleHomeWidget());
      
      // Verify section icons are displayed
      expect(find.byIcon(Icons.emoji_events), findsOneWidget);
      expect(find.byIcon(Icons.category), findsOneWidget);
      expect(find.byIcon(Icons.history), findsOneWidget);
    });
  });
}
