// Manual mock implementation for SocketService
import 'dart:async';
import 'package:achawach/services/socket_service.dart';

class MockSocketService implements SocketService {
  final StreamController<Map<String, dynamic>> gameEndedController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> winnerAnnouncementController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> gameStateController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> gameleaderBoardController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<String> errorController =
      StreamController<String>.broadcast();

  String? endGameToken;
  String? endGameChallengeId;
  bool _isConnected = false;
  int gameEndedStreamCalls = 0;
  int winnerAnnouncementStreamCalls = 0;
  int endGameCalls = 0;

  @override
  Stream<Map<String, dynamic>> get gameEndedStream {
    gameEndedStreamCalls++;
    return gameEndedController.stream;
  }

  @override
  Stream<Map<String, dynamic>> get winnerAnnouncementStream {
    winnerAnnouncementStreamCalls++;
    return winnerAnnouncementController.stream;
  }

  @override
  Stream<Map<String, dynamic>> get gameStateStream =>
      gameStateController.stream;

  @override
  Stream<Map<String, dynamic>> get gameleaderBoardStream =>
      gameleaderBoardController.stream;

  @override
  Stream<String> get errorStream => errorController.stream;

  @override
  bool get isConnected => _isConnected;

  @override
  Future<void> connect(String token, String challengeId) async {
    _isConnected = true;
  }

  @override
  void disconnect() {
    _isConnected = false;
  }

  @override
  void endGame(String token, String challengeId) {
    endGameCalls++;
    endGameToken = token;
    endGameChallengeId = challengeId;
  }

  @override
  void dispose() {
    gameEndedController.close();
    winnerAnnouncementController.close();
    gameStateController.close();
    gameleaderBoardController.close();
    errorController.close();
  }

  @override
  void requestLeaderboardRefresh(String token, String challengeId) {
    // Mock implementation
  }

  // Add any other methods from SocketService that need to be implemented
  @override
  dynamic noSuchMethod(Invocation invocation) {
    return super.noSuchMethod(invocation);
  }
}
