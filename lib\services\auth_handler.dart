import 'package:achawach/main.dart'; // To access the global navigatorKey
import 'package:achawach/routers/app_router.dart'; // To access your route constants
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

// A custom exception to identify this specific error type if needed.
class TokenExpiredException implements Exception {
  final String message;
  TokenExpiredException(this.message);
  @override
  String toString() => message;
}

class AuthHandler {
  /// Checks an HTTP response. If the status code is 401 (Unauthorized),
  /// it triggers a global logout and throws a [TokenExpiredException].
  /// Otherwise, it does nothing.
  static Future<void> handleResponse(http.Response response) async {
    if (response.statusCode == 401) {
      debugPrint("Token expired (401). Initiating logout.");
      await _performLogout();

      // Throw an exception to stop the original function from proceeding.
      throw TokenExpiredException('Your session has expired. Please log in again.');
    }
    // If the token is valid, the function completes without doing anything.
  }

  /// Clears user data and navigates to the login screen.
  static Future<void> _performLogout() async {
    // 1. Clear the stored token from SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    // You can also clear other user-related data here
    // await prefs.clear();

    // 2. Navigate to the login screen and remove all previous routes
    // We use the global navigatorKey you already have in main.dart
    navigatorKey.currentState?.pushNamedAndRemoveUntil(
      AppRouter.loginRoute,
      (Route<dynamic> route) => false, // This predicate removes all routes.
    );
  }
}