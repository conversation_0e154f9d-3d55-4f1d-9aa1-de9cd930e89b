import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/core/utils/theme_utils.dart';
import 'package:achawach/providers/terms_provider.dart';

class TermsAndConditionsDialog extends StatefulWidget {
  final Function() onAgree;
  final Function() onDecline;

  const TermsAndConditionsDialog({
    Key? key,
    required this.onAgree,
    required this.onDecline,
  }) : super(key: key);

  @override
  State<TermsAndConditionsDialog> createState() =>
      _TermsAndConditionsDialogState();
}

class _TermsAndConditionsDialogState extends State<TermsAndConditionsDialog> {
  bool _isAgreed = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: contentBox(context),
    );
  }

  Widget contentBox(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        color: Theme.of(context).brightness == Brightness.dark
            ? AppColors.background
            : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            offset: const Offset(0, 10),
            blurRadius: 20,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            'Terms and Conditions',
            style: GoogleFonts.poppins(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: ThemeUtils.getTextColor(context),
            ),
          ),
          const SizedBox(height: 15),
          Container(
            height: 300,
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? AppColors.background.withValues(alpha: 0.5)
                  : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(10),
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Text(
                _termsAndConditionsText,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: ThemeUtils.getTextColor(context),
                ),
              ),
            ),
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              Checkbox(
                value: _isAgreed,
                activeColor: AppColors.primary,
                onChanged: (bool? value) {
                  setState(() {
                    _isAgreed = value ?? false;
                  });
                },
              ),
              Expanded(
                child: Text(
                  'I have read and agree to the terms and conditions',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: ThemeUtils.getTextColor(context),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: widget.onDecline,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: Text(
                    'Decline',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: ElevatedButton(
                  onPressed: _isAgreed
                      ? () {
                          // Save agreement to SharedPreferences
                          Provider.of<TermsProvider>(context, listen: false)
                              .setTermsAgreement(true);
                          widget.onAgree();
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    disabledBackgroundColor:
                        AppColors.primary.withValues(alpha: 0.3),
                  ),
                  child: Text(
                    'Agree',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Sample terms and conditions text
const String _termsAndConditionsText = '''
Welcome to Achawach Challenges!

By participating in our challenges, you agree to the following terms and conditions:

1. GENERAL TERMS

1.1 These terms and conditions govern your participation in challenges offered through the Achawach application.

1.2 By joining or starting a challenge, you confirm that you have read, understood, and agree to be bound by these terms.

2. CHALLENGE PARTICIPATION

2.1 You must have a registered account to participate in challenges.

2.2 Each challenge has specific rules, including scoring mechanisms, time limits, and winning conditions.

2.3 The app uses a lives system. When you run out of lives, you must wait for them to refill or purchase additional lives.

3. USER CONDUCT

3.1 You agree to participate in challenges fairly and not to use any unauthorized methods to gain advantages.

3.2 You will not attempt to manipulate scores, lives, or any other game elements through technical exploits.

3.3 Inappropriate behavior, including harassment of other users, may result in account suspension.

4. REWARDS AND POINTS

4.1 Points earned in challenges are virtual and have no monetary value.

4.2 Rewards may be offered for completing challenges according to the specific terms of each challenge.

4.3 The app reserves the right to adjust point systems and rewards at any time.

5. PRIVACY AND DATA

5.1 Your participation in challenges will generate data about your performance and preferences.

5.2 This data will be handled according to our Privacy Policy.

6. MODIFICATIONS

6.1 These terms may be updated at any time. Continued use of the app constitutes acceptance of modified terms.

7. LIMITATION OF LIABILITY

7.1 The app is provided "as is" without warranties of any kind.

7.2 We are not liable for any damages arising from your participation in challenges.

By clicking "Agree," you confirm that you have read and accept these terms and conditions.
''';
