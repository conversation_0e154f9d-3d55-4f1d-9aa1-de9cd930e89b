import 'dart:io'; // For File
import 'package:achawach/core/constants/constants.dart';
import 'package:achawach/core/utils/page_transitions.dart';
import 'package:achawach/models/questions_category.dart';

import 'package:achawach/services/api_profile_service.dart';
import 'package:achawach/widgets/animated_bottom_bar.dart';
import 'package:achawach/widgets/drawer.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/screens/auth/login_screen.dart';
import 'package:intl/intl.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  final ApiProfileService _apiService = ApiProfileService();
  Profile? _profile;
  bool _isLoadingProfile = true; // Specific loading state for profile
  String? _profileError;

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // Animation controllers
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Scroll controller for parallax effect
  final ScrollController _scrollController = ScrollController();
  final NumberFormat _currencyFormatter = NumberFormat.currency(
    locale: 'am_ET',
    symbol: 'Birr ',
    decimalDigits: 2,
  );

  // --- State for Editing ---
  bool _isEditing = false;
  bool _isSaving = false;
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _fullNameController;
  File? _selectedImageFile;
  String? _selectedAvatar;
  bool _useProfileImage = false;
  String? _saveError; // Specific error during save operation
  final DateFormat _dateFormatter = DateFormat('MMMM dd, yyyy');
  // --- State for Categories ---
  List<QuestionsCategory> _allCategories = [];
  bool _isLoadingCategories = true; // Specific loading state for categories
  String? _categoriesError;
  Set<int> _selectedCategoryIds = {};
  // ---
  bool _isLoading = false;
  String? _error;
  // Available avatars (customize this list)
  final List<String> _availableAvatars = [
    '🐯',
    '🐷',
    '🦁',
    '🐼',
    '🐨',
    '🐮',
    '🦊',
    '🐻',
    '🦙',
    '🐸'
  ];

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Setup animations
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Setup scroll listener for parallax effect
    _scrollController.addListener(() {
      setState(() {});
    });

    _fullNameController = TextEditingController();
    _loadInitialData();

    // Start animations
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    _fullNameController.dispose();
    super.dispose();
  }

  // Combine initial loading
  Future<void> _loadInitialData() async {
    setState(() {
      _isLoadingProfile = true;
      _isLoadingCategories = true;
      _profileError = null;
      _categoriesError = null;
      _saveError = null; // Clear save error on reload
    });

    try {
      // Fetch profile and categories in parallel
      final results = await Future.wait([
        _apiService.getProfile(),
        _apiService.getAllCategories(),
      ]);

      final profile = results[0] as Profile;
      final allCategories = results[1] as List<QuestionsCategory>;

      setState(() {
        _profile = profile;
        _allCategories = allCategories;

        // Initialize editing state variables from profile
        _resetEditFields(profile); // Use a helper to set fields

        _isLoadingProfile = false;
        _isLoadingCategories = false;
        _isEditing = false; // Ensure not in edit mode initially
      });
    } catch (e) {
      // Determine which part failed or show a general error
      print("Error loading initial data: $e");
      setState(() {
        // A simple approach: show error based on which is still loading
        if (_isLoadingProfile && _isLoadingCategories) {
          _profileError =
              "Failed to load profile and categories: ${e.toString().replaceFirst('Exception: ', '')}";
        } else if (_isLoadingProfile) {
          _profileError =
              "Failed to load profile: ${e.toString().replaceFirst('Exception: ', '')}";
        } else {
          _categoriesError =
              "Failed to load categories: ${e.toString().replaceFirst('Exception: ', '')}";
        }
        _isLoadingProfile = false; // Stop loading states on error
        _isLoadingCategories = false;
      });
    }
  }

  Future<void> _loadProfile() async {
    if (_isLoading) return; // Prevent multiple simultaneous loads
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final profile = await _apiService.getProfile();
      setState(() {
        _profile = profile;
        // --- Initialize editing state variables ---
        _fullNameController.text = profile.fullName;
        _useProfileImage = profile.isProfileImageAvailable;
        _selectedAvatar = profile.avatar; // Keep original avatar initially
        _selectedImageFile = null; // Reset selected file
        // ---
        _isLoading = false;
        _isEditing = false; // Ensure exiting edit mode on refresh
      });
    } catch (e) {
      setState(() {
        _error = e
            .toString()
            .replaceFirst('Exception: ', ''); // Cleaner error message
        _isLoading = false;
      });
    }
  }

  Future<void> _logout() async {
    // Add confirmation dialog?
    await _apiService.clearToken();
    if (mounted) {
      Navigator.of(context).pushAndRemoveUntil(
        PageTransitions.fadeSlideDown(
          page: const LoginScreen(),
          duration: const Duration(milliseconds: 500),
        ),
        (route) => false,
      );
    }
  }

  void _toggleEditMode() {
    setState(() {
      _isEditing = !_isEditing;
      _saveError = null; // Clear previous save errors when toggling
      if (!_isEditing) {
        // If cancelling edit, reset fields to original profile data
        _resetEditFields(_profile);
      } else if (_profile != null) {
        // Ensure fields are set correctly when entering edit mode
        _fullNameController.text = _profile!.fullName;
        _useProfileImage = _profile!.isProfileImageAvailable;
        _selectedAvatar = _profile!.avatar;
        _selectedImageFile = null;
        // Initialize selected category IDs from the current profile
        _selectedCategoryIds =
            _profile!.categories.map((cat) => cat.id).toSet();
      }
    });
  }

  // Helper to reset fields, now accepts profile
  void _resetEditFields(Profile? profile) {
    if (profile != null) {
      _fullNameController.text = profile.fullName;
      _useProfileImage = profile.isProfileImageAvailable;
      _selectedAvatar = profile.avatar;
      _selectedImageFile = null;
      // Reset selected categories to the profile's current categories
      _selectedCategoryIds = profile.categories.map((cat) => cat.id).toSet();
    }
  }

  // --- Image/Avatar Picking ---
  Future<void> _pickImage(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
          source: source, imageQuality: 80, maxWidth: 800);

      if (pickedFile != null) {
        setState(() {
          _selectedImageFile = File(pickedFile.path);
          _useProfileImage = true; // Switched to using an image
          _selectedAvatar = null; // Clear avatar selection
        });
        // Close the bottom sheet if open
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
      }
    } catch (e) {
      print("Image picking error: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Failed to pick image: $e'),
            backgroundColor: Colors.red),
      );
    }
  }

  void _showAvatarPicker() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text("Choose an Avatar",
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(color: AppColors.text)),
            const SizedBox(height: 20),
            GridView.builder(
              shrinkWrap: true,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 5,
                crossAxisSpacing: 15,
                mainAxisSpacing: 15,
              ),
              itemCount: _availableAvatars.length,
              itemBuilder: (context, index) {
                final avatar = _availableAvatars[index];
                bool isSelected =
                    _selectedAvatar == avatar && !_useProfileImage;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedAvatar = avatar;
                      _useProfileImage = false; // Switched to using avatar
                      _selectedImageFile = null; // Clear image selection
                    });
                    Navigator.pop(context); // Close bottom sheet
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected
                            ? AppColors.primary.withOpacity(0.3)
                            : Colors.grey.shade200,
                        border: Border.all(
                            color: isSelected
                                ? AppColors.primary
                                : Colors.transparent,
                            width: 2)),
                    alignment: Alignment.center,
                    child: Text(
                      avatar,
                      style: const TextStyle(fontSize: 30),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SafeArea(
        // Use SafeArea
        child: Wrap(
          // Use Wrap for content
          children: <Widget>[
            ListTile(
              leading:
                  const Icon(Icons.photo_library, color: AppColors.secondary),
              title: const Text('Pick from Gallery'),
              onTap: () => _pickImage(ImageSource.gallery),
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt, color: AppColors.secondary),
              title: const Text('Take a Picture'),
              onTap: () => _pickImage(ImageSource.camera),
            ),
            ListTile(
              leading:
                  const Icon(Icons.emoji_emotions, color: AppColors.secondary),
              title: const Text('Choose an Avatar'),
              onTap: () {
                Navigator.pop(context); // Close current sheet first
                _showAvatarPicker();
              },
            ),
            // Option to remove custom image and revert to avatar
            if (_profile?.isProfileImageAvailable ??
                false) // Show remove only if they currently have an image
              ListTile(
                leading: Icon(Icons.no_photography, color: Colors.red.shade400),
                title: const Text('Remove Photo (use Avatar)',
                    style: TextStyle(color: Colors.red)),
                onTap: () {
                  setState(() {
                    _selectedImageFile = null;
                    _useProfileImage = false;
                    // Keep the original avatar or default if none
                    _selectedAvatar =
                        _profile?.avatar ?? _availableAvatars.first;
                  });
                  Navigator.pop(context);
                },
              ),
          ],
        ),
      ),
    );
  }
  // ---

  // --- Save Profile ---
  Future<void> _saveProfile() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedCategoryIds.isEmpty) {
        setState(() {
          _saveError = "Please select at least one interest category.";
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(_saveError!), backgroundColor: Colors.orange),
        );
        return;
      }
      setState(() {
        _isSaving = true;
        _error = null;
      });

      try {
        final updatedProfile = await _apiService.updateProfile(
          fullName: _fullNameController.text.trim(),
          profileImageFile: _selectedImageFile,
          avatar: _selectedAvatar,
          useProfileImage: _useProfileImage,
          categoryIds: _selectedCategoryIds.toList(), // Pass the selected IDs
        );

        setState(() {
          _profile = updatedProfile; // Update local profile data
          _isEditing = false;
          // Reset fields using the *updated* profile data
          _resetEditFields(updatedProfile);
          _selectedImageFile =
              null; // Clear selected file after successful upload
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully!'),
            backgroundColor: AppColors.secondary,
          ),
        );
      } catch (e) {
        setState(() {
          _saveError = e.toString().replaceFirst('Exception: ', '');
          _isSaving = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update profile: $_saveError'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  // ---

  @override
  Widget build(BuildContext context) {
    // Determine overall loading state
    final bool isLoading = _isLoadingProfile || _isLoadingCategories;
    // Determine if there's any error preventing display
    final String? initialError = _profileError ?? _categoriesError;
    return Scaffold(
      key: _scaffoldKey,
      extendBodyBehindAppBar: true, // Allow content to flow behind app bar
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: _isEditing
            ? IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: _isSaving
                    ? null
                    : _toggleEditMode, // Disable cancel while saving
                tooltip: 'Cancel',
              )
            : IconButton(
                // Drawer Icon as leading
                icon: const Icon(Icons.menu, color: Colors.white),
                onPressed: () {
                  _scaffoldKey.currentState
                      ?.openDrawer(); // Open drawer on icon tap
                },
              ),
        actions: [
          if (_isEditing)
            IconButton(
              icon: _isSaving
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                          color: Colors.white, strokeWidth: 2))
                  : const Icon(Icons.save, color: Colors.white),
              onPressed:
                  _isSaving ? null : _saveProfile, // Disable save while saving
              tooltip: 'Save Changes',
            )
          else
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.white),
              onPressed: _profile == null
                  ? null
                  : _toggleEditMode, // Disable edit if profile not loaded
              tooltip: 'Edit Profile',
            ),
          IconButton(
            icon: const Icon(Icons.logout, color: Colors.white),
            onPressed: _logout,
            tooltip: 'Logout',
          ),
        ],
      ),
      drawer: const AppDrawer(), // Call function to build the Drawer
      bottomNavigationBar:
          const SafeArea(child: AnimatedBottomBar(currentRoute: '/profile')),
      extendBody: true,
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 500),
        child: isLoading
            ? Container(
                color: AppColors.primary,
                child: const Center(
                    child: CircularProgressIndicator(color: Colors.white)),
              )
            : initialError != null &&
                    _profile == null // Show error only if profile failed
                ? Container(
                    color: AppColors.primary,
                    child: _buildErrorWidget(initialError,
                        _loadInitialData), // Pass error and retry action
                  )
                : _buildProfileContent(),
      ),
    );
  }

  Widget _buildErrorWidget(String errorMsg, VoidCallback onRetry) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Animated error icon
            FadeTransition(
              opacity: _fadeAnimation,
              child: const Icon(Icons.error_outline,
                  color: Colors.white, size: 80),
            ),
            const SizedBox(height: 20),
            // Error title with animation
            FadeTransition(
              opacity: _fadeAnimation,
              child: const Text(
                'Error Loading Profile',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 12),
            // Error message with animation
            FadeTransition(
              opacity: _fadeAnimation,
              child: Text(
                errorMsg,
                style: TextStyle(color: Colors.white.withAlpha(200)),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            // Retry button with animation
            SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 0.5),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: _animationController,
                curve: Curves.easeOutQuart,
              )),
              child: ElevatedButton.icon(
                icon: const Icon(Icons.refresh),
                onPressed: onRetry,
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: AppColors.primary,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileContent() {
    if (_profile == null) {
      // Should be covered by loading/error states, but as a fallback
      return const Center(child: Text("Profile data not available."));
    }

    return Form(
      key: _formKey,
      child: ListView(
        controller: _scrollController,
        padding: EdgeInsets.zero,
        children: [
          // Animated Header with Parallax Effect
          _buildAnimatedHeader(),

          // Main Content Container
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(30),
                topRight: Radius.circular(30),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(20),
                  blurRadius: 8,
                  offset: const Offset(0, -3),
                ),
              ],
            ),
            padding: const EdgeInsets.fromLTRB(
                20, 30, 20, 100), // Extra bottom padding for bottom nav bar
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User Stats Summary
                if (!_isEditing) _buildUserStatsSummary(),

                const SizedBox(height: 25),

                // Section 1: Basic Info (Display or Edit)
                _isEditing
                    ? _buildEditFormBasicInfo()
                    : _buildDisplayInfoBasicInfo(),
                const SizedBox(height: 25),

                // Section 2: Categories/Interests (Display or Edit)
                _buildCategoriesSection(),

                const SizedBox(height: 20),

                // Display save error if relevant
                if (_saveError != null && _isEditing)
                  Padding(
                    padding: const EdgeInsets.only(top: 15.0, bottom: 10.0),
                    child: Text(
                      'Save Error: $_saveError',
                      style: const TextStyle(color: Colors.red, fontSize: 14),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedHeader() {
    return Stack(
      children: [
        // Background with parallax effect
        Container(
          height: 240,
          width: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary,
                AppColors.secondary,
              ],
            ),
          ),
          child: Stack(
            children: [
              // Decorative elements
              Positioned(
                top: -20,
                right: -20,
                child: Container(
                  height: 100,
                  width: 100,
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(30),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
              Positioned(
                bottom: 20,
                left: -30,
                child: Container(
                  height: 80,
                  width: 80,
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(20),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Profile content with animation
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              padding: const EdgeInsets.only(bottom: 30),
              child: Column(
                children: [
                  _buildProfileHeader(),
                  const SizedBox(height: 15),
                  // User name with animation
                  Text(
                    _profile?.fullName ?? 'User',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 5),
                  // Phone number (masked)
                  Text(
                    _profile?.phoneNumber != null
                        ? '******${_profile!.phoneNumber.substring(_profile!.phoneNumber.length - 4)}'
                        : 'No phone number',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white.withAlpha(200),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserStatsSummary() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
                'Member Since',
                _dateFormatter.format(_profile!.createdAt),
                Icons.calendar_today_outlined),
            _buildStatItem('Interests', '${_profile!.categories.length}',
                Icons.category_outlined),
            _buildStatItem(
                'Total Earned',
                _currencyFormatter.format(_profile!.totalEarned!),
                Icons.monetization_on),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.primary.withAlpha(76), // 0.3 opacity
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: AppColors.primary, size: 22),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildDisplayInfoBasicInfo() {
    // Contains Full Name, Phone, Member Since
    return Card(
      elevation: 3,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10.0),
        child: Column(
          children: [
            _buildInfoRow(
                Icons.person_outline, 'Full Name', _profile?.fullName ?? 'N/A'),
            _buildDivider(),
            _buildInfoRow(Icons.phone_outlined, 'Phone Number',
                _profile?.phoneNumber ?? 'N/A',
                isSensitive: true),
            _buildDivider(),
            // _buildInfoRow(
            //     Icons.calendar_today_outlined,
            //     'Member Since',
            //     /* ... date format ... */ _profile?.createdAt != null
            //         ? '${_profile!.createdAt.day}/${_profile!.createdAt.month}/${_profile!.createdAt.year}'
            //         : 'N/A'),
          ],
        ),
      ),
    );
  }

  Widget _buildEditFormBasicInfo() {
    // Contains Full Name input, Phone display
    return Card(
      elevation: 3,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("Edit Information",
                style: Theme.of(context)
                    .textTheme
                    .titleMedium
                    ?.copyWith(color: AppColors.text)),
            const SizedBox(height: 20),
            TextFormField(
              controller: _fullNameController,
              decoration: _inputDecoration('Full Name', Icons.person_outline),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter your full name';
                }
                return null;
              },
              enabled: !_isSaving,
            ),
            const SizedBox(height: 20),
            _buildInfoRow(Icons.phone_outlined, 'Phone Number',
                _profile?.phoneNumber ?? 'N/A',
                isSensitive: true),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  // --- NEW: Categories Section ---

  Widget _buildCategoriesSection() {
    // Show loading or error specifically for categories if needed during edit?
    // For simplicity, we assume categories are loaded if we reach here.
    final bool hasCategories = _isEditing
        ? _allCategories.isNotEmpty
        : (_profile?.categories.isNotEmpty ?? false);

    return Card(
      elevation: 3,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_isEditing ? "Select Your Interests" : "Interests",
                style: Theme.of(context)
                    .textTheme
                    .titleMedium
                    ?.copyWith(color: AppColors.text)),
            const SizedBox(height: 15),
            if (!hasCategories && !_isEditing)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10.0),
                child: Text("No interests selected yet.",
                    style: TextStyle(color: Colors.grey[600])),
              )
            else if (_isLoadingCategories) // Show loader if categories still loading (edge case)
              const Center(
                  child: Padding(
                      padding: EdgeInsets.all(8.0),
                      child: CircularProgressIndicator(strokeWidth: 2)))
            else if (_categoriesError !=
                null) // Show error if categories failed
              const Center(
                  child: Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Text("Error loading categories.",
                          style: TextStyle(color: Colors.red))))
            else
              _isEditing ? _buildEditCategories() : _buildDisplayCategories(),
          ],
        ),
      ),
    );
  }

  Widget _buildDisplayCategories() {
    if (_profile == null || _profile!.categories.isEmpty) {
      return const SizedBox.shrink(); // Or Text("None selected")
    }
    return Wrap(
      spacing: 8.0, // Horizontal space between chips
      runSpacing: 8.0, // Vertical space between lines
      children: _profile!.categories.map((category) {
        return Chip(
          avatar: Icon(category.iconData, color: AppColors.secondary, size: 18),
          label: Text(category.name),
          backgroundColor: AppColors.primary.withAlpha(38), // 0.15 opacity
          labelStyle: const TextStyle(color: AppColors.text, fontSize: 13),
          side:
              BorderSide(color: AppColors.primary.withAlpha(76)), // 0.3 opacity
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        );
      }).toList(),
    );
  }

  Widget _buildEditCategories() {
    if (_allCategories.isEmpty) {
      return const Center(child: Text("No categories available to select."));
    }
    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: _allCategories.map((category) {
        final bool isSelected = _selectedCategoryIds.contains(category.id);
        return FilterChip(
          label: Text(category.name),
          avatar: Icon(category.iconData,
              color: isSelected ? Colors.white : AppColors.secondary, size: 18),
          selected: isSelected,
          onSelected: _isSaving
              ? null
              : (selected) {
                  // Disable selection while saving
                  setState(() {
                    if (selected) {
                      _selectedCategoryIds.add(category.id);
                    } else {
                      _selectedCategoryIds.remove(category.id);
                    }
                    _saveError = null; // Clear save error on interaction
                  });
                },
          backgroundColor: Colors.grey[100],
          selectedColor:
              AppColors.secondary, // Use secondary for selected state
          checkmarkColor: Colors.white,
          labelStyle: TextStyle(
              color: isSelected ? Colors.white : AppColors.text,
              fontSize: 13,
              fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal),
          side: BorderSide(
            color: isSelected ? AppColors.secondary : Colors.grey.shade300,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          showCheckmark: false, // Checkmark can feel cluttered with icon
        );
      }).toList(),
    );
  }

  Widget _buildProfileHeader() {
    Widget profileWidget;
    final double profileSize = _isEditing ? 70 : 60;

    // Determine what to show: selected image, selected avatar, current image, current avatar, or default
    if (_isEditing) {
      if (_useProfileImage && _selectedImageFile != null) {
        // Show newly selected image preview
        profileWidget = Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(40),
                blurRadius: 12,
                spreadRadius: 2,
              ),
            ],
          ),
          child: CircleAvatar(
            radius: profileSize,
            backgroundImage: FileImage(_selectedImageFile!),
          ),
        );
      } else if (!_useProfileImage && _selectedAvatar != null) {
        // Show newly selected avatar
        profileWidget =
            _buildEnhancedAvatarWidget(_selectedAvatar!, profileSize);
      } else if (_profile!.isProfileImageAvailable &&
          _profile!.profileImagePath != null) {
        // Show existing profile image (while editing, but not changed yet)
        profileWidget = Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(40),
                blurRadius: 12,
                spreadRadius: 2,
              ),
            ],
          ),
          child: CircleAvatar(
            radius: profileSize,
            backgroundImage: NetworkImage(
                '${AppConstants.apiBaseIP}/${_profile!.profileImagePath!.replaceAll('\\', '/')}'),
            onBackgroundImageError: (_, __) {},
            child: _profile!.profileImagePath == null
                ? const Icon(Icons.person, size: 60, color: AppColors.secondary)
                : null,
          ),
        );
      } else {
        // Show existing avatar (while editing, but not changed yet) or default
        profileWidget =
            _buildEnhancedAvatarWidget(_profile!.avatar ?? '❓', profileSize);
      }
    } else {
      // --- View Mode ---
      if (_profile!.isProfileImageAvailable &&
          _profile!.profileImagePath != null) {
        profileWidget = Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 3),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(40),
                blurRadius: 12,
                spreadRadius: 2,
              ),
            ],
          ),
          child: CircleAvatar(
            radius: profileSize,
            backgroundImage: NetworkImage(
                '${AppConstants.apiBaseIP}/${_profile!.profileImagePath!.replaceAll('\\', '/')}'),
            onBackgroundImageError: (_, __) {},
            child: _profile!.profileImagePath == null
                ? const Icon(Icons.person, size: 50, color: AppColors.secondary)
                : null,
          ),
        );
      } else {
        profileWidget =
            _buildEnhancedAvatarWidget(_profile!.avatar ?? '❓', profileSize);
      }
    }

    return Center(
      child: Stack(
        children: [
          // Add animation to the profile widget
          SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.2),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: _animationController,
              curve: Curves.easeOutQuart,
            )),
            child: profileWidget,
          ),
          if (_isEditing)
            Positioned(
              bottom: 0,
              right: 0,
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(1, 1),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: _animationController,
                  curve: Curves.elasticOut,
                )),
                child: Material(
                  color: AppColors.primary,
                  shape: const CircleBorder(),
                  elevation: 4,
                  child: InkWell(
                    onTap: _isSaving ? null : _showImageSourceDialog,
                    customBorder: const CircleBorder(),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: const Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEnhancedAvatarWidget(String avatar, double radius) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 3),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(40),
            blurRadius: 12,
            spreadRadius: 2,
          ),
        ],
      ),
      child: CircleAvatar(
        radius: radius,
        backgroundColor: AppColors.accent.withAlpha(76), // 0.3 opacity
        child: Text(
          avatar,
          style:
              TextStyle(fontSize: radius * 0.9), // Scale emoji size with radius
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value,
      {bool isSensitive = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Row(
        children: [
          Icon(icon, color: AppColors.secondary, size: 22),
          const SizedBox(width: 15),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(color: Colors.grey[600], fontSize: 13),
              ),
              const SizedBox(height: 3),
              Text(
                isSensitive
                    ? '******${value.substring(value.length - 4)}'
                    : value, // Simple masking for phone
                style: const TextStyle(
                  color: AppColors.text,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  InputDecoration _inputDecoration(String label, IconData icon) {
    return InputDecoration(
      labelText: label,
      prefixIcon: Icon(icon, color: AppColors.secondary),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
      ),
      floatingLabelStyle: const TextStyle(color: AppColors.primary),
      labelStyle: TextStyle(color: Colors.grey[600]),
      filled: true,
      fillColor: Colors.white,
    );
  }

  Widget _buildDivider() {
    return Divider(height: 1, thickness: 1, color: Colors.grey[200]);
  }
}
