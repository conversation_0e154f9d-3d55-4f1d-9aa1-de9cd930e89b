import 'package:achawach/core/constants/app_colors.dart';
import 'package:flutter/material.dart';

/// Utility class for theme-aware colors and styles
class ThemeUtils {
  /// Returns the appropriate text color based on the current theme brightness
  static Color getTextColor(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? Colors.white : AppColors.text;
  }

  /// Returns a secondary text color (for subtitles, etc.) based on the current theme brightness
  static Color getSecondaryTextColor(BuildContext context, {double opacity = 0.7}) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark 
        ? Colors.white.withValues(alpha: opacity)
        : AppColors.text.withValues(alpha: opacity);
  }

  /// Returns the appropriate card/container background color based on the current theme brightness
  static Color getCardColor(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? Colors.grey[850]! : Colors.white;
  }

  /// Returns a text style with the appropriate color for the current theme
  static TextStyle getTextStyle(BuildContext context, {
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.normal,
    double? letterSpacing,
    double opacity = 1.0,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      letterSpacing: letterSpacing,
      color: getTextColor(context).withValues(alpha: opacity),
    );
  }
}

/// Extension method for Color to use withValues instead of withOpacity
extension ColorExtension on Color {
  Color withValues({double? alpha}) {
    return Color.fromARGB(
      alpha != null ? (alpha * 255).round() : a.toInt(),
     r.toInt(),
     g.toInt(),
     b.toInt(),
    );
  }
}
