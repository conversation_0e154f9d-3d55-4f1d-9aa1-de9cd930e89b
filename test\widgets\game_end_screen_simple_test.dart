import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:achawach/widgets/game_end_screen.dart';

void main() {
  group('GameEndScreen Tests', () {
    testWidgets('GameEndScreen can be created with required parameters',
        (WidgetTester tester) async {
      // Create test data
      const bool isWinner = true;
      const String message = 'You won!';
      final Map<String, dynamic> finalState = {'score': 500};
      bool continueCalled = false;

      // Build a simple widget with GameEndScreen
      await tester.pumpWidget(
        MaterialApp(
          home: Material(
            child: GameEndScreen(
              isWinner: isWinner,
              message: message,
              finalState: finalState,
              onContinue: () {
                continueCalled = true;
              },
            ),
          ),
        ),
      );

      // Verify that the widget was created successfully
      expect(find.byType(GameEndScreen), findsOneWidget);
    });

    test('GameEndService can handle game end events', () {
      // This is a non-widget test to verify the basic functionality
      // Create a mock data for a winner
      final winnerData = {
        'isWinner': true,
        'message': 'You won!',
        'finalState': {'score': 500, 'level': 3},
        'winnerMessage': 'Congratulations!',
        'loserMessage': 'Better luck next time!',
      };

      // Verify the data structure
      expect(winnerData['isWinner'], isTrue);
      expect(winnerData['message'], 'You won!');
      expect(winnerData['finalState'], isA<Map>());

      // Create mock data for a loser
      final loserData = {
        'isWinner': false,
        'message': 'You lost!',
        'finalState': {'score': 300, 'level': 2},
        'winnerMessage': 'Congratulations!',
        'loserMessage': 'Better luck next time!',
      };

      // Verify the data structure
      expect(loserData['isWinner'], isFalse);
      expect(loserData['message'], 'You lost!');
      expect(loserData['finalState'], isA<Map>());
    });
  });
}
