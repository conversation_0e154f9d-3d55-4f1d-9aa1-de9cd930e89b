import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:achawach/widgets/game_end_screen.dart';

void main() {
  group('GameEndScreen Tests', () {
    testWidgets('GameEndScreen displays winner UI correctly',
        (WidgetTester tester) async {
      // Create test data for a winner
      const bool isWinner = true;
      const String message = 'You won the challenge!';
      final Map<String, dynamic> finalState = {
        'score': 500,
        'level': 3,
        'rank': 1,
      };
      bool continueCalled = false;

      // Set a larger screen size to avoid overflow issues
      tester.view.physicalSize = const Size(1080, 1920);
      tester.view.devicePixelRatio = 1.0;

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: GameEndScreen(
            isWinner: isWinner,
            message: message,
            finalState: finalState,
            winnerMessage: 'Congratulations!',
            loserMessage: 'Better luck next time!',
            winnerId: 123,
            challengeId: 456,
            winnerScore: 500,
            winnerRank: 1,
            winnerLevel: 3,
            onContinue: () {
              continueCalled = true;
            },
          ),
        ),
      );

      // Pump a few frames to allow animations to start
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 500));

      // Verify that the winner UI is displayed
      expect(find.text('Congratulations!'), findsOneWidget);
      expect(find.text('You won the challenge!'), findsOneWidget);

      // Verify that the final state is displayed
      expect(find.text('Final Results'), findsOneWidget);

      // Tap the continue button
      await tester.tap(find.text('Continue'));
      await tester.pump();

      // Verify that the onContinue callback was called
      expect(continueCalled, isTrue);

      // Reset the screen size
      addTearDown(tester.view.resetPhysicalSize);
    });

    testWidgets('GameEndScreen displays loser UI correctly',
        (WidgetTester tester) async {
      // Create test data for a loser
      const bool isWinner = false;
      const String message = 'You lost the challenge!';
      final Map<String, dynamic> finalState = {
        'score': 300,
        'level': 2,
        'rank': 5,
      };
      bool continueCalled = false;

      // Set a larger screen size to avoid overflow issues
      tester.view.physicalSize = const Size(1080, 1920);
      tester.view.devicePixelRatio = 1.0;

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: GameEndScreen(
            isWinner: isWinner,
            message: message,
            finalState: finalState,
            winnerMessage: 'Congratulations!',
            loserMessage: 'Better luck next time!',
            winnerId: 456,
            challengeId: 123,
            winnerScore: 500,
            winnerRank: 1,
            winnerLevel: 3,
            onContinue: () {
              continueCalled = true;
            },
          ),
        ),
      );

      // Pump a few frames to allow animations to start
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 500));

      // Verify that the loser UI is displayed
      expect(find.text('Better luck next time!'), findsOneWidget);
      expect(find.text('You lost the challenge!'), findsOneWidget);

      // Verify that the final state is displayed
      expect(find.text('Final Results'), findsOneWidget);

      // Tap the continue button
      await tester.tap(find.text('Continue'));
      await tester.pump();

      // Verify that the onContinue callback was called
      expect(continueCalled, isTrue);

      // Reset the screen size
      addTearDown(tester.view.resetPhysicalSize);
    });

    testWidgets('GameEndScreen handles empty finalState correctly',
        (WidgetTester tester) async {
      // Create test data with empty finalState
      const bool isWinner = true;
      const String message = 'Game ended!';
      final Map<String, dynamic> finalState = {};

      // Set a larger screen size to avoid overflow issues
      tester.view.physicalSize = const Size(1080, 1920);
      tester.view.devicePixelRatio = 1.0;

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: GameEndScreen(
            isWinner: isWinner,
            message: message,
            finalState: finalState,
            onContinue: () {},
          ),
        ),
      );

      // Pump a few frames to allow animations to start
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 500));

      // Verify that the message is displayed
      expect(find.text('Game ended!'), findsOneWidget);

      // Verify that the final state section is not displayed
      expect(find.text('Final Results'), findsNothing);

      // Reset the screen size
      addTearDown(tester.view.resetPhysicalSize);
    });
  });
}
