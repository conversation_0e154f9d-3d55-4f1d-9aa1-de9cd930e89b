import 'package:achawach/screens/challenges/challenge.dart';
import 'package:flutter/material.dart';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/widgets/animated_bottom_bar.dart';
import 'package:achawach/services/api_service.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

class JoinedChallengesScreen extends StatefulWidget {
  const JoinedChallengesScreen({super.key});

  @override
  State<JoinedChallengesScreen> createState() => _JoinedChallengesScreenState();
}

class _JoinedChallengesScreenState extends State<JoinedChallengesScreen>
    with SingleTickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  late TabController _tabController;
  bool _isLoadingStarted = false;
  bool _isLoadingCompleted = false;
  String? _errorStarted;
  String? _errorCompleted;
  List<Map<String, dynamic>> _startedChallenges = [];
  List<Map<String, dynamic>> _completedChallenges = [];
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadStartedChallenges();
    _loadCompletedChallenges();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStartedChallenges() async {
    try {
      setState(() {
        _isLoadingStarted = true;
        _errorStarted = null;
      });

      final challenges = await _apiService.getJoinedChallenges();
      setState(() {
        _startedChallenges = challenges;
        _isLoadingStarted = false;
      });

      print(_startedChallenges);
    } catch (e) {
      setState(() {
        _errorStarted = e.toString();
        _isLoadingStarted = false;
      });
    }
  }

  Future<void> _loadCompletedChallenges() async {
    try {
      setState(() {
        _isLoadingCompleted = true;
        _errorCompleted = null;
      });

      final challenges = await _apiService.getCompletedChallenges();
      setState(() {
        _completedChallenges = challenges;
        _isLoadingCompleted = false;
      });
      print('ended chanllenges:$_completedChallenges');
    } catch (e) {
      setState(() {
        _errorCompleted = e.toString();
        _isLoadingCompleted = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.background,
              AppColors.background.withValues(alpha: 0.8),
              AppColors.primary.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildStartedChallenges(),
                    _buildCompletedChallenges(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: const SafeArea(
          child: AnimatedBottomBar(currentRoute: '/joined-challenges')),
      extendBody: true,
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withValues(alpha: 0.8),
                  AppColors.secondary.withValues(alpha: 0.6),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  blurRadius: 10,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1.5,
              ),
            ),
            child: const Icon(
              Icons.assignment_turned_in_rounded,
              color: Colors.white,
              size: 26,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'My Challenges',
                  style: GoogleFonts.poppins(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.text,
                    letterSpacing: 0.5,
                  ),
                ),
                Text(
                  'Track your progress and achievements',
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    color: Colors.grey[400],
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1.5,
        ),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          gradient: LinearGradient(
            colors: [
              AppColors.primary,
              AppColors.primary.withValues(alpha: 0.8),
              AppColors.secondary.withValues(alpha: 0.9),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.3),
              blurRadius: 8,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey[400],
        labelStyle: GoogleFonts.poppins(
          fontWeight: FontWeight.w600,
          fontSize: 15,
          letterSpacing: 0.5,
        ),
        unselectedLabelStyle: GoogleFonts.poppins(
          fontWeight: FontWeight.w500,
          fontSize: 15,
          letterSpacing: 0.5,
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding:
            const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
        labelPadding: const EdgeInsets.symmetric(horizontal: 5),
        splashBorderRadius: BorderRadius.circular(30),
        overlayColor: WidgetStateProperty.resolveWith<Color?>(
          (Set<WidgetState> states) {
            if (states.contains(WidgetState.hovered)) {
              return Colors.white.withValues(alpha: 0.1);
            }
            if (states.contains(WidgetState.pressed)) {
              return Colors.white.withValues(alpha: 0.2);
            }
            return null;
          },
        ),
        tabs: [
          _buildTab(Icons.hourglass_top_rounded, 'In Progress'),
          _buildTab(Icons.emoji_events_rounded, 'Ended'),
        ],
      ),
    );
  }

  Widget _buildTab(IconData icon, String text) {
    return Tab(
      height: 50,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 8),
          Text(text),
        ],
      ),
    );
  }

  Widget _buildStartedChallenges() {
    if (_isLoadingStarted) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorStarted != null) {
      return _buildErrorState(_errorStarted!, _loadStartedChallenges);
    }

    if (_startedChallenges.isEmpty) {
      return _buildEmptyState(
          'No challenges in progress', Icons.hourglass_empty);
    }

    return _buildChallengesList(_startedChallenges, true);
  }

  Widget _buildCompletedChallenges() {
    if (_isLoadingCompleted) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorCompleted != null) {
      return _buildErrorState(_errorCompleted!, _loadCompletedChallenges);
    }

    if (_completedChallenges.isEmpty) {
      return _buildEmptyState('No ended challenges', Icons.emoji_events);
    }

    return _buildChallengesList(_completedChallenges, false);
  }

  Widget _buildErrorState(String error, VoidCallback onRetry) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            error,
            style: GoogleFonts.poppins(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChallengesList(
      List<Map<String, dynamic>> challenges, bool isStarted) {
    return RefreshIndicator(
      onRefresh: () async {
        if (isStarted) {
          await _loadStartedChallenges();
        } else {
          await _loadCompletedChallenges();
        }
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(20),
        itemCount: challenges.length,
        itemBuilder: (context, index) {
          final challenge = challenges[index];
          return _buildChallengeCard(challenge, isStarted);
        },
      ),
    );
  }

  Widget _buildChallengeCard(Map<String, dynamic> challenge, bool isStarted) {
    final reward = challenge['reward'] ?? 0;
    final score = num.tryParse(challenge['score']?.toString() ?? '0') ?? 0;

    // Extract winning_points from winning_rules if available, otherwise fallback to direct winning_points
    num winningPoints = 1; // Default to 1 to avoid division by zero
    if (challenge['winning_rules'] != null &&
        challenge['winning_rules']['winning_points'] != null) {
      winningPoints = num.tryParse(
              challenge['winning_rules']['winning_points'].toString()) ??
          1;
    } else if (challenge['winning_points'] != null) {
      winningPoints = num.tryParse(challenge['winning_points'].toString()) ?? 1;
    }

    // Ensure we don't divide by zero and limit progress to a maximum of 1.0
    final progress =
        winningPoints > 0 ? (score / winningPoints).clamp(0.0, 1.0) : 0.0;

    // Generate a color based on the category name
    const Color categoryColor = Colors.blue;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Colors.white.withValues(alpha: 0.95),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 12,
            spreadRadius: 1,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.lightImpact();
            // Navigate to challenge details
          },
          borderRadius: BorderRadius.circular(24),
          splashColor: AppColors.primary.withValues(alpha: 0.1),
          highlightColor: AppColors.primary.withValues(alpha: 0.05),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: categoryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.lightbulb,
                        color: categoryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            challenge['name'] ?? '',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          // Text(
                          //   categoryName != null || categoryName != ''
                          //       ? categoryName['name']
                          //       : 'Special',
                          //   style: GoogleFonts.poppins(
                          //     fontSize: 13,
                          //     color: categoryColor.withValues(alpha: 0.8),
                          //     fontWeight: FontWeight.w500,
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [
                            AppColors.primary,
                            AppColors.secondary,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.3),
                            blurRadius: 8,
                            spreadRadius: 0,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        '${NumberFormat("#,##0.00", "en_US").format(reward)} ETB',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                if (isStarted) ...[
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'Progress',
                                  style: GoogleFonts.poppins(
                                    fontSize: 13,
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '${(progress * 100).toInt()}%',
                                  style: GoogleFonts.poppins(
                                    fontSize: 13,
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Stack(
                              children: [
                                // Background
                                Container(
                                  height: 10,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                // Progress
                                Container(
                                  height: 10,
                                  width: MediaQuery.of(context).size.width *
                                      0.6 *
                                      progress,
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        AppColors.primary,
                                        AppColors.secondary,
                                      ],
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                    boxShadow: [
                                      BoxShadow(
                                        color: AppColors.primary
                                            .withValues(alpha: 0.3),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 20),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.orange.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: Colors.orange[700],
                            ),
                            const SizedBox(width: 6),
                            Text(
                              '${score.toInt()}/${winningPoints.toInt()}',
                              style: GoogleFonts.poppins(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                color: Colors.orange[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 10,
                    runSpacing: 8,
                    children: [
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () async {
                            final bool isStarted =
                                challenge['status'] == 1 ? true : false;
                            final bool isCompleted = challenge['status'] == 2;
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ChallengeScreen(
                                  selectedChallenge: {
                                    'id': challenge['id'] ?? 0,
                                    'name': challenge['name'] ?? '',
                                    'winningPoints':
                                        _extractWinningPoints(challenge),
                                    'reward': challenge['reward'] ?? 0,
                                    'status':
                                        isStarted ? (isCompleted ? 2 : 1) : 0,
                                    'score': num.tryParse(
                                            challenge['score']?.toString() ??
                                                '0') ??
                                        0,
                                    'categoryId': challenge['category_id'],
                                    'startDate': challenge['startDate']
                                        ?.toIso8601String(),
                                    'endDate':
                                        challenge['endDate']?.toIso8601String(),
                                    'winning_rules': {
                                      'level': _extractLevel(challenge),
                                      'winning_points':
                                          _extractWinningPoints(challenge),
                                      'rank': _extractRank(challenge)
                                    },
                                  },
                                ),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: AppColors.primary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 4,
                          ),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.refresh_rounded,
                                size: 24,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Continue Challenge',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  Wrap(
                    spacing: 10,
                    runSpacing: 8,
                    alignment: WrapAlignment.spaceBetween,
                    children: [
                      _buildCompletionInfo(
                        Icons.star,
                        '${challenge['user_progress']['score'] ?? 0} pts',
                        Colors.amber,
                      ),
                      //Icons.emoji_events
                      _buildCompletionInfo(
                        Icons.emoji_flags,
                        'Rank #${challenge['user_progress']['rank'] ?? 0}',
                        Colors.green,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Wrap(
                        spacing: 10,
                        runSpacing: 8,
                        children: [
                          _buildInfoChip(
                            Icons.calendar_today,
                            "${_formatDate(challenge['start_date'])} - ${_formatDate(challenge['end_date'])}",
                            Colors.purple,
                          ),
                          _buildInfoChip(
                            Icons.check_circle_outline,
                            '${challenge['is_winner'] ? 'Winner' : 'Lost the Game'} ',
                            challenge['is_winner'] ? Colors.green : Colors.red,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              text,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: color.withValues(alpha: 0.8),
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletionInfo(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Flexible(
            child: Text(
              text,
              style: GoogleFonts.poppins(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: color.withValues(alpha: 0.8),
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }



  String _formatDate(String dateString) {
    if (dateString.isEmpty) return '';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  // Helper method to extract winning points from challenge data
  int _extractWinningPoints(Map<String, dynamic> challenge) {
    // First try to get from winning_rules
    if (challenge['winning_rules'] != null &&
        challenge['winning_rules']['winning_points'] != null) {
      final points =
          num.tryParse(challenge['winning_rules']['winning_points'].toString());
      if (points != null) return points.toInt();
    }

    // Fallback to direct winning_points
    if (challenge['winning_points'] != null) {
      final points = num.tryParse(challenge['winning_points'].toString());
      if (points != null) return points.toInt();
    }

    // Default value if nothing found
    return 0;
  }

  int _extractLevel(Map<String, dynamic> challenge) {
    // First try to get from winning_rules
    if (challenge['winning_rules'] != null &&
        challenge['winning_rules']['level'] != null) {
      final points =
          num.tryParse(challenge['winning_rules']['level'].toString());
      if (points != null) return points.toInt();
    }

    // Fallback to direct level
    if (challenge['level'] != null) {
      final points = num.tryParse(challenge['level'].toString());
      if (points != null) return points.toInt();
    }

    // Default value if nothing found
    return 0;
  }

  int _extractRank(Map<String, dynamic> challenge) {
    // First try to get from winning_rules
    if (challenge['winning_rules'] != null &&
        challenge['winning_rules']['rank'] != null) {
      final points =
          num.tryParse(challenge['winning_rules']['rank'].toString());
      if (points != null) return points.toInt();
    }

    // Fallback to direct rank
    if (challenge['rank'] != null) {
      final points = num.tryParse(challenge['rank'].toString());
      if (points != null) return points.toInt();
    }

    // Default value if nothing found
    return 0;
  }
}
