class LeaderboardEntry {
  final int id;
  final String fullName;
  final int score;
  final int rank;
  final String avatar;
  final int? challengeId;
  final bool? isProfileAvailable;
  LeaderboardEntry({
    required this.id,
    required this.fullName,
    required this.score,
    required this.rank,
    required this.challengeId,
    this.avatar = '',
    required this.isProfileAvailable,
  });

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) {
    // Handle the case where avatar might be a Map or a String
    String avatarStr = '';
    final dynamic avatarValue = json['avatar'];
    if (avatarValue is String) {
      avatarStr = avatarValue;
    } else if (avatarValue != null) {
      // If it's not a String but some other value, convert to string representation
      avatarStr = '👤'; // Default emoji for non-string avatars
    }

    return LeaderboardEntry(
      id: json['id'] ?? 0,
      fullName: json['fullName'] ?? 'Anonymous',
      score: json['score'] ?? 0,
      rank: json['rank'] ?? 0,
      avatar: avatarStr,
      challengeId: json['challengeId'] ?? 0,
      isProfileAvailable: json['isProfileAvailable'] ?? false,
    );
  }
}
