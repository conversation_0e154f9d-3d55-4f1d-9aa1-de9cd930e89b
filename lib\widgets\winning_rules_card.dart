import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/challenge.dart';

class WinningRulesCard extends StatelessWidget {
  final WinningRules? winningRules;
  final int currentScore;
  final int? currentLevel;
  final int? currentRank;

  const WinningRulesCard({
    super.key,
    required this.winningRules,
    required this.currentScore,
    this.currentLevel,
    this.currentRank,
  });

  @override
  Widget build(BuildContext context) {
    // If no winning rules are provided, show a default message
    if (winningRules == null) {
      return Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Winning Conditions',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Complete the challenge to win rewards!',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Winning Conditions',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'To win this challenge, you must meet ALL of the following conditions:',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),
            _buildConditionItem(
              icon: Icons.star,
              title: 'Points',
              required: '${winningRules!.winningPoints}',
              current: '$currentScore',
              isCompleted: currentScore >= winningRules!.winningPoints,
              color: Colors.amber,
            ),
            const SizedBox(height: 12),
            _buildConditionItem(
              icon: Icons.trending_up,
              title: 'Level',
              required: '${winningRules!.level}',
              current: '${currentLevel ?? 1}',
              isCompleted: (currentLevel ?? 1) >= winningRules!.level,
              color: Colors.blue,
            ),
            const SizedBox(height: 12),
            _buildConditionItem(
              icon: Icons.emoji_events,
              title: 'Rank',
              required: '${winningRules!.rank}',
              current: '${currentRank ?? "-"}',
              isCompleted:
                  currentRank != null && currentRank! >= winningRules!.rank,
              color: Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConditionItem({
    required IconData icon,
    required String title,
    required String required,
    required String current,
    required bool isCompleted,
    required Color color,
  }) {
    // Use a layout that adapts to different screen sizes
    return LayoutBuilder(
      builder: (context, constraints) {
        // For very small screens, use a more compact layout
        if (constraints.maxWidth < 300) {
          return Column(
            children: [
              // Icon and title in a row
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: color.withAlpha(50),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(icon, color: color, size: 16),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // Values and status icon
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Req: $required | Now: $current',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  Icon(
                    isCompleted
                        ? Icons.check_circle
                        : Icons.radio_button_unchecked,
                    color: isCompleted ? Colors.green : Colors.grey,
                    size: 16,
                  ),
                ],
              ),
            ],
          );
        }

        // For normal screens, use the original layout
        return Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withAlpha(50),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    'Required: $required | Current: $current',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: isCompleted
                    ? Colors.green.withAlpha(50)
                    : Colors.grey.withAlpha(50),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Icon(
                isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
                color: isCompleted ? Colors.green : Colors.grey,
                size: 20,
              ),
            ),
          ],
        );
      },
    );
  }
}
