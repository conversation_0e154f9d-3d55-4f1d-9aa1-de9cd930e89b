
// import 'package:achawach/core/utils/apiClient.dart';
// import 'package:achawach/core/utils/tokenManager.dart';

// class AuthService {
//   static const String isLoggedInKey = 'isLoggedIn';
//   final ApiClient apiClient = ApiClient();

//   // Check if the user is logged in locally
//   static Future<bool> isLoggedIn() async {
//     String? token = await TokenManager.getToken();
//     return token != null && token.isNotEmpty;
//   }

//   // Set login status
//   static Future<void> setLoggedIn(bool value, {String? token}) async {
//     if (value && token != null) {
//       await TokenManager.saveToken(token);
//     } else {
//       await TokenManager.clearToken();
//     }
//   }
// }
