
import 'package:achawach/widgets/cool_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:achawach/core/constants/app_colors.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CoolAppBar(title: 'Help & FAQ'),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildSectionTitle(context, 'Frequently Asked Questions'),
          const SizedBox(height: 16),
          _buildFaqItem(
            'How do I reset my password?',
            'You can reset your password by going to the login screen and tapping on the "Forgot Password" link. You will then receive an email with instructions on how to reset your password.',
          ),
          _buildFaqItem(
            'How do I change my profile picture?',
            'You can change your profile picture by going to your profile and tapping on the edit icon. You will then be able to select a new profile picture from your gallery or take a new one.',
          ),
          _buildFaqItem(
            'How do I earn points?',
            'You can earn points by completing challenges and answering questions correctly. The more challenges you complete and the more questions you answer correctly, the more points you will earn.',
          ),
          _buildFaqItem(
            'How do I use my points?',
            'You can use your points to redeem rewards in the rewards store. The rewards store is updated regularly with new and exciting rewards, so be sure to check back often.',
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: AppColors.text,
            fontWeight: FontWeight.bold,
          ),
    );
  }

  Widget _buildFaqItem(String question, String answer) {
    return ExpansionTile(
      title: Text(question),
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(answer),
        ),
      ],
    );
  }
}
