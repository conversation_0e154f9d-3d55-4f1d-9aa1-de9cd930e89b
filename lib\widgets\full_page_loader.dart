import 'package:achawach/core/constants/app_colors.dart';
import 'package:flutter/material.dart';

class FullPageLoader extends StatefulWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color? primaryColor;
  final Color? secondaryColor;
  final Duration animationDuration;

  const FullPageLoader({
    super.key,
    this.title = 'Loading...',
    this.subtitle = 'Please wait while we prepare your experience',
    this.icon = Icons.hourglass_empty_rounded,
    this.primaryColor,
    this.secondaryColor,
    this.animationDuration = const Duration(milliseconds: 1200),
  });

  @override
  State<FullPageLoader> createState() => _FullPageLoaderState();
}

class _FullPageLoaderState extends State<FullPageLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = widget.primaryColor ?? AppColors.primary;
    final secondaryColor = widget.secondaryColor ?? AppColors.secondary;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              primaryColor.withValues(alpha: 0.1),
              secondaryColor.withValues(alpha: 0.1),
              AppColors.background,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Animated logo/icon container with pulsing effect
              AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  final pulseScale = 1.0 +
                      (0.1 *
                          (1 - (_animationController.value - 0.5).abs() * 2));
                  return Transform.scale(
                    scale: pulseScale,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            primaryColor,
                            secondaryColor,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: primaryColor
                                .withValues(alpha: 0.3 * pulseScale),
                            blurRadius: 20 * pulseScale,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Icon(
                        widget.icon,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 40),

              // Loading animation
              SizedBox(
                width: 60,
                height: 60,
                child: CircularProgressIndicator(
                  strokeWidth: 4,
                  valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                  backgroundColor: primaryColor.withValues(alpha: 0.2),
                ),
              ),
              const SizedBox(height: 30),

              // Loading text
              Text(
                widget.title,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.text,
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(height: 12),

              // Subtitle
              Text(
                widget.subtitle,
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.text.withValues(alpha: 0.7),
                  letterSpacing: 0.3,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),

              // Animated dots
              _buildLoadingDots(primaryColor),
            ],
          ),
        ),
      ),
    );
  }

  // Build animated loading dots
  Widget _buildLoadingDots(Color primaryColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            final delay = index * 0.2;
            final animationValue = (_animationController.value + delay) % 1.0;
            final scale = 0.5 + (0.5 * (1 - (animationValue - 0.5).abs() * 2));

            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              child: Transform.scale(
                scale: scale,
                child: Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: primaryColor.withValues(
                      alpha: 0.3 + (0.7 * scale),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}

// Specialized loaders for different screens
class ChallengeLoader extends StatelessWidget {
  const ChallengeLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return const FullPageLoader(
      title: 'Loading Challenge...',
      subtitle: 'Preparing your gaming experience',
      icon: Icons.emoji_events_rounded,
    );
  }
}

class GameLoader extends StatelessWidget {
  const GameLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return const FullPageLoader(
      title: 'Starting Game...',
      subtitle: 'Get ready for an amazing challenge',
      icon: Icons.sports_esports_rounded,
    );
  }
}

class ProfileLoader extends StatelessWidget {
  const ProfileLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return const FullPageLoader(
      title: 'Loading Profile...',
      subtitle: 'Fetching your latest achievements',
      icon: Icons.person_rounded,
    );
  }
}

class LeaderboardLoader extends StatelessWidget {
  const LeaderboardLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return const FullPageLoader(
      title: 'Loading Leaderboard...',
      subtitle: 'Checking the latest rankings',
      icon: Icons.leaderboard_rounded,
    );
  }
}

class DataLoader extends StatelessWidget {
  final String message;
  
  const DataLoader({
    super.key,
    this.message = 'Loading data...',
  });

  @override
  Widget build(BuildContext context) {
    return FullPageLoader(
      title: 'Loading...',
      subtitle: message,
      icon: Icons.cloud_download_rounded,
    );
  }
}
