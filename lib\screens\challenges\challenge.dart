// ignore_for_file: use_build_context_synchronously

import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/core/constants/constants.dart';
import 'package:achawach/models/challenge.dart';
import 'package:achawach/models/leaderboard_entry.dart';
import 'package:achawach/providers/challenge_provider.dart';
import 'package:achawach/providers/terms_provider.dart';
import 'package:achawach/screens/challenges/challenge_start.dart';
import 'package:achawach/services/api_refill_service.dart';
import 'package:achawach/services/media_service.dart';
import 'package:achawach/services/service_locator.dart';
import 'package:achawach/services/socket_service.dart';
import 'package:achawach/widgets/noLivesScreen.dart';
import 'package:achawach/widgets/terms_and_conditions_dialog.dart';
import 'package:achawach/widgets/winning_rules_card.dart';
import 'package:achawach/widgets/full_page_loader.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

class ChallengeScreen extends StatefulWidget {
  final Map<String, dynamic> selectedChallenge;

  const ChallengeScreen({super.key, required this.selectedChallenge});

  @override
  State<ChallengeScreen> createState() => _ChallengeScreenState();
}

class _ChallengeScreenState extends State<ChallengeScreen>
    with SingleTickerProviderStateMixin {
  // Animation controllers
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Challenge data
  Map<String, dynamic>? _life;
  int _categoryId = 0;

  // Loading states
  bool _isLoadingLife = false;
  bool _isCheckingLife = false;
  bool _isLoadingLeaderboard = false;
  bool _isFullPageLoading = true;

  // Leaderboard data
  List<LeaderboardEntry> _leaderboardEntries = [];
  Timer? _leaderboardTimer;

  // Services
  final ApiRefillService _apiRefillService = ApiRefillService();
  final SocketService _socketService = SocketService.instance;

  // Subscriptions
  StreamSubscription? _gameStateSubscription;
  StreamSubscription? _gameleaderBoardSubscription;
  StreamSubscription? _errorSubscription;
  late MediaService _mediaService;
  late ServiceLocator _serviceLocator;

  @override
  void initState() {
    super.initState();

    if (mounted) {
      _setupAnimations();
      _initializeScreen();
      _categoryId = widget.selectedChallenge['categoryId'] ?? 0;
      _serviceLocator = ServiceLocator();
      _serviceLocator.initializeServices(
        context: context,
        challengeId: 0,
        categoryId: 0,
      );
      _mediaService = _serviceLocator.mediaService;
    }
  }

  // Initialize screen with loading sequence
  Future<void> _initializeScreen() async {
    setState(() => _isFullPageLoading = true);

    try {
      // Run initialization tasks sequentially for better error handling
      await _checkAndLoadLifeData();
      _setupSocketConnection();

      // Add a minimum loading time for smooth UX
      await Future.delayed(const Duration(milliseconds: 1500));
    } catch (e) {
      debugPrint('Error during screen initialization: $e');
    } finally {
      if (mounted) {
        setState(() => _isFullPageLoading = false);

        // Stop repeating animation and start forward animation for content
        _animationController.stop();
        _animationController.reset();
        _animationController.forward();
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Check and reload life data when dependencies change
    if (!_isCheckingLife) {
      _checkAndLoadLifeData();
    }
  }

  @override
  void dispose() {
    // Clean up animation controller
    _animationController.dispose();

    // Cancel any active timers
    _leaderboardTimer?.cancel();

    // Cancel all socket subscriptions
    _gameStateSubscription?.cancel();
    _gameleaderBoardSubscription?.cancel();
    _errorSubscription?.cancel();
    _socketService.disconnect();

    // Note: We don't disconnect the socket here because:
    // 1. Other components might still be using it (like ScoreLifeLevel widget)
    // 2. The socket is managed as a singleton and should persist between screens
    // 3. Disconnecting here could cause issues if the user navigates back quickly

    super.dispose();
  }

  // Setup animations for the UI
  void _setupAnimations() {
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
      ),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.35),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
      ),
    );

    // Start repeating animation for loading dots
    _animationController.repeat();
  }

  // Check if lives should be refilled and load life data
  Future<void> _checkAndLoadLifeData() async {
    if (!mounted) return;

    setState(() => _isCheckingLife = true);

    try {
      // Get the challenge ID from the selected challenge
      final int challengeId = widget.selectedChallenge['id'];

      // Call the check-life endpoint to see if lives should be refilled
      await _apiRefillService.checkLife(challengeId);

      // After checking life, load the updated life data
      await _loadLifeData();
    } catch (e) {
      debugPrint('Error checking life: $e');
      // Even if check-life fails, still try to load life data
      await _loadLifeData();
    } finally {
      if (mounted) {
        setState(() => _isCheckingLife = false);
      }
    }
  }

  // Load life data for the current challenge
  Future<void> _loadLifeData() async {
    if (!mounted) return;
    final bool isStarted = widget.selectedChallenge['status'] == 1;
    if (!isStarted) return;

    setState(() => _isLoadingLife = true);

    try {
      final challengeProvider =
          Provider.of<ChallengeProvider>(context, listen: false);
      await challengeProvider.getChallengeLife(widget.selectedChallenge['id']);

      if (mounted) {
        setState(() {
          _life = challengeProvider.challengeLife;
          _isLoadingLife = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading life data: $e');
      if (mounted) {
        setState(() => _isLoadingLife = false);
      }
    }
  }

  // Setup socket connection for real-time updates
  void _setupSocketConnection() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // Try both possible token keys, matching what's used in ApiService
      final token = prefs.getString('auth_token') ?? prefs.getString('token');

      if (token == null || token.isEmpty) {
        // If we don't have a token, show an error
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Unable to connect to real-time updates: No authentication token found. Please log in again.'),
              duration: Duration(seconds: 5),
            ),
          );
        }
        return;
      }

      _connectWithToken(token);
    } catch (e) {
      debugPrint('Error in _setupSocketConnection: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error connecting to socket: $e')),
        );
      }
    }
  }

  // Connect to the socket with the given token
  void _connectWithToken(String token) {
    // No need to disconnect here - the SocketService.connect() method
    // will handle cleanup of any previous socket if needed
    _socketService.disconnect();
    final int challengeId = widget.selectedChallenge['id'];
    _socketService.connect(token, challengeId.toString());

    // --- Leaderboard Listener ---
    _gameleaderBoardSubscription?.cancel();
    _gameleaderBoardSubscription =
        _socketService.gameleaderBoardStream.listen((data) {
      // Listen directly for the data Map

      // Validate the received data structure
      if (data.containsKey('leaderboardData')) {
        final List<dynamic>? rawEntries =
            data['leaderboardData'] as List<dynamic>?; // Use safe casting

        if (rawEntries != null) {
          // Process the leaderboard data
          List<LeaderboardEntry> currentLeaderboard =
              []; // Create a new list for this update
          for (var entry in rawEntries) {
            if (entry is Map<String, dynamic>) {
              // Ensure each entry is a map
              try {
                // Defensive parsing: Use ?? for defaults if keys might be missing
                int userId = entry['userId'] ?? 0;
                String fullName = entry['fullName'] ?? 'Unknown User';
                // Ensure score and rank are treated as numbers (int or num)
                num score = entry['score'] ?? 0;
                int rank = entry['rank'] ?? 0;
                // Handle '1' as string or int for boolean check
                bool isProfileAvailable =
                    (entry['isProfileImageAvailable']?.toString() == '1');
                String avatarPath = entry['avatar'] ?? '';

                // Construct avatar URL carefully
                String avatarUrl;
                if (isProfileAvailable && avatarPath.isNotEmpty) {
                  // Ensure no double slashes and correct base IP format
                  String baseIp = AppConstants.apiBaseIP.endsWith('/')
                      ? AppConstants.apiBaseIP
                      : '${AppConstants.apiBaseIP}/';

                  String path = avatarPath.startsWith('/')
                      ? avatarPath.substring(1)
                      : avatarPath;
                  avatarUrl = '$baseIp$path';
                } else {
                  // Use a more reliable placeholder or the default emoji
                  avatarUrl = avatarPath.isNotEmpty ? avatarPath : '👤';
                }

                currentLeaderboard.add(LeaderboardEntry(
                    id: userId,
                    fullName: fullName,
                    score: score
                        .toInt(), // Convert num score back to int if needed by model
                    rank: rank,
                    avatar: avatarUrl,
                    challengeId: challengeId,
                    isProfileAvailable: isProfileAvailable));
              } catch (e) {
                debugPrint(
                    "Error parsing leaderboard entry: $entry, Error: $e");
                // Optionally add a placeholder entry or skip
              }
            }
          }

          // Update the UI with the processed list
          _loadLeaderboard(currentLeaderboard);
        } else {
          _loadLeaderboard([]); // Show empty list
        }
      } else {
        _loadLeaderboard([]); // Show empty list
      }
    }, onError: (error) {
      debugPrint('Error on gameleaderBoardStream: $error');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Leaderboard update error: $error')),
        );
        // Optionally clear the UI on error
        _loadLeaderboard([]);
      }
    });

    // --- Game State Listener ---
    _gameStateSubscription?.cancel();
    _gameStateSubscription = _socketService.gameStateStream.listen((state) {
      if (mounted) {
        setState(() {
          // Update _life based on gameState if needed
          _life = {
            'life':
                state['life'] ?? _life?['life'] ?? 0, // Keep existing if null
            'max': state['maxLife'] ??
                _life?['max'] ??
                10, // Keep existing if null
            'level':
                state['level'] ?? _life?['level'] ?? 1, // Keep existing if null
            // Add other relevant fields from gameState if needed
          };

          _isLoadingLife = false; // Assume loading stops when state arrives
        });
      }
    }, onError: (error) {
      debugPrint('Error on gameStateStream: $error');
      // Handle gameState stream errors
    });

    // --- Error Listener ---
    _errorSubscription?.cancel();
    _errorSubscription = _socketService.errorStream.listen((error) {
      debugPrint('Socket general error received: $error');
    });
  }

  // Update the leaderboard data
  void _loadLeaderboard(List<LeaderboardEntry> data) {
    if (!mounted) return;

    setState(() {
      _leaderboardEntries = data;
      _isLoadingLeaderboard = false;
    });
  }

  // Method to handle starting or continuing a challenge
  Future<void> _startChallenge(bool isStarted) async {
    if (!mounted) return;

    // Store context reference to avoid async gap issues
    final BuildContext currentContext = context;

    try {
      final challengeProvider =
          Provider.of<ChallengeProvider>(currentContext, listen: false);

      // Show loading indicator
      if (!mounted) return;
      showDialog(
        context: currentContext,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        },
      );

      Map<String, dynamic> updatedChallenge;
      if (!isStarted) {
        // Call the start challenge API only for new challenges
        await challengeProvider.startChallenge(
            widget.selectedChallenge['id'], _categoryId);
        updatedChallenge = {
          ...widget.selectedChallenge,
          'status': 1, // Mark as started
        };
      } else {
        updatedChallenge = widget.selectedChallenge;
      }

      if (!mounted) return;

      if (_life?['life'] == 0) {
        // Close loading indicator
        Navigator.pop(currentContext);

        // Show no lives screen
        if (!mounted) return;
        _mediaService.playLost();
        Navigator.push(
            // ignore: use_build_context_synchronously
            currentContext,
            MaterialPageRoute(
                builder: (context) => NoLivesScreen(
                      onBuyLives: () => {},
                      categoryId: _categoryId,
                      isStarted: false,
                    )));
      } else {
        // Close loading indicator
        Navigator.pop(currentContext);

        // Navigate to start screen with updated data
        if (!mounted) return;
        await Navigator.push(
          // ignore: use_build_context_synchronously
          currentContext,
          MaterialPageRoute(
            builder: (context) => ChallengeStartScreen(
              selectedChallenge: updatedChallenge,
              categoryId: _categoryId,
            ),
          ),
        );

        // After returning from ChallengeStartScreen
        if (!mounted) return;

        // Refresh challenge data from API
        final challengeProvider =
            Provider.of<ChallengeProvider>(currentContext, listen: false);

        // Refresh life data
        await challengeProvider
            .getChallengeLife(widget.selectedChallenge['id']);

        // Get updated challenge data
        await challengeProvider.fetchStartedChallenges(_categoryId);

        // Also refresh not started challenges to update the UI
        await challengeProvider.fetchNotStartedChallenges(_categoryId);

        // Find the updated challenge in the list
        final updatedChallenges = challengeProvider.startedChallenges;
        final updatedChallengeList = updatedChallenges
            .where((c) => c.id == widget.selectedChallenge['id'])
            .toList();

        if (!mounted) return;

        // Update the selected challenge with new data
        setState(() {
          if (updatedChallengeList.isNotEmpty) {
            widget.selectedChallenge['score'] =
                updatedChallengeList.first.score ?? 0;
          }
          _life = challengeProvider.challengeLife;
        });

        // Return a result indicating that a challenge was started
        if (!mounted) return;
        Navigator.of(currentContext).pop({'challengeStarted': true});
      }
    } catch (e) {
      // Close loading indicator if it's showing
      if (mounted && Navigator.canPop(currentContext)) {
        Navigator.pop(currentContext);
      }

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(currentContext).showSnackBar(
          SnackBar(
            content: Text('Failed to join challenge: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Display a life counter widget
  Widget _buildLifeDisplay() {
    final bool isStarted = widget.selectedChallenge['status'] == 1;

    if (!isStarted) {
      return const SizedBox.shrink();
    }

    if (_isLoadingLife) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      );
    }

    if (_life == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.favorite, color: Colors.red, size: 24),
          const SizedBox(width: 8),
          Text(
            '${_life!['life'] ?? 0}',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 16),
                const SizedBox(width: 4),
                Text(
                  'Lvl ${_life!['level'] ?? 1}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build the challenge card with join/continue button
  Widget _buildChallengeCard(bool isStarted, bool isFinished) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.8),
            AppColors.secondary.withValues(alpha: 0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildStatusBadge(
                  isStarted ? 'Started' : (isFinished ? 'Finished' : 'New')),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.emoji_events_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            widget.selectedChallenge['name'] ?? 'Challenge',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Complete this challenge to earn rewards and points!',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: isFinished || _isLoadingLife || _life == null
                  ? null
                  : () async {
                      try {
                        // We don't need to use challengeProvider here, but we'll keep termsProvider
                        // final termsProvider =
                        //     Provider.of<TermsProvider>(context, listen: false);

                        // Check if user has already agreed to terms
                        if (!isStarted) {
                          // Show terms and conditions dialog
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return TermsAndConditionsDialog(
                                onAgree: () async {
                                  // Close the terms dialog
                                  Navigator.pop(context);

                                  // Continue with starting the challenge
                                  await _startChallenge(isStarted);
                                },
                                onDecline: () {
                                  // Close the terms dialog and do nothing
                                  Navigator.pop(context);
                                },
                              );
                            },
                          );
                        } else {
                          // User has already agreed to terms, proceed directly
                          await _startChallenge(isStarted);
                        }
                      } catch (e) {
                        // Show error message
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                  'Failed to join challenge: ${e.toString()}'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 4,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    !isStarted
                        ? Icons.play_arrow_rounded
                        : Icons.refresh_rounded,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    isStarted ? 'Continue Challenge' : 'Join Challenge',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build a status badge for the challenge
  Widget _buildStatusBadge(String status) {
    Color color;
    IconData icon;
    switch (status.toLowerCase()) {
      case 'new':
        color = Colors.green;
        icon = Icons.fiber_new_rounded;
        break;
      case 'started':
        color = Colors.orange;
        icon = Icons.play_circle_filled_rounded;
        break;
      default:
        color = Colors.blue;
        icon = Icons.check_circle_rounded;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(width: 4),
          Text(
            status,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // Build the progress section for started challenges
  Widget _buildProgressSection() {
    // Extract winning rules if available
    WinningRules? winningRules;
    if (widget.selectedChallenge.containsKey('winning_rules')) {
      winningRules =
          WinningRules.fromJson(widget.selectedChallenge['winning_rules']);
    }

    // Calculate overall progress based on all winning conditions
    double pointsProgress = widget.selectedChallenge['score'] /
        (winningRules?.winningPoints ??
            widget.selectedChallenge['winningPoints'] ??
            1);

    // Cap progress at 1.0 (100%)
    pointsProgress = pointsProgress > 1.0 ? 1.0 : pointsProgress;

    // Get current level progress
    int currentLevel = _life?['level'] ?? 1;
    int targetLevel = winningRules?.level ?? 1;
    double levelProgress = currentLevel / targetLevel;
    levelProgress = levelProgress > 1.0 ? 1.0 : levelProgress;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Your Progress',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          // Points progress
          _buildProgressItem(
            label: 'Points',
            progress: pointsProgress,
            valueText:
                '${widget.selectedChallenge['score']} / ${winningRules?.winningPoints ?? widget.selectedChallenge['winningPoints'] ?? 0}',
            color: Colors.amber,
          ),
          const SizedBox(height: 12),
          // Level progress
          _buildProgressItem(
            label: 'Level',
            progress: levelProgress,
            valueText: '$currentLevel / ${winningRules?.level ?? 1}',
            color: Colors.blue,
          ),
        ],
      ),
    );
  }

  // Build a progress item with label, value, and progress bar
  Widget _buildProgressItem({
    required String label,
    required double progress,
    required String valueText,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Use a more responsive layout for the progress item labels
        LayoutBuilder(
          builder: (context, constraints) {
            // For very small screens, stack the label and value
            if (constraints.maxWidth < 250) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    valueText,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: color,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              );
            }

            // For normal screens, use the original layout
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                ),
                Text(
                  valueText,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[200],
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 8,
          borderRadius: BorderRadius.circular(4),
        ),
      ],
    );
  }

  // Build the rewards section
  Widget _buildRewardsSection() {
    // Extract winning rules if available
    WinningRules? winningRules;
    if (widget.selectedChallenge.containsKey('winning_rules')) {
      winningRules =
          WinningRules.fromJson(widget.selectedChallenge['winning_rules']);
    }
    int currentScore = widget.selectedChallenge['score'] ?? 0;
    int level = _life?['level'] ??
        widget.selectedChallenge['level'] ??
        1; // Default to level 1 if not available
    int rank = _life?['rank'] ?? widget.selectedChallenge['rank'] ?? 0;

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Rewards And Points',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildRewardItem(
                      icon: Icons.stars_rounded,
                      value:
                          "${widget.selectedChallenge['score'] ?? 0}/${winningRules?.winningPoints ?? 0}",
                      label: 'Points',
                      color: Colors.amber,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildRewardItem(
                      icon: Icons.attach_money_sharp,
                      value:
                          '${NumberFormat("#,##0.00", "en_US").format(widget.selectedChallenge['reward'] ?? 0)} ETB',
                      label: 'Reward',
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        _buildPrizeDistributionCard(),
        const SizedBox(height: 24),
        // Add the winning rules card
        if (winningRules != null)
          WinningRulesCard(
            winningRules: winningRules,
            currentScore: currentScore,
            currentLevel: level,
            currentRank: rank,
          ),
      ],
    );
  }

  Widget _buildPrizeDistributionCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Prize Distribution',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          _buildPrizeRow('🥇', '1st Place', '45% of the prize pool'),
          const SizedBox(height: 12),
          _buildPrizeRow('🥈', '2nd Place', '25% of the prize pool'),
          const SizedBox(height: 12),
          _buildPrizeRow('🥉', '3rd Place', '15% of the prize pool'),
          const SizedBox(height: 12),
          _buildPrizeRow('🏆', 'Top 20%', 'Share of the remaining 15%'),
        ],
      ),
    );
  }

  Widget _buildPrizeRow(String icon, String title, String subtitle) {
    return Row(
      children: [
        Text(icon, style: const TextStyle(fontSize: 24)),
        const SizedBox(width: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build a reward item with icon, value, and label
  Widget _buildRewardItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: color.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Build the leaderboard section
  Widget _buildLeaderboardSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Leaderboard',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  children: [
                    _isLoadingLeaderboard
                        ? SizedBox(
                            width: 12,
                            height: 12,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.green.shade600,
                              ),
                            ),
                          )
                        : Container(
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              color: Colors.green.shade600,
                              shape: BoxShape.circle,
                            ),
                          ),
                    const SizedBox(width: 8),
                    Text(
                      'Live',
                      style: TextStyle(
                        color: Colors.green.shade600,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_leaderboardEntries.isEmpty && !_isLoadingLeaderboard)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.people_outline,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No participants yet',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Be the first to join!',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _leaderboardEntries.length,
              itemBuilder: (context, index) {
                final entry = _leaderboardEntries[index];
                final isTopThree = entry.rank <= 3;
                final rankColor = isTopThree
                    ? [
                        Colors.amber,
                        Colors.grey[400],
                        Colors.brown[300],
                      ][entry.rank - 1]
                    : Colors.grey[600];

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: index % 2 == 0 ? Colors.grey[50] : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey[200]!,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: rankColor?.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: isTopThree
                              ? Icon(
                                  Icons.emoji_events_rounded,
                                  color: rankColor,
                                  size: 20,
                                )
                              : Text(
                                  '${entry.rank}',
                                  style: TextStyle(
                                    color: rankColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      if (entry.isProfileAvailable == true) ...[
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            image: DecorationImage(
                              image: NetworkImage(entry.avatar),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ] else ...[
                        Container(
                          width: 40,
                          height: 40,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              // Use a default emoji if avatar is not a valid string
                              entry.avatar.isNotEmpty ? entry.avatar : '👤',
                              style: TextStyle(
                                fontSize: 24,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        ),
                      ],
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              entry.fullName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            Row(
                              children: [
                                Icon(
                                  Icons.star,
                                  size: 14,
                                  color: Colors.amber[400],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${entry.score} points',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      if (isTopThree)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: rankColor?.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            ['🥇 1st', '🥈 2nd', '🥉 3rd'][entry.rank - 1],
                            style: TextStyle(
                              color: rankColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Show full-page loader during initialization
    if (_isFullPageLoading) {
      return const ChallengeLoader();
    }

    final bool isStarted = widget.selectedChallenge['status'] == 1;
    final bool isFinished = widget.selectedChallenge['status'] == 2;

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // Pop and return to previous screen
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.selectedChallenge['name'] ?? 'Challenge'),
          backgroundColor: AppColors.primary,
          actions: [
            _buildLifeDisplay(),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Challenge card with join/continue button
                  _buildChallengeCard(isStarted, isFinished),
                  const SizedBox(height: 24),
                  _buildRewardsSection(),
                  const SizedBox(height: 24),
                  // Only show these sections for started challenges
                  if (isStarted || isFinished) ...[
                    // Progress section
                    _buildProgressSection(),
                    const SizedBox(height: 24),

                    // Rewards section

                    // Leaderboard section
                    _buildLeaderboardSection(),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
