# Google Fonts Fix

This document explains how to fix the Google Fonts connectivity issue in the app.

## The Problem

The app was encountering the following error:

```
Error: google_fonts was unable to load font Poppins-Medium because the following exception occurred:
Exception: Failed to load font with url https://fonts.gstatic.com/s/a/a05eb17c43309b14b916303c48995b19407a7cdcf47bc6d8085d464722627918.ttf: ClientException with SocketException: Failed host lookup: 'fonts.gstatic.com' (OS Error: No address associated with hostname, errno = 7)
```

This error occurs when the app cannot connect to Google Fonts servers to download the font files.

## The Solution

The solution is to include the Poppins font files locally in the app instead of downloading them from Google Fonts servers. Here's what has been done:

1. Created a `fonts` directory in the `assets` folder
2. Updated the `pubspec.yaml` to include the Poppins font files
3. Updated the app theme to use the local Poppins font
4. Created a `FontUtils` class to replace `GoogleFonts` usage
5. Updated key files to use the local font instead of Google Fonts

## How to Complete the Fix

1. Download the Poppins font files from Google Fonts or another source (see FONT_DOWNLOAD_INSTRUCTIONS.md)
2. Place the font files in the `assets/fonts` directory
3. Run `flutter pub get` to update the dependencies
4. Update any remaining files that use `GoogleFonts` to use the `AppFonts` utility instead

## Files Modified

- `pubspec.yaml`: Added font configuration
- `lib/main.dart`: Updated theme to use local fonts
- `lib/core/utils/font_utils.dart`: Created utility class for fonts
- `lib/widgets/questionsContent.dart`: Updated to use local fonts

## Additional Files to Update

You should update all files that use `GoogleFonts` to use the `AppFonts` utility instead. Here are some files that might need updating:

- `lib/widgets/top_challenges_carousel.dart`
- `lib/screens/challenges/joined_challenges_screen.dart`
- `lib/widgets/score_life_level.dart`
- `lib/widgets/winning_rules_card.dart`
- `lib/screens/settings/settings_screen.dart`
- `lib/screens/challenges/special_challenge_start.dart`
- `lib/screens/challenges/challenge_start.dart`
- `lib/screens/categories/category_details.dart`
- `lib/screens/home/<USER>
- `lib/widgets/game_end_screen.dart`

## Testing

After making these changes, test the app to ensure that:

1. The fonts are displayed correctly
2. There are no more errors related to Google Fonts
3. The app works offline without trying to download fonts
