import 'package:flutter/material.dart';

class AppColors {
  // Main colors
  static const Color primary = Color(0xFFFFA500); // Warm Orange
  static const Color secondary = Color(0xFF2A7F62); // Deep Teal
  static const Color background = Color(0xFFF5F5F5); // Light Gray
  static const Color text = Color(0xFF333333); // Dark Gray
  static const Color accent = Color(0xFFFFC107); // Yellow Accent

  // Status colors
  static const Color error = Color(0xFFE53935); // Red
  static const Color success = Color(0xFF4CAF50); // Green
  static const Color info = Color(0xFF2196F3); // Blue

  // Neutral colors
  static const Color white = Colors.white;
  static const Color black = Colors.black;

  // Grey variants
  static const Color grey100 = Color(0xFFF5F5F5);
  static const Color grey200 = Color(0xFFEEEEEE);
  static const Color grey300 = Color(0xFFE0E0E0);
  static const Color grey400 = Color(0xFFBDBDBD);
  static const Color grey500 = Color(0xFF9E9E9E);
  static const Color grey600 = Color(0xFF757575);
  static const Color grey700 = Color(0xFF616161);
  static const Color grey800 = Color(0xFF424242);
  static const Color grey900 = Color(0xFF212121);

  // Utility method for opacity
  static Color withAlpha(Color color, int alpha) {
    return color.withAlpha(alpha);
  }
}
