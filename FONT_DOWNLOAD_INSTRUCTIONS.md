# Font Download Instructions

To fix the Google Fonts error, you need to download the Poppins font family and include it locally in your app.

## Steps to Download Poppins Font

1. Visit Google Fonts website: https://fonts.google.com/specimen/Poppins
2. Click on "Download family"
3. Extract the downloaded ZIP file
4. Copy all the font files to your project's `assets/fonts` directory

## Required Font Files

At minimum, you need these font files:
- Poppins-Regular.ttf
- Poppins-Medium.ttf
- Poppins-Bold.ttf
- Poppins-SemiBold.ttf

## Alternative Download Method

If you can't access Google Fonts, you can download Poppins from other font repositories:
- Font Squirrel: https://www.fontsquirrel.com/fonts/poppins
- DaFont: https://www.dafont.com/poppins.font

After downloading, place the TTF files in the `assets/fonts` directory.
