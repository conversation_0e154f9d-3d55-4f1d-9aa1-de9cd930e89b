import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:achawach/widgets/game_end_screen.dart';

void main() {
  group('GameEndScreen Dark Mode Tests', () {
    testWidgets('GameEndScreen handles dark mode correctly',
        (WidgetTester tester) async {
      // Set a larger screen size to avoid overflow issues
      tester.view.physicalSize = const Size(1080, 1920);
      tester.view.devicePixelRatio = 1.0;

      // Create test data
      const bool isWinner = true;
      const String message = 'You won the challenge!';
      final Map<String, dynamic> finalState = {
        'score': 500,
        'level': 3,
        'rank': 1,
      };
      bool continueCalled = false;

      // Build the widget with dark theme
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.dark(),
          home: GameEndScreen(
            isWinner: isWinner,
            message: message,
            finalState: finalState,
            winnerMessage: 'Congratulations!',
            loserMessage: 'Better luck next time!',
            winnerId: 123,
            challengeId: 456,
            winnerScore: 500,
            winnerRank: 1,
            winnerLevel: 3,
            onContinue: () {
              continueCalled = true;
            },
          ),
        ),
      );

      // Wait for animations to complete
      // Use a fixed number of pumps instead of pumpAndSettle to avoid timeout
      for (int i = 0; i < 10; i++) {
        await tester.pump(const Duration(milliseconds: 100));
      }

      // Verify that the screen is displayed
      expect(find.text('WINNER!'), findsOneWidget);
      expect(find.text('Congratulations!'), findsOneWidget);
      expect(find.text('You won the challenge!'), findsOneWidget);
      expect(find.text('Final Results'), findsOneWidget);

      // Verify that the continue button is displayed and works
      expect(find.text('Continue'), findsOneWidget);
      await tester.tap(find.text('Continue'));
      expect(continueCalled, isTrue);
    });

    testWidgets('GameEndScreen handles empty or null data gracefully',
        (WidgetTester tester) async {
      // Set a larger screen size to avoid overflow issues
      tester.view.physicalSize = const Size(1080, 1920);
      tester.view.devicePixelRatio = 1.0;

      // Create minimal test data
      const bool isWinner = false;
      const String message = 'Game has ended!';
      final Map<String, dynamic> finalState = {};
      bool continueCalled = false;

      // Build the widget with minimal data
      await tester.pumpWidget(
        MaterialApp(
          home: GameEndScreen(
            isWinner: isWinner,
            message: message,
            finalState: finalState,
            onContinue: () {
              continueCalled = true;
            },
          ),
        ),
      );

      // Wait for animations to complete
      // Use a fixed number of pumps instead of pumpAndSettle to avoid timeout
      for (int i = 0; i < 10; i++) {
        await tester.pump(const Duration(milliseconds: 100));
      }

      // Verify that the screen is displayed with default values
      expect(find.text('GAME OVER'), findsOneWidget);
      expect(find.text('Better luck next time!'), findsOneWidget);
      expect(find.text('Game has ended!'), findsOneWidget);

      // Final Results section should not be shown since finalState is empty
      expect(find.text('Final Results'), findsNothing);

      // Verify that the continue button is displayed and works
      expect(find.text('Continue'), findsOneWidget);
      await tester.tap(find.text('Continue'));
      expect(continueCalled, isTrue);
    });
  });
}
