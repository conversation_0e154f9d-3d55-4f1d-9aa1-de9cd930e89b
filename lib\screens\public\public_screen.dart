import 'package:flutter/material.dart';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/services/api_service.dart';
import 'package:achawach/routers/app_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class PublicScreen extends StatefulWidget {
  const PublicScreen({super.key});

  @override
  State<PublicScreen> createState() => _PublicScreenState();
}

class _PublicScreenState extends State<PublicScreen>
    with SingleTickerProviderStateMixin {
  final ApiService _apiService = ApiService();

  List<Map<String, dynamic>> _topChallenges = [];
  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> _selectedCategoryChallenges = [];

  bool _isLoadingTopChallenges = true;
  bool _isLoadingCategories = true;
  bool _isLoadingCategoryChallenges = false;

  String? _topChallengesError;
  String? _categoriesError;
  String? _categoryChallengesError;

  int? _selectedCategoryId;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadData();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await Future.wait([
      _loadTopChallenges(),
      _loadCategories(),
    ]);
  }

  Future<void> _loadTopChallenges() async {
    try {
      setState(() {
        _isLoadingTopChallenges = true;
        _topChallengesError = null;
      });

      final topChallenges = await _apiService.getPublicTopChallenges();
      if (mounted) {
        setState(() {
          _topChallenges = topChallenges;
          _isLoadingTopChallenges = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _topChallengesError = e.toString();
          _isLoadingTopChallenges = false;
        });
      }
    }
  }

  Future<void> _loadCategories() async {
    try {
      setState(() {
        _isLoadingCategories = true;
        _categoriesError = null;
      });

      final categories = await _apiService.getPublicCategories();
      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoadingCategories = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _categoriesError = e.toString();
          _isLoadingCategories = false;
        });
      }
    }
  }

  Future<void> _loadCategoryChallenges(int categoryId) async {
    try {
      setState(() {
        _isLoadingCategoryChallenges = true;
        _categoryChallengesError = null;
        _selectedCategoryId = categoryId;
      });

      final challenges =
          await _apiService.getPublicCategoryChallenges(categoryId);
      if (mounted) {
        setState(() {
          _selectedCategoryChallenges = challenges;
          _isLoadingCategoryChallenges = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _categoryChallengesError = e.toString();
          _isLoadingCategoryChallenges = false;
        });
      }
    }
  }

  void _onChallengePressed() {
    // Redirect to login screen when user tries to play a challenge
    Navigator.of(context).pushNamed(AppRouter.loginRoute);
  }

  void _onSignInPressed() {
    Navigator.of(context).pushNamed(AppRouter.loginRoute);
  }

  void _onSignUpPressed() {
    Navigator.of(context).pushNamed(AppRouter.signupRoute);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            _buildTopChallengesSection(),
            _buildCategoriesSection(),
            if (_selectedCategoryId != null) _buildCategoryChallengesSection(),
            const SliverToBoxAdapter(child: SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Achawach',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: AppColors.white,
          ),
        ),
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [AppColors.primary, AppColors.secondary],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _onSignInPressed,
          child: Text(
            'Sign In',
            style: GoogleFonts.poppins(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: _onSignUpPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.white,
            foregroundColor: AppColors.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          child: Text(
            'Sign Up',
            style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
          ),
        ),
        const SizedBox(width: 16),
      ],
    );
  }

  Widget _buildTopChallengesSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Top Challenges',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.text,
              ),
            ),
            const SizedBox(height: 16),
            if (_isLoadingTopChallenges)
              const Center(child: CircularProgressIndicator())
            else if (_topChallengesError != null)
              _buildErrorWidget(_topChallengesError!, _loadTopChallenges)
            else
              _buildTopChallengesCarousel(),
          ],
        ),
      ),
    );
  }

  Widget _buildTopChallengesCarousel() {
    if (_topChallenges.isEmpty) {
      return const Center(
        child: Text('No top challenges available'),
      );
    }

    return SizedBox(
      height: 200,
      child: PageView.builder(
        controller: PageController(viewportFraction: 0.85),
        itemCount: _topChallenges.length,
        itemBuilder: (context, index) {
          final challenge = _topChallenges[index];
          return _buildChallengeCard(challenge);
        },
      ),
    );
  }

  Widget _buildChallengeCard(Map<String, dynamic> challenge) {
    final winningRules = challenge['winning_rules'] as Map<String, dynamic>?;
    final startDate = challenge['start_date'] != null
        ? DateTime.parse(challenge['start_date'])
        : null;
    final endDate = challenge['end_date'] != null
        ? DateTime.parse(challenge['end_date'])
        : null;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primary, AppColors.secondary],
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withAlpha(51),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: _onChallengePressed,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        challenge['name'] ?? 'Unknown Challenge',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.white.withAlpha(51),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${challenge['reward'] ?? 0} pts',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                if (winningRules != null) ...[
                  Row(
                    children: [
                      Icon(Icons.emoji_events,
                          color: AppColors.white, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'Level ${winningRules['level']} • ${winningRules['winning_points']} pts',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: AppColors.white.withAlpha(204),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                ],
                if (endDate != null) ...[
                  Row(
                    children: [
                      Icon(Icons.schedule, color: AppColors.white, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'Ends ${DateFormat('MMM dd').format(endDate)}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: AppColors.white.withAlpha(204),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Categories',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.text,
              ),
            ),
            const SizedBox(height: 16),
            if (_isLoadingCategories)
              const Center(child: CircularProgressIndicator())
            else if (_categoriesError != null)
              _buildErrorWidget(_categoriesError!, _loadCategories)
            else
              _buildCategoriesGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesGrid() {
    if (_categories.isEmpty) {
      return const Center(
        child: Text('No categories available'),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: _categories.length,
      itemBuilder: (context, index) {
        final category = _categories[index];
        return _buildCategoryCard(category);
      },
    );
  }

  Widget _buildCategoryCard(Map<String, dynamic> category) {
    final isSelected = _selectedCategoryId == category['id'];

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: isSelected ? AppColors.primary.withAlpha(26) : AppColors.white,
        border: Border.all(
          color: isSelected ? AppColors.primary : AppColors.grey300,
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.grey300.withAlpha(51),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _loadCategoryChallenges(category['id']),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppColors.primary.withAlpha(51)
                        : AppColors.grey100,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getIconFromString(category['icon']),
                    size: 32,
                    color: isSelected ? AppColors.primary : AppColors.grey600,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  category['name'] ?? 'Unknown Category',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isSelected ? AppColors.primary : AppColors.text,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryChallengesSection() {
    final selectedCategory = _categories.firstWhere(
      (cat) => cat['id'] == _selectedCategoryId,
      orElse: () => {'name': 'Category'},
    );

    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '${selectedCategory['name']} Challenges',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.text,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedCategoryId = null;
                      _selectedCategoryChallenges.clear();
                    });
                  },
                  child: Text(
                    'Clear',
                    style: GoogleFonts.poppins(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoadingCategoryChallenges)
              const Center(child: CircularProgressIndicator())
            else if (_categoryChallengesError != null)
              _buildErrorWidget(_categoryChallengesError!,
                  () => _loadCategoryChallenges(_selectedCategoryId!))
            else
              _buildCategoryChallengesList(),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChallengesList() {
    if (_selectedCategoryChallenges.isEmpty) {
      return const Center(
        child: Text('No challenges available in this category'),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _selectedCategoryChallenges.length,
      itemBuilder: (context, index) {
        final challenge = _selectedCategoryChallenges[index];
        return _buildChallengeListItem(challenge);
      },
    );
  }

  Widget _buildChallengeListItem(Map<String, dynamic> challenge) {
    final winningRules = challenge['winning_rules'] as Map<String, dynamic>?;
    final endDate = challenge['end_date'] != null
        ? DateTime.parse(challenge['end_date'])
        : null;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.grey300.withAlpha(51),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: _onChallengePressed,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.quiz,
                    color: AppColors.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        challenge['name'] ?? 'Unknown Challenge',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.text,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          if (winningRules != null) ...[
                            Icon(Icons.emoji_events,
                                size: 14, color: AppColors.grey600),
                            const SizedBox(width: 4),
                            Text(
                              'Level ${winningRules['level']}',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: AppColors.grey600,
                              ),
                            ),
                            const SizedBox(width: 12),
                          ],
                          Icon(Icons.stars, size: 14, color: AppColors.grey600),
                          const SizedBox(width: 4),
                          Text(
                            '${challenge['reward'] ?? 0} pts',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: AppColors.grey600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                if (endDate != null)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Ends',
                        style: GoogleFonts.poppins(
                          fontSize: 10,
                          color: AppColors.grey600,
                        ),
                      ),
                      Text(
                        DateFormat('MMM dd').format(endDate),
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppColors.text,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget(String error, VoidCallback onRetry) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.error.withAlpha(26),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(Icons.error_outline, color: AppColors.error, size: 32),
          const SizedBox(height: 8),
          Text(
            'Something went wrong',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.error,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            error.replaceAll('Exception: ', ''),
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: onRetry,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.white,
            ),
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }

  IconData _getIconFromString(String? iconName) {
    if (iconName == null) return Icons.category;

    switch (iconName) {
      case 'Icons.people_alt_rounded':
        return Icons.people_alt_rounded;
      case 'Icons.today_rounded':
        return Icons.today_rounded;
      case 'Icons.star_rounded':
        return Icons.star_rounded;
      case 'Icons.calendar_month_rounded':
        return Icons.calendar_month_rounded;
      case 'Icons.calendar_view_week':
        return Icons.calendar_view_week;
      case 'Icons.science':
        return Icons.science;
      case 'Icons.history':
        return Icons.history;
      case 'Icons.sports_soccer':
        return Icons.sports_soccer;
      case 'Icons.computer':
        return Icons.computer;
      case 'Icons.palette':
        return Icons.palette;
      case 'Icons.public':
        return Icons.public;
      case 'Icons.music_note':
        return Icons.music_note;
      case 'Icons.movie':
        return Icons.movie;
      case 'Icons.restaurant':
        return Icons.restaurant;
      default:
        return Icons.category;
    }
  }
}
