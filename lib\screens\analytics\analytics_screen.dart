import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:math';

// Your actual imports
import 'package:achawach/widgets/cool_app_bar.dart';
import 'package:achawach/core/constants/app_colors.dart';

/// This screen features:
/// - A prominent "hero" card for total winnings.
/// - A grid layout for key performance indicators (KPIs).
/// - Advanced chart styling with gradients and refined interactivity.
/// - A clean, spacious layout with strong typographical hierarchy.
class AnalyticsScreen extends StatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen> {
  int _touchedPieIndex = -1;
  int? _touchedBarIndex;

  // --- MOCK DATA FOR A PLAYER (This would come from your backend) ---
  final Map<String, double> earningsData = {
    'Daily': 25.50,
    'Weekly': 40.00,
    'Seasonal': 75.00
  };
  final Map<String, double> categoryPerformance = {
    'Science': 85,
    'History': 92,
    'Movies': 75,
    'Sports': 60,
    'Music': 88
  };
  final List<double> weeklyCorrectAnswers = [15, 25, 20, 30, 22, 28, 35];
  late final double totalWinnings = earningsData.values.reduce((a, b) => a + b);
  // --------------------------------------------------------------------

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CoolAppBar(title: 'My Performance'),
      backgroundColor: AppColors.background,
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section 1: The Hero Card - Total Winnings
            _buildWinningsCard(),
            const SizedBox(height: 24),

            // Section 2: Quick Stats Grid
            _buildQuickStatsGrid(),
            const SizedBox(height: 24),

            // Section 3: Performance by Category
            _buildChartCard(
              title: 'Category Mastery',
              icon: Icons.radar_rounded,
              iconColor: AppColors.secondary,
              chart: _buildCategoryPerformanceChart(),
            ),
            const SizedBox(height: 24),

            // Section 4: Weekly Activity
            _buildChartCard(
              title: 'Weekly Activity',
              icon: Icons.bar_chart_rounded,
              iconColor: AppColors.primary,
              chart: _buildActivityChart(),
            ),
            const SizedBox(height: 24),

            // Section 5: Achievements as a simple list
            _buildAchievementsList(),
          ],
        ),
      ),
    );
  }

  /// The main "hero" card combining the donut chart and total winnings.
  Widget _buildWinningsCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [AppColors.secondary, Color(0xFF1E5C4B)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
              color: AppColors.secondary.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 5))
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Total Winnings',
            style: TextStyle(
                color: AppColors.white,
                fontSize: 18,
                fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          Text(
            '${totalWinnings.toStringAsFixed(2)} ETB ',
            style: const TextStyle(
                color: AppColors.white,
                fontSize: 40,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 150,
            child: PieChart(
              PieChartData(
                pieTouchData: PieTouchData(
                    touchCallback: (event, response) => setState(() {
                          _touchedPieIndex =
                              response?.touchedSection?.touchedSectionIndex ??
                                  -1;
                        })),
                sectionsSpace: 4,
                centerSpaceRadius: 0, // Filled donut chart
                sections: _getPieChartSections(),
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildLegend(),
        ],
      ),
    );
  }

  List<PieChartSectionData> _getPieChartSections() {
    final sections = earningsData.entries.toList();
    final colors = [AppColors.accent, AppColors.primary, AppColors.info];
    return List.generate(sections.length, (i) {
      final isTouched = i == _touchedPieIndex;
      final radius = isTouched ? 60.0 : 50.0;
      return PieChartSectionData(
        color: colors[i],
        value: sections[i].value,
        title:
            '${(sections[i].value / totalWinnings * 100).toStringAsFixed(0)}%',
        radius: radius,
        titleStyle: TextStyle(
            fontSize: isTouched ? 18 : 14,
            fontWeight: FontWeight.bold,
            color: AppColors.white,
            shadows: const [Shadow(color: Colors.black38, blurRadius: 3)]),
      );
    });
  }

  Widget _buildLegend() {
    final keys = earningsData.keys.toList();
    final colors = [AppColors.accent, AppColors.primary, AppColors.info];
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
          keys.length,
          (i) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                child: Row(children: [
                  Container(width: 12, height: 12, color: colors[i]),
                  const SizedBox(width: 8),
                  Text(keys[i],
                      style: const TextStyle(
                          color: AppColors.grey200, fontSize: 14)),
                ]),
              )),
    );
  }

  /// A 2x2 grid for key performance indicators.
  Widget _buildQuickStatsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2, // Adjust for desired height
      children: [
        _StatCard(
            icon: Icons.military_tech,
            value: '#1,245',
            label: 'Global Rank',
            color: AppColors.secondary),
        _StatCard(
            icon: Icons.check_circle,
            value: '82%',
            label: 'Win Rate',
            color: AppColors.success),
        _StatCard(
            icon: Icons.leaderboard,
            value: 'Top 5%',
            label: 'Category Rank',
            color: AppColors.primary),
        _StatCard(
            icon: Icons.card_giftcard,
            value: '3',
            label: 'Prizes Won',
            color: AppColors.accent),
      ],
    );
  }

  /// Reusable card for the main charts.
  Widget _buildChartCard(
      {required String title,
      required IconData icon,
      required Color iconColor,
      required Widget chart}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
          color: AppColors.white, borderRadius: BorderRadius.circular(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(children: [
            Icon(icon, color: iconColor, size: 24),
            const SizedBox(width: 12),
            Text(title,
                style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.text)),
          ]),
          const SizedBox(height: 20),
          SizedBox(height: 200, child: chart),
        ],
      ),
    );
  }

  /// Radar Chart for category performance.
  Widget _buildCategoryPerformanceChart() {
    return RadarChart(
      RadarChartData(
        dataSets: [
          RadarDataSet(
            fillColor: AppColors.secondary.withOpacity(0.25),
            borderColor: AppColors.secondary,
            borderWidth: 2,
            entryRadius: 4,
            dataEntries: categoryPerformance.values
                .map((v) => RadarEntry(value: v))
                .toList(),
          ),
        ],
        radarBackgroundColor: Colors.transparent,
        borderData: FlBorderData(show: false),
        radarBorderData: const BorderSide(color: AppColors.grey300),
        tickCount: 5,
        ticksTextStyle:
            const TextStyle(color: Colors.transparent, fontSize: 10),
        tickBorderData: const BorderSide(color: AppColors.grey300, width: 1.5),
        gridBorderData: const BorderSide(color: AppColors.grey300, width: 1),
        getTitle: (index, angle) => RadarChartTitle(
          text: categoryPerformance.keys
              .elementAt(index)
              .substring(0, 3), // Abbreviate for cleaner look
          angle: angle,
          // textStyle: const TextStyle(
          //     color: AppColors.grey700,
          //     fontWeight: FontWeight.w600,
          //     fontSize: 13),
        ),
      ),
    );
  }

  /// Bar Chart for weekly activity with enhanced touch feedback.
  Widget _buildActivityChart() {
    final barColor = AppColors.primary;
    final touchedColor = AppColors.accent;
    return BarChart(
      BarChartData(
        maxY: weeklyCorrectAnswers.reduce(max) * 1.2,
        barTouchData: BarTouchData(
          touchCallback: (event, response) {
            if (response == null || response.spot == null) {
              setState(() => _touchedBarIndex = null);
              return;
            }
            if (event is FlTapUpEvent) {
              setState(
                  () => _touchedBarIndex = response.spot!.touchedBarGroupIndex);
            }
          },
          touchTooltipData: BarTouchTooltipData(
              // tooltipBgColor: AppColors.grey800,
              getTooltipItem: (group, groupIndex, rod, rodIndex) =>
                  BarTooltipItem('${rod.toY.toInt()} Correct',
                      const TextStyle(color: Colors.white))),
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                  showTitles: true,
                  getTitlesWidget: (value, meta) => SideTitleWidget(
                      axisSide: meta.axisSide,
                      space: 8,
                      child: Text(
                          ['M', 'T', 'W', 'T', 'F', 'S', 'S'][value.toInt()],
                          style: const TextStyle(
                              color: AppColors.grey600, fontSize: 12))),
                  reservedSize: 30)),
          leftTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        gridData: const FlGridData(show: false),
        barGroups: List.generate(weeklyCorrectAnswers.length, (i) {
          final isTouched = i == _touchedBarIndex;
          return BarChartGroupData(x: i, barRods: [
            BarChartRodData(
                toY: weeklyCorrectAnswers[i],
                width: isTouched ? 20 : 16,
                gradient: LinearGradient(colors: [
                  isTouched ? touchedColor : barColor,
                  isTouched ? barColor : barColor.withOpacity(0.5)
                ], begin: Alignment.topCenter, end: Alignment.bottomCenter),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(4)))
          ]);
        }),
      ),
    );
  }

  /// A simple, clean list of achievements.
  Widget _buildAchievementsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(left: 4.0, bottom: 12.0),
          child: Text(
            'Recent Achievements',
            style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.text),
          ),
        ),
        _AchievementTile(
            icon: Icons.local_fire_department,
            color: AppColors.primary,
            title: 'Hot Streak',
            subtitle: 'Answered 20 questions correctly in a row.'),
        _AchievementTile(
            icon: Icons.school,
            color: AppColors.secondary,
            title: 'Historian',
            subtitle: 'Won a weekly challenge in the History category.'),
        _AchievementTile(
            icon: Icons.paid,
            color: AppColors.accent,
            title: 'Big Spender',
            subtitle: 'Earned your first 100 ETB in total winnings.'),
      ],
    );
  }
}

/// A compact card for the Quick Stats grid.
class _StatCard extends StatelessWidget {
  const _StatCard(
      {required this.icon,
      required this.value,
      required this.label,
      required this.color});
  final IconData icon;
  final String value;
  final String label;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
          color: AppColors.white, borderRadius: BorderRadius.circular(16)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            backgroundColor: color.withOpacity(0.15),
            radius: 20,
            child: Icon(icon, color: color, size: 22),
          ),
          const Spacer(),
          Text(value,
              style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.text)),
          const SizedBox(height: 4),
          Text(label,
              style: const TextStyle(fontSize: 14, color: AppColors.grey600)),
        ],
      ),
    );
  }
}

/// A custom list tile for displaying an achievement.
class _AchievementTile extends StatelessWidget {
  const _AchievementTile(
      {required this.icon,
      required this.color,
      required this.title,
      required this.subtitle});
  final IconData icon;
  final Color color;
  final String title;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
          color: AppColors.white, borderRadius: BorderRadius.circular(12)),
      child: Row(
        children: [
          CircleAvatar(
              backgroundColor: color.withOpacity(0.15),
              child: Icon(icon, color: color)),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title,
                    style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.text)),
                const SizedBox(height: 2),
                Text(subtitle,
                    style: const TextStyle(
                        color: AppColors.grey600, fontSize: 13)),
              ],
            ),
          )
        ],
      ),
    );
  }
}
