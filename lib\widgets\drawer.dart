// import 'package:achawach/core/constants/app_colors.dart';
// import 'package:achawach/core/constants/constants.dart';
// import 'package:achawach/core/utils/page_transitions.dart';
// import 'package:achawach/services/api_service.dart';
// import 'package:achawach/screens/auth/login_screen.dart';
// import 'package:achawach/models/profile.dart'; // Make sure to import the Profile model
// import 'package:flutter/material.dart';

// class AppDrawer extends StatefulWidget {
//   const AppDrawer({super.key});

//   @override
//   State<AppDrawer> createState() => _AppDrawerState();
// }

// class _AppDrawerState extends State<AppDrawer> {
//   final ApiService _apiService = ApiService();
//   Profile? _profile;
//   bool _isLoading = true;

//   @override
//   void initState() {
//     super.initState();
//     _loadProfile();
//   }

//   Future<void> _loadProfile() async {
//     try {
//       final profile = await _apiService.getProfile();
//       if (mounted) {
//         setState(() {
//           _profile = profile;

//           _isLoading = false;
//         });
//       }
//     } catch (e) {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//       // Handle unauthorized access
//       if (e.toString().contains('Unauthorized')) {
//         _logout(context);
//       }
//     }
//   }

//   Widget _buildProfileImage() {
//     if (_isLoading) {
//       return const CircularProgressIndicator(color: Colors.white);
//     }

//     if (_profile == null) {
//       return const CircleAvatar(
//         radius: 30,
//         backgroundColor: Colors.white,
//         child: Icon(
//           Icons.person,
//           size: 40,
//           color: AppColors.primary,
//         ),
//       );
//     }

//     if (_profile!.isProfileImageAvailable == 1 &&
//         _profile!.profileImagePath != null) {
//       return CircleAvatar(
//         radius: 30,
//         backgroundColor: Colors.white,
//         backgroundImage: NetworkImage(
//           '${AppConstants.apiBaseIP}/${_profile!.profileImagePath}'
//               .replaceAll('\\', '/'),
//         ),
//       );
//     } else if (_profile!.avatar != null) {
//       return CircleAvatar(
//         radius: 30,
//         backgroundColor: Colors.white,
//         child: Text(
//           _profile!.avatar!,
//           style: const TextStyle(fontSize: 40),
//         ),
//       );
//     }

//     return const CircleAvatar(
//       radius: 30,
//       backgroundColor: Colors.white,
//       child: Icon(
//         Icons.person,
//         size: 40,
//         color: AppColors.primary,
//       ),
//     );
//   }

//   Future<void> _logout(BuildContext context) async {
//     await _apiService.clearToken();
//     if (context.mounted) {
//       Navigator.of(context).pushAndRemoveUntil(
//         PageTransitions.fadeSlideDown(
//           page: const LoginScreen(),
//           duration: const Duration(milliseconds: 500),
//         ),
//         (route) => false,
//       );
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Drawer(
//       backgroundColor: AppColors.background,
//       child: ListView(
//         padding: EdgeInsets.zero,
//         children: <Widget>[
//           DrawerHeader(
//             decoration: const BoxDecoration(
//               color: AppColors.primary,
//             ),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               mainAxisAlignment: MainAxisAlignment.end,
//               children: [
//                 _buildProfileImage(),
//                 const SizedBox(height: 10),
//                 Text(
//                   _isLoading
//                       ? 'Loading...'
//                       : _profile?.fullName ?? 'Achawach Menu',
//                   style: const TextStyle(
//                     color: Colors.white,
//                     fontSize: 24,
//                   ),
//                 ),
//                 if (_profile?.phoneNumber != null)
//                   Text(
//                     _profile!.phoneNumber,
//                     style: const TextStyle(
//                       color: Colors.white70,
//                       fontSize: 14,
//                     ),
//                   ),
//               ],
//             ),
//           ),
//           _buildDrawerItem(
//             icon: Icons.home,
//             text: 'Home',
//             onTap: () {
//               Navigator.pushNamed(context, '/home');
//             },
//           ),
//           _buildDrawerItem(
//             icon: Icons.person,
//             text: 'Profile',
//             onTap: () {
//               Navigator.pushNamed(context, '/profile');
//             },
//           ),
//           _buildDrawerItem(
//             icon: Icons.settings,
//             text: 'Settings',
//             onTap: () {
//               Navigator.pop(context);
//               Navigator.pushNamed(context, '/settings');
//             },
//           ),
//           const Divider(),
//           _buildDrawerItem(
//             icon: Icons.logout,
//             text: 'Logout',
//             onTap: () => _logout(context),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildDrawerItem({
//     required IconData icon,
//     required String text,
//     required VoidCallback onTap,
//   }) {
//     return ListTile(
//       leading: Icon(icon, color: AppColors.text),
//       title: Text(text, style: const TextStyle(color: AppColors.text)),
//       onTap: onTap,
//     );
//   }
// }

// import 'package:achawach/core/constants/app_colors.dart';
// import 'package:achawach/core/constants/constants.dart';
// import 'package:achawach/core/utils/page_transitions.dart';
// import 'package:achawach/services/api_service.dart';
// import 'package:achawach/screens/auth/login_screen.dart';
// import 'package:achawach/models/profile.dart';
// import 'package:flutter/material.dart';
// import 'package:intl/intl.dart';
// import 'package:shimmer/shimmer.dart';

// class AppDrawer extends StatefulWidget {
//   const AppDrawer({super.key});

//   @override
//   State<AppDrawer> createState() => _AppDrawerState();
// }

// class _AppDrawerState extends State<AppDrawer> {
//   final ApiService _apiService = ApiService();
//   Profile? _profile;
//   bool _isLoading = true;

//   // --- MODIFIED FOR ETHIOPIAN BIRR ---
//   final NumberFormat _currencyFormatter = NumberFormat.currency(
//     // For locale, 'am_ET' is Amharic - Ethiopia.
//     // If you want just the currency symbol without specific Amharic number formatting,
//     // you can sometimes get away with a more generic locale like 'en_US'
//     // and then specify the symbol or name.
//     // However, 'am_ET' should give the most accurate local representation.
//     locale: 'am_ET', // Amharic - Ethiopia
//     symbol: 'Birr ',    // The symbol for Ethiopian Birr (you can also use 'ETB')
//     // OR using the currency code directly (often preferred for consistency):
//     // name: 'ETB',  // ISO 4217 currency code for Ethiopian Birr
//     // symbol: 'ETB ' // If 'name' is used, 'symbol' can be used to prefix/suffix.
//                      // Using 'ETB ' (with a space) will put "ETB " before the number.
//                      // If you want "ብር" use symbol: 'ብር'.
//     decimalDigits: 2, // Standard two decimal places for currency
//   );
//   // --- END OF MODIFICATION ---

//   @override
//   void initState() {
//     super.initState();
//     _loadProfile();
//   }

//   Future<void> _loadProfile() async {
//     // await Future.delayed(const Duration(seconds: 2)); // For testing shimmer
//     try {
//       final profile = await _apiService.getProfile();
//       if (mounted) {
//         setState(() {
//           _profile = profile;
//           _isLoading = false;
//         });
//       }
//     } catch (e) {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//       debugPrint("Error loading profile: $e");
//       if (e.toString().contains('Unauthorized')) {
//         _logout(context);
//       }
//     }
//   }

//   Widget _buildProfileImageWidget(Profile? profileData) {
//     if (profileData != null) {
//       if (profileData.isProfileImageAvailable == 1 &&
//           profileData.profileImagePath != null) {
//         return CircleAvatar(
//           radius: 35,
//           backgroundColor: Colors.white.withOpacity(0.3),
//           backgroundImage: NetworkImage(
//             '${AppConstants.apiBaseIP}/${profileData.profileImagePath}'
//                 .replaceAll('\\', '/'),
//           ),
//         );
//       } else if (profileData.avatar != null) {
//         return CircleAvatar(
//           radius: 35,
//           backgroundColor: Colors.white.withOpacity(0.8),
//           child: Text(
//             profileData.avatar!,
//             style: TextStyle(fontSize: 38, color: AppColors.primary, fontWeight: FontWeight.bold),
//           ),
//         );
//       }
//     }
//     return CircleAvatar(
//       radius: 35,
//       backgroundColor: Colors.white.withOpacity(0.8),
//       child: Icon(
//         Icons.person_outline,
//         size: 45,
//         color: AppColors.primary.withOpacity(0.7),
//       ),
//     );
//   }

//   Widget _buildHeaderContent() {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       mainAxisAlignment: MainAxisAlignment.end,
//       children: [
//         _buildProfileImageWidget(_profile),
//         const SizedBox(height: 12),
//         Text(
//           _profile?.fullName ?? 'User Name',
//           style: const TextStyle(
//             color: Colors.white,
//             fontSize: 20,
//             fontWeight: FontWeight.bold,
//             letterSpacing: 0.5,
//           ),
//           maxLines: 1,
//           overflow: TextOverflow.ellipsis,
//         ),
//         if (_profile?.phoneNumber != null)
//           Padding(
//             padding: const EdgeInsets.only(top: 2.0),
//             child: Text(
//               _profile!.phoneNumber,
//               style: TextStyle(
//                 color: Colors.white.withOpacity(0.8),
//                 fontSize: 13,
//               ),
//               maxLines: 1,
//               overflow: TextOverflow.ellipsis,
//             ),
//           ),
//         if (_profile?.totalEarned != null)
//           Padding(
//             padding: const EdgeInsets.only(top: 5.0),
//             child: Text(
//               // This line now uses the updated _currencyFormatter
//               'Earned: ${_currencyFormatter.format(_profile!.totalEarned!)}',
//               style: TextStyle(
//                 color: Colors.white,
//                 fontSize: 15,
//                 fontWeight: FontWeight.w500,
//               ),
//               maxLines: 1,
//               overflow: TextOverflow.ellipsis,
//             ),
//           ),
//       ],
//     );
//   }

//   Widget _buildShimmerHeaderContent() {
//     return Shimmer.fromColors(
//       baseColor: Colors.white.withOpacity(0.3),
//       highlightColor: Colors.white.withOpacity(0.6),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         mainAxisAlignment: MainAxisAlignment.end,
//         children: [
//           const CircleAvatar(
//             radius: 35,
//             backgroundColor: Colors.white,
//           ),
//           const SizedBox(height: 12),
//           Container(
//             height: 20,
//             width: 150,
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(4)
//             ),
//           ),
//           const SizedBox(height: 6),
//           Container(
//             height: 14,
//             width: 100,
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(4)
//             ),
//           ),
//           const SizedBox(height: 6),
//           Container(
//             height: 16,
//             width: 120,
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(4)
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Future<void> _logout(BuildContext context) async {
//     await _apiService.clearToken();
//     if (context.mounted) {
//       Navigator.of(context).pushAndRemoveUntil(
//         PageTransitions.fadeSlideDown(
//           page: const LoginScreen(),
//           duration: const Duration(milliseconds: 500),
//         ),
//         (route) => false,
//       );
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Drawer(
//       backgroundColor: AppColors.background,
//       elevation: 10,
//       child: Column(
//         children: <Widget>[
//           Container(
//             height: 220,
//             width: double.infinity,
//             padding: const EdgeInsets.fromLTRB(20, 0, 20, 16),
//             decoration: BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
//                 begin: Alignment.topLeft,
//                 end: Alignment.bottomRight,
//               ),
//             ),
//             child: SafeArea(
//               bottom: false,
//               child: AnimatedSwitcher(
//                 duration: const Duration(milliseconds: 500),
//                 switchInCurve: Curves.easeIn,
//                 switchOutCurve: Curves.easeOut,
//                 child: _isLoading
//                     ? _buildShimmerHeaderContent()
//                     : _buildHeaderContent(),
//               ),
//             ),
//           ),
//           Expanded(
//             child: ListView(
//               padding: EdgeInsets.zero,
//               children: <Widget>[
//                 _buildDrawerItem(
//                   icon: Icons.home_outlined,
//                   text: 'Home',
//                   onTap: () {
//                     Navigator.pop(context);
//                     Navigator.pushNamed(context, '/home');
//                   },
//                 ),
//                 _buildDrawerItem(
//                   icon: Icons.person_outline,
//                   text: 'Profile',
//                   onTap: () {
//                     Navigator.pop(context);
//                     Navigator.pushNamed(context, '/profile');
//                   },
//                 ),
//                 _buildDrawerItem(
//                   icon: Icons.settings_outlined,
//                   text: 'Settings',
//                   onTap: () {
//                     Navigator.pop(context);
//                     Navigator.pushNamed(context, '/settings');
//                   },
//                 ),
//                 Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 16.0),
//                   child: Divider(color: AppColors.grey400, height: 1),
//                 ),
//                 _buildDrawerItem(
//                   icon: Icons.logout_outlined,
//                   text: 'Logout',
//                   iconColor: Colors.redAccent,
//                   textColor: Colors.redAccent,
//                   onTap: () => _logout(context),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildDrawerItem({
//     required IconData icon,
//     required String text,
//     required VoidCallback onTap,
//     Color? iconColor,
//     Color? textColor,
//   }) {
//     return Material(
//       color: Colors.transparent,
//       child: InkWell(
//         onTap: onTap,
//         splashColor: AppColors.primary.withOpacity(0.1),
//         highlightColor: AppColors.primary.withOpacity(0.05),
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
//           child: Row(
//             children: <Widget>[
//               Icon(icon, color: iconColor ?? AppColors.text.withOpacity(0.7), size: 24),
//               const SizedBox(width: 20),
//               Text(
//                 text,
//                 style: TextStyle(
//                   color: textColor ?? AppColors.text,
//                   fontSize: 16,
//                   fontWeight: FontWeight.w500
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

// version 3
// import 'package:achawach/core/constants/app_colors.dart';
// import 'package:achawach/core/constants/constants.dart';
// import 'package:achawach/core/utils/page_transitions.dart';
// import 'package:achawach/services/api_service.dart';
// import 'package:achawach/screens/auth/login_screen.dart';
// import 'package:achawach/models/profile.dart';
// import 'package:flutter/material.dart';
// import 'package:intl/intl.dart';
// import 'package:shimmer/shimmer.dart';

// class AppDrawer extends StatefulWidget {
//   const AppDrawer({super.key});

//   @override
//   State<AppDrawer> createState() => _AppDrawerState();
// }

// class _AppDrawerState extends State<AppDrawer> with TickerProviderStateMixin {
//   final ApiService _apiService = ApiService();
//   Profile? _profile;
//   bool _isLoading = true;
//   late AnimationController _slideController;
//   late AnimationController _fadeController;
//   late AnimationController _bounceController;
//   late Animation<double> _slideAnimation;
//   late Animation<double> _fadeAnimation;
//   late Animation<double> _bounceAnimation;

//   final NumberFormat _currencyFormatter = NumberFormat.currency(
//     locale: 'am_ET',
//     symbol: 'Birr ',
//     decimalDigits: 2,
//   );

//   @override
//   void initState() {
//     super.initState();
//     _initializeAnimations();
//     _loadProfile();
//   }

//   void _initializeAnimations() {
//     _slideController = AnimationController(
//       duration: const Duration(milliseconds: 800),
//       vsync: this,
//     );
//     _fadeController = AnimationController(
//       duration: const Duration(milliseconds: 600),
//       vsync: this,
//     );
//     _bounceController = AnimationController(
//       duration: const Duration(milliseconds: 1200),
//       vsync: this,
//     );

//     _slideAnimation = CurvedAnimation(
//       parent: _slideController,
//       curve: Curves.elasticOut,
//     );
//     _fadeAnimation = CurvedAnimation(
//       parent: _fadeController,
//       curve: Curves.easeInOut,
//     );
//     _bounceAnimation = CurvedAnimation(
//       parent: _bounceController,
//       curve: Curves.bounceOut,
//     );

//     // Start animations with delays
//     Future.delayed(const Duration(milliseconds: 100), () {
//       _fadeController.forward();
//     });
//     Future.delayed(const Duration(milliseconds: 300), () {
//       _slideController.forward();
//     });
//     Future.delayed(const Duration(milliseconds: 500), () {
//       _bounceController.forward();
//     });
//   }

//   @override
//   void dispose() {
//     _slideController.dispose();
//     _fadeController.dispose();
//     _bounceController.dispose();
//     super.dispose();
//   }

//   Future<void> _loadProfile() async {
//     try {
//       final profile = await _apiService.getProfile();
//       if (mounted) {
//         setState(() {
//           _profile = profile;
//           _isLoading = false;
//         });
//       }
//     } catch (e) {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//       debugPrint("Error loading profile: $e");
//       if (e.toString().contains('Unauthorized')) {
//         _logout(context);
//       }
//     }
//   }

//   Widget _buildPatternBackground() {
//     return Container(
//       decoration: BoxDecoration(
//         gradient: LinearGradient(
//           colors: [
//             AppColors.primary,
//             AppColors.primary.withOpacity(0.8),
//             AppColors.secondary.withOpacity(0.6),
//           ],
//           begin: Alignment.topLeft,
//           end: Alignment.bottomRight,
//         ),
//       ),
//       child: CustomPaint(
//         painter: PatternPainter(),
//         size: Size.infinite,
//       ),
//     );
//   }

//   Widget _buildHeaderContent() {
//     return SlideTransition(
//       position: Tween<Offset>(
//         begin: const Offset(-1, 0),
//         end: Offset.zero,
//       ).animate(_slideAnimation),
//       child: FadeTransition(
//         opacity: _fadeAnimation,
//         child: LayoutBuilder(
//           builder: (context, constraints) {
//             // Calculate available height and adjust accordingly
//             final availableHeight = constraints.maxHeight;
//             final avatarSize = availableHeight < 150 ? 30.0 : 40.0;
//             final cardPadding = availableHeight < 150 ? 8.0 : 12.0;
//             final spacing = availableHeight < 150 ? 8.0 : 12.0;
//             final nameFontSize = availableHeight < 150 ? 14.0 : 16.0;
//             final phoneFontSize = availableHeight < 150 ? 11.0 : 12.0;

//             return SingleChildScrollView(
//               child: ConstrainedBox(
//                 constraints: BoxConstraints(
//                   minHeight: availableHeight,
//                 ),
//                 child: IntrinsicHeight(
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     mainAxisAlignment: MainAxisAlignment.end,
//                     children: [
//                       // Profile image with responsive size
//                       ScaleTransition(
//                         scale: _bounceAnimation,
//                         child: _buildResponsiveProfileImage(avatarSize),
//                       ),
//                       SizedBox(height: spacing),
//                       // Single responsive card containing all user info
//                       Container(
//                         width: double.infinity,
//                         constraints: BoxConstraints(
//                           minHeight: availableHeight < 150 ? 40 : 60,
//                         ),
//                         padding: EdgeInsets.all(cardPadding),
//                         decoration: BoxDecoration(
//                           color: Colors.white.withOpacity(0.25),
//                           borderRadius: BorderRadius.circular(12),
//                           border:
//                               Border.all(color: Colors.white.withOpacity(0.3)),
//                           boxShadow: [
//                             BoxShadow(
//                               color: Colors.black.withOpacity(0.1),
//                               blurRadius: 8,
//                               offset: const Offset(0, 2),
//                             ),
//                           ],
//                         ),
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           mainAxisSize: MainAxisSize.min,
//                           children: [
//                             // User name
//                             Text(
//                               _profile?.fullName ?? 'User Name',
//                               style: TextStyle(
//                                 color: Colors.white,
//                                 fontSize: nameFontSize,
//                                 fontWeight: FontWeight.bold,
//                                 letterSpacing: 0.3,
//                               ),
//                               maxLines: 1,
//                               overflow: TextOverflow.ellipsis,
//                             ),
//                             SizedBox(height: cardPadding / 2),
//                             // Phone number and earnings in a responsive row
//                             _buildResponsiveInfoRow(
//                                 phoneFontSize, constraints.maxWidth),
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             );
//           },
//         ),
//       ),
//     );
//   }

//   Widget _buildResponsiveProfileImage(double radius) {
//     Widget avatarWidget;

//     if (_profile != null) {
//       if (_profile!.isProfileImageAvailable == 1 &&
//           _profile!.profileImagePath != null) {
//         avatarWidget = Container(
//           decoration: BoxDecoration(
//             shape: BoxShape.circle,
//             border: Border.all(color: Colors.white, width: 2),
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withOpacity(0.2),
//                 blurRadius: 8,
//                 offset: const Offset(0, 4),
//               ),
//             ],
//           ),
//           child: CircleAvatar(
//             radius: radius,
//             backgroundImage: NetworkImage(
//               '${AppConstants.apiBaseIP}/${_profile!.profileImagePath}'
//                   .replaceAll('\\', '/'),
//             ),
//           ),
//         );
//       } else if (_profile!.avatar != null) {
//         avatarWidget = Container(
//           decoration: BoxDecoration(
//             shape: BoxShape.circle,
//             border: Border.all(color: Colors.white, width: 2),
//             gradient: LinearGradient(
//               colors: [Colors.white, Colors.white.withOpacity(0.9)],
//               begin: Alignment.topLeft,
//               end: Alignment.bottomRight,
//             ),
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withOpacity(0.2),
//                 blurRadius: 8,
//                 offset: const Offset(0, 4),
//               ),
//             ],
//           ),
//           child: CircleAvatar(
//             radius: radius,
//             backgroundColor: Colors.transparent,
//             child: Text(
//               _profile!.avatar!,
//               style: TextStyle(
//                 fontSize: radius * 0.8,
//                 color: AppColors.primary,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//           ),
//         );
//       } else {
//         avatarWidget = _buildDefaultResponsiveAvatar(radius);
//       }
//     } else {
//       avatarWidget = _buildDefaultResponsiveAvatar(radius);
//     }

//     return avatarWidget;
//   }

//   Widget _buildDefaultResponsiveAvatar(double radius) {
//     return Container(
//       decoration: BoxDecoration(
//         shape: BoxShape.circle,
//         border: Border.all(color: Colors.white, width: 2),
//         gradient: LinearGradient(
//           colors: [Colors.white, Colors.white.withOpacity(0.9)],
//           begin: Alignment.topLeft,
//           end: Alignment.bottomRight,
//         ),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.2),
//             blurRadius: 8,
//             offset: const Offset(0, 4),
//           ),
//         ],
//       ),
//       child: CircleAvatar(
//         radius: radius,
//         backgroundColor: Colors.transparent,
//         child: Icon(
//           Icons.person_outline,
//           size: radius * 1.2,
//           color: AppColors.primary.withOpacity(0.8),
//         ),
//       ),
//     );
//   }

//   Widget _buildResponsiveInfoRow(double fontSize, double maxWidth) {
//     // Determine layout based on available width
//     final bool useVerticalLayout = maxWidth < 200;

//     if (useVerticalLayout) {
//       return Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Phone number
//           if (_profile?.phoneNumber != null) ...[
//             Row(
//               children: [
//                 Icon(
//                   Icons.phone,
//                   color: Colors.white.withOpacity(0.8),
//                   size: fontSize + 2,
//                 ),
//                 const SizedBox(width: 4),
//                 Expanded(
//                   child: Text(
//                     _profile!.phoneNumber,
//                     style: TextStyle(
//                       color: Colors.white.withOpacity(0.9),
//                       fontSize: fontSize,
//                       fontWeight: FontWeight.w500,
//                     ),
//                     maxLines: 1,
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                 ),
//               ],
//             ),
//             const SizedBox(height: 4),
//           ],
//           // Earnings
//           if (_profile?.totalEarned != null) ...[
//             Container(
//               padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
//               decoration: BoxDecoration(
//                 color: AppColors.accent.withOpacity(0.8),
//                 borderRadius: BorderRadius.circular(8),
//                 border: Border.all(color: Colors.white.withOpacity(0.3)),
//               ),
//               child: Row(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Container(
//                     padding: const EdgeInsets.all(1),
//                     decoration: const BoxDecoration(
//                       color: Colors.white,
//                       shape: BoxShape.circle,
//                     ),
//                     child: Icon(
//                       Icons.monetization_on,
//                       color: AppColors.accent,
//                       size: fontSize - 1,
//                     ),
//                   ),
//                   const SizedBox(width: 4),
//                   Text(
//                     'Earned: ${_currencyFormatter.format(_profile!.totalEarned!)}',
//                     style: TextStyle(
//                       color: Colors.white,
//                       fontSize: fontSize - 1,
//                       fontWeight: FontWeight.bold,
//                     ),
//                     maxLines: 1,
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ],
//       );
//     } else {
//       return Row(
//         children: [
//           // Phone number section
//           if (_profile?.phoneNumber != null) ...[
//             Expanded(
//               flex: 3,
//               child: Row(
//                 children: [
//                   Icon(
//                     Icons.phone,
//                     color: Colors.white.withOpacity(0.8),
//                     size: fontSize + 2,
//                   ),
//                   const SizedBox(width: 4),
//                   Expanded(
//                     child: Text(
//                       _profile!.phoneNumber,
//                       style: TextStyle(
//                         color: Colors.white.withOpacity(0.9),
//                         fontSize: fontSize,
//                         fontWeight: FontWeight.w500,
//                       ),
//                       maxLines: 1,
//                       overflow: TextOverflow.ellipsis,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//             const SizedBox(width: 8),
//           ],
//           // Earnings section
//           if (_profile?.totalEarned != null) ...[
//             Expanded(
//               flex: 2,
//               child: Container(
//                 padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
//                 decoration: BoxDecoration(
//                   color: AppColors.accent.withOpacity(0.8),
//                   borderRadius: BorderRadius.circular(10),
//                   border: Border.all(color: Colors.white.withOpacity(0.3)),
//                 ),
//                 child: Row(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Container(
//                       padding: const EdgeInsets.all(1),
//                       decoration: const BoxDecoration(
//                         color: Colors.white,
//                         shape: BoxShape.circle,
//                       ),
//                       child: Icon(
//                         Icons.monetization_on,
//                         color: AppColors.accent,
//                         size: fontSize,
//                       ),
//                     ),
//                     const SizedBox(width: 4),
//                     Expanded(
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         mainAxisSize: MainAxisSize.min,
//                         children: [
//                           Text(
//                             'Earned',
//                             style: TextStyle(
//                               color: Colors.white.withOpacity(0.9),
//                               fontSize: fontSize - 2,
//                               fontWeight: FontWeight.w500,
//                             ),
//                           ),
//                           Text(
//                             _currencyFormatter.format(_profile!.totalEarned!),
//                             style: TextStyle(
//                               color: Colors.white,
//                               fontSize: fontSize - 1,
//                               fontWeight: FontWeight.bold,
//                             ),
//                             maxLines: 1,
//                             overflow: TextOverflow.ellipsis,
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ],
//       );
//     }
//   }

//   Widget _buildShimmerHeaderContent() {
//     return LayoutBuilder(
//       builder: (context, constraints) {
//         final availableHeight = constraints.maxHeight;
//         final avatarSize = availableHeight < 150 ? 60.0 : 80.0;
//         final cardHeight = availableHeight < 150 ? 50.0 : 70.0;
//         final spacing = availableHeight < 150 ? 8.0 : 16.0;

//         return Shimmer.fromColors(
//           baseColor: Colors.white.withOpacity(0.3),
//           highlightColor: Colors.white.withOpacity(0.6),
//           child: SingleChildScrollView(
//             child: ConstrainedBox(
//               constraints: BoxConstraints(
//                 minHeight: availableHeight,
//               ),
//               child: IntrinsicHeight(
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   mainAxisAlignment: MainAxisAlignment.end,
//                   children: [
//                     Container(
//                       width: avatarSize,
//                       height: avatarSize,
//                       decoration: const BoxDecoration(
//                         color: Colors.white,
//                         shape: BoxShape.circle,
//                       ),
//                     ),
//                     SizedBox(height: spacing),
//                     Container(
//                       height: cardHeight,
//                       width: double.infinity,
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         borderRadius: BorderRadius.circular(12),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }

//   Future<void> _logout(BuildContext context) async {
//     await _apiService.clearToken();
//     if (context.mounted) {
//       Navigator.of(context).pushAndRemoveUntil(
//         PageTransitions.fadeSlideDown(
//           page: const LoginScreen(),
//           duration: const Duration(milliseconds: 500),
//         ),
//         (route) => false,
//       );
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Drawer(
//       backgroundColor: AppColors.background,
//       elevation: 0,
//       child: Column(
//         children: <Widget>[
//           Container(
//             height: 280,
//             width: double.infinity,
//             child: Stack(
//               children: [
//                 _buildPatternBackground(),
//                 SafeArea(
//                   bottom: false,
//                   child: Padding(
//                     padding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
//                     child: AnimatedSwitcher(
//                       duration: const Duration(milliseconds: 500),
//                       switchInCurve: Curves.easeIn,
//                       switchOutCurve: Curves.easeOut,
//                       child: _isLoading
//                           ? _buildShimmerHeaderContent()
//                           : _buildHeaderContent(),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           Expanded(
//             child: Container(
//               decoration: BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: const BorderRadius.only(
//                   topLeft: Radius.circular(0),
//                   topRight: Radius.circular(0),
//                 ),
//               ),
//               child: ListView(
//                 padding: const EdgeInsets.symmetric(vertical: 20),
//                 children: <Widget>[
//                   _buildAnimatedDrawerItem(
//                     icon: Icons.dashboard_outlined,
//                     text: 'Dashboard',
//                     delay: 0,
//                     onTap: () {
//                       Navigator.pop(context);
//                       Navigator.pushNamed(context, '/home');
//                     },
//                   ),
//                   _buildAnimatedDrawerItem(
//                     icon: Icons.person_outline_rounded,
//                     text: 'Profile',
//                     delay: 100,
//                     onTap: () {
//                       Navigator.pop(context);
//                       Navigator.pushNamed(context, '/profile');
//                     },
//                   ),
//                   _buildAnimatedDrawerItem(
//                     icon: Icons.settings_outlined,
//                     text: 'Settings',
//                     delay: 200,
//                     onTap: () {
//                       Navigator.pop(context);
//                       Navigator.pushNamed(context, '/settings');
//                     },
//                   ),
//                   _buildAnimatedDrawerItem(
//                     icon: Icons.analytics_outlined,
//                     text: 'Analytics',
//                     delay: 300,
//                     onTap: () {
//                       Navigator.pop(context);
//                       // Navigate to analytics
//                     },
//                   ),
//                   _buildAnimatedDrawerItem(
//                     icon: Icons.help_outline_rounded,
//                     text: 'Help & Support',
//                     delay: 400,
//                     onTap: () {
//                       Navigator.pop(context);
//                       // Navigate to help
//                     },
//                   ),
//                   const SizedBox(height: 20),
//                   Padding(
//                     padding: const EdgeInsets.symmetric(horizontal: 24.0),
//                     child: Divider(
//                       color: AppColors.grey300,
//                       height: 1,
//                       thickness: 1,
//                     ),
//                   ),
//                   const SizedBox(height: 20),
//                   _buildAnimatedDrawerItem(
//                     icon: Icons.logout_rounded,
//                     text: 'Logout',
//                     delay: 500,
//                     iconColor: AppColors.error,
//                     textColor: AppColors.error,
//                     onTap: () => _logout(context),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildAnimatedDrawerItem({
//     required IconData icon,
//     required String text,
//     required VoidCallback onTap,
//     required int delay,
//     Color? iconColor,
//     Color? textColor,
//   }) {
//     return TweenAnimationBuilder<double>(
//       tween: Tween(begin: 0.0, end: 1.0),
//       duration: Duration(milliseconds: 600 + delay),
//       curve: Curves.elasticOut,
//       builder: (context, value, child) {
//         // Clamp the opacity value to ensure it's between 0.0 and 1.0
//         final clampedOpacity = value.clamp(0.0, 1.0);

//         return Transform.translate(
//           offset: Offset((1 - value) * 100, 0),
//           child: Opacity(
//             opacity: clampedOpacity,
//             child: Container(
//               margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(16),
//               ),
//               child: Material(
//                 color: Colors.transparent,
//                 borderRadius: BorderRadius.circular(16),
//                 child: InkWell(
//                   onTap: onTap,
//                   borderRadius: BorderRadius.circular(16),
//                   splashColor: AppColors.primary.withOpacity(0.1),
//                   highlightColor: AppColors.primary.withOpacity(0.05),
//                   child: Container(
//                     padding: const EdgeInsets.symmetric(
//                         horizontal: 20, vertical: 16),
//                     child: Row(
//                       children: <Widget>[
//                         Container(
//                           padding: const EdgeInsets.all(8),
//                           decoration: BoxDecoration(
//                             color: (iconColor ?? AppColors.primary)
//                                 .withOpacity(0.1),
//                             borderRadius: BorderRadius.circular(12),
//                           ),
//                           child: Icon(
//                             icon,
//                             color: iconColor ?? AppColors.primary,
//                             size: 24,
//                           ),
//                         ),
//                         const SizedBox(width: 16),
//                         Expanded(
//                           child: Text(
//                             text,
//                             style: TextStyle(
//                               color: textColor ?? AppColors.text,
//                               fontSize: 16,
//                               fontWeight: FontWeight.w600,
//                               letterSpacing: 0.3,
//                             ),
//                           ),
//                         ),
//                         Icon(
//                           Icons.arrow_forward_ios_rounded,
//                           color: (textColor ?? AppColors.text).withOpacity(0.3),
//                           size: 16,
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }
// }

// class PatternPainter extends CustomPainter {
//   @override
//   void paint(Canvas canvas, Size size) {
//     final paint = Paint()
//       ..color = Colors.white.withOpacity(0.1)
//       ..strokeWidth = 2
//       ..style = PaintingStyle.stroke;

//     final path = Path();

//     // Create geometric pattern
//     for (double x = -50; x < size.width + 50; x += 40) {
//       for (double y = -50; y < size.height + 50; y += 40) {
//         // Draw circles
//         canvas.drawCircle(Offset(x, y), 15, paint);

//         // Draw connecting lines
//         if (x + 40 < size.width + 50) {
//           canvas.drawLine(
//             Offset(x + 15, y),
//             Offset(x + 25, y),
//             paint,
//           );
//         }
//         if (y + 40 < size.height + 50) {
//           canvas.drawLine(
//             Offset(x, y + 15),
//             Offset(x, y + 25),
//             paint,
//           );
//         }
//       }
//     }

//     // Add some floating shapes
//     final shapePaint = Paint()
//       ..color = Colors.white.withOpacity(0.05)
//       ..style = PaintingStyle.fill;

//     for (int i = 0; i < 5; i++) {
//       final x = (size.width / 5) * i + 20;
//       final y = (size.height / 3) * (i % 2) + 30;

//       canvas.drawRRect(
//         RRect.fromRectAndRadius(
//           Rect.fromLTWH(x, y, 30, 30),
//           const Radius.circular(8),
//         ),
//         shapePaint,
//       );
//     }
//   }

//   @override
//   bool shouldRepaint(CustomPainter oldDelegate) => false;
// }

// version 4

import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/core/constants/constants.dart';
import 'package:achawach/core/utils/page_transitions.dart';
import 'package:achawach/routers/app_router.dart';
import 'package:achawach/services/api_service.dart';
import 'package:achawach/screens/auth/login_screen.dart';
import 'package:achawach/models/profile.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';

class AppDrawer extends StatefulWidget {
  const AppDrawer({super.key});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> with TickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  Profile? _profile;
  bool _isLoading = true;
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _bounceController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _bounceAnimation;

  final NumberFormat _currencyFormatter = NumberFormat.currency(
    locale: 'am_ET',
    symbol: 'Birr ',
    decimalDigits: 2,
  );

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadProfile();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _slideAnimation = CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    );
    _bounceAnimation = CurvedAnimation(
      parent: _bounceController,
      curve: Curves.bounceOut,
    );

    // Start animations with delays
    Future.delayed(const Duration(milliseconds: 100), () {
      _fadeController.forward();
    });
    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });
    Future.delayed(const Duration(milliseconds: 500), () {
      _bounceController.forward();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  Future<void> _loadProfile() async {
    try {
      final profile = await _apiService.getProfile();

      if (mounted) {
        setState(() {
          _profile = profile;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      debugPrint("Error loading profile: $e");
      if (e.toString().contains('Unauthorized')) {
        // ignore: use_build_context_synchronously
        _logout(context);
      }
    }
  }

  Widget _buildPatternBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary,
            AppColors.primary.withOpacity(0.8),
            AppColors.secondary.withOpacity(0.6),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: CustomPaint(
        painter: PatternPainter(),
        size: Size.infinite,
      ),
    );
  }

  Widget _buildHeaderContent() {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(-1, 0),
        end: Offset.zero,
      ).animate(_slideAnimation),
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: LayoutBuilder(
          builder: (context, constraints) {
            final availableHeight = constraints.maxHeight;
            // Adjusted sizes for the new layout
            final avatarRadius = availableHeight < 150 ? 20.0 : 25.0;
            final internalSpacing = availableHeight < 150 ? 8.0 : 10.0;
            final nameFontSize = availableHeight < 150 ? 15.0 : 17.0;
            final phoneFontSize = availableHeight < 150 ? 11.0 : 13.0;
            final earningsFontSize = availableHeight < 150 ? 12.0 : 14.0;

            return SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: availableHeight,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment
                        .end, // Keeps content block at the bottom of allocated space
                    children: [
                      // Row for Image, Name, Phone
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          ScaleTransition(
                            scale: _bounceAnimation,
                            child: _buildResponsiveProfileImage(avatarRadius),
                          ),
                          SizedBox(width: internalSpacing),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  _profile?.fullName ?? 'User Name',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: nameFontSize,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.3,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: internalSpacing / 3),
                                if (_profile?.phoneNumber != null &&
                                    _profile!.phoneNumber.isNotEmpty)
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.phone_outlined,
                                        color: Colors.white.withOpacity(0.85),
                                        size: phoneFontSize + 1,
                                      ),
                                      const SizedBox(width: 5),
                                      Expanded(
                                        child: Text(
                                          _profile!.phoneNumber,
                                          style: TextStyle(
                                            color:
                                                Colors.white.withOpacity(0.9),
                                            fontSize: phoneFontSize,
                                            fontWeight: FontWeight.w500,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  )
                                else
                                  Text(
                                    'No phone number',
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.7),
                                      fontSize: phoneFontSize - 1,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                          height: internalSpacing *
                              1.5), // Space between (image,name,phone) row and Earnings

                      // Earnings section at the bottom of the header content
                      if (_profile?.totalEarned != null)
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: internalSpacing * 1.2,
                              vertical: internalSpacing * 0.6),
                          decoration: BoxDecoration(
                            color: AppColors.accent.withOpacity(0.85),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                                color: Colors.white.withOpacity(0.3)),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.15),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(2),
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.monetization_on,
                                  color: AppColors.accent,
                                  size: earningsFontSize,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Earned: ',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.95),
                                  fontSize: earningsFontSize - 1,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                _currencyFormatter
                                    .format(_profile!.totalEarned!),
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: earningsFontSize,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        )
                      else if (!_isLoading) // Show only if not loading and no earnings
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: internalSpacing * 1.2,
                              vertical: internalSpacing * 0.6),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            'No earnings yet',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.7),
                              fontSize: earningsFontSize - 1,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildResponsiveProfileImage(double radius) {
    Widget avatarWidget;

    if (_profile != null) {
      if (_profile!.isProfileImageAvailable == 1 &&
          _profile!.profileImagePath != null) {
        avatarWidget = Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: radius,
            backgroundImage: NetworkImage(
              '${AppConstants.apiBaseIP}/${_profile!.profileImagePath}'
                  .replaceAll('\\', '/'),
            ),
          ),
        );
      } else if (_profile!.avatar != null) {
        avatarWidget = Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
            gradient: LinearGradient(
              colors: [Colors.white, Colors.white.withOpacity(0.9)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: radius,
            backgroundColor: Colors.transparent,
            child: Text(
              _profile!.avatar!,
              style: TextStyle(
                fontSize: radius * 0.8, // Adjusted for better fit
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      } else {
        avatarWidget = _buildDefaultResponsiveAvatar(radius);
      }
    } else {
      avatarWidget = _buildDefaultResponsiveAvatar(radius);
    }

    return avatarWidget;
  }

  Widget _buildDefaultResponsiveAvatar(double radius) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 2),
        gradient: LinearGradient(
          colors: [Colors.white, Colors.white.withOpacity(0.9)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: CircleAvatar(
        radius: radius,
        backgroundColor: Colors.transparent,
        child: Icon(
          Icons.person_outline,
          size: radius * 1.2, // Adjusted for better fit
          color: AppColors.primary.withOpacity(0.8),
        ),
      ),
    );
  }

  Widget _buildShimmerHeaderContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableHeight = constraints.maxHeight;
        final avatarRadius = availableHeight < 150 ? 20.0 : 25.0;
        final internalSpacing = availableHeight < 150 ? 8.0 : 10.0;
        final nameLineHeight = availableHeight < 150 ? 15.0 : 17.0;
        final phoneLineHeight = availableHeight < 150 ? 11.0 : 13.0;
        final earningsHeight = availableHeight < 150 ? 30.0 : 36.0;

        return Shimmer.fromColors(
          baseColor: Colors.white.withOpacity(0.25),
          highlightColor: Colors.white.withOpacity(0.5),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment:
                MainAxisAlignment.end, // Match actual content placement
            children: [
              // Shimmer for Image, Name, Phone Row
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: avatarRadius * 2,
                    height: avatarRadius * 2,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: internalSpacing),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: constraints.maxWidth * 0.55,
                          height: nameLineHeight,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        SizedBox(height: internalSpacing / 3),
                        Container(
                          width: constraints.maxWidth * 0.45,
                          height: phoneLineHeight,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: internalSpacing * 1.5),
              // Shimmer for Earnings
              Container(
                width: constraints.maxWidth * 0.5,
                height: earningsHeight,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _logout(BuildContext context) async {
    await _apiService.clearToken();
    if (context.mounted) {
      Navigator.of(context).pushAndRemoveUntil(
        PageTransitions.fadeSlideDown(
          page: const LoginScreen(),
          duration: const Duration(milliseconds: 500),
        ),
        (route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.background,
      elevation: 0,
      child: Column(
        children: <Widget>[
          SizedBox(
            // Use SizedBox for header container instead of Container with child
            height:
                280, // Consider adjusting if content feels too cramped or too spacious
            width: double.infinity,
            child: Stack(
              children: [
                _buildPatternBackground(),
                SafeArea(
                  bottom: false,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(
                        24, 20, 24, 24), // Overall padding for header content
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 500),
                      switchInCurve: Curves.easeIn,
                      switchOutCurve: Curves.easeOut,
                      child: _isLoading
                          ? _buildShimmerHeaderContent()
                          : _buildHeaderContent(),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                // No top border radius needed if header visually connects
              ),
              child: ListView(
                padding: const EdgeInsets.symmetric(vertical: 20),
                children: <Widget>[
                  _buildAnimatedDrawerItem(
                    icon: Icons.dashboard_outlined,
                    text: 'Dashboard',
                    delay: 0,
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.pushNamed(context, '/home');
                    },
                  ),
                  _buildAnimatedDrawerItem(
                    icon: Icons.person_outline_rounded,
                    text: 'Profile',
                    delay: 100,
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.pushNamed(context, '/profile');
                    },
                  ),
                  _buildAnimatedDrawerItem(
                    icon: Icons.settings_outlined,
                    text: 'Settings',
                    delay: 200,
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.pushNamed(context, '/settings');
                    },
                  ),
                  _buildAnimatedDrawerItem(
                    icon: Icons.analytics_outlined,
                    text: 'Analytics',
                    delay: 300,
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.pushNamed(context, AppRouter.analyticsRoute);
                    },
                  ),
                  _buildAnimatedDrawerItem(
                    icon: Icons.help_outline_rounded,
                    text: 'Help & Support',
                    delay: 400,
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.pushNamed(context, AppRouter.helpRoute);
                    },
                  ),
                  const SizedBox(height: 20),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Divider(
                      color: AppColors.grey300,
                      height: 1,
                      thickness: 1,
                    ),
                  ),
                  const SizedBox(height: 20),
                  SafeArea(
                    child: _buildAnimatedDrawerItem(
                      icon: Icons.logout_rounded,
                      text: 'Logout',
                      delay: 500,
                      iconColor: AppColors.error,
                      textColor: AppColors.error,
                      onTap: () => _logout(context),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedDrawerItem({
    required IconData icon,
    required String text,
    required VoidCallback onTap,
    required int delay,
    Color? iconColor,
    Color? textColor,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: 600 + delay),
      curve: Curves.elasticOut,
      builder: (context, value, child) {
        final clampedOpacity = value.clamp(0.0, 1.0);

        return Transform.translate(
          offset: Offset((1 - value) * 100, 0),
          child: Opacity(
            opacity: clampedOpacity,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(16),
                child: InkWell(
                  onTap: onTap,
                  borderRadius: BorderRadius.circular(16),
                  splashColor: AppColors.primary.withOpacity(0.1),
                  highlightColor: AppColors.primary.withOpacity(0.05),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 16),
                    child: Row(
                      children: <Widget>[
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: (iconColor ?? AppColors.primary)
                                .withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            icon,
                            color: iconColor ?? AppColors.primary,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            text,
                            style: TextStyle(
                              color: textColor ?? AppColors.text,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.3,
                            ),
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios_rounded,
                          color: (textColor ?? AppColors.text).withOpacity(0.3),
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class PatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // Create geometric pattern
    for (double x = -50; x < size.width + 50; x += 40) {
      for (double y = -50; y < size.height + 50; y += 40) {
        canvas.drawCircle(Offset(x, y), 15, paint);
        if (x + 40 < size.width + 50) {
          canvas.drawLine(
            Offset(x + 15, y),
            Offset(x + 25, y),
            paint,
          );
        }
        if (y + 40 < size.height + 50) {
          canvas.drawLine(
            Offset(x, y + 15),
            Offset(x, y + 25),
            paint,
          );
        }
      }
    }

    final shapePaint = Paint()
      ..color = Colors.white.withOpacity(0.05)
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 5; i++) {
      final x = (size.width / 5) * i + 20;
      final y = (size.height / 3) * (i % 2) + 30;
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(x, y, 30, 30),
          const Radius.circular(8),
        ),
        shapePaint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
