import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Simple mock classes for testing
class MockSocketService {
  final StreamController<Map<String, dynamic>> gameEndedController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> winnerAnnouncementController =
      StreamController<Map<String, dynamic>>.broadcast();

  String? endGameToken;
  String? endGameChallengeId;
  bool isConnected = false;

  Stream<Map<String, dynamic>> get gameEndedStream =>
      gameEndedController.stream;
  Stream<Map<String, dynamic>> get winnerAnnouncementStream =>
      winnerAnnouncementController.stream;

  void endGame(String token, String challengeId) {
    endGameToken = token;
    endGameChallengeId = challengeId;
  }

  void dispose() {
    gameEndedController.close();
    winnerAnnouncementController.close();
  }
}

// Simple mock for GameEndService that allows us to test without Navigator
class TestableGameEndService {
  final MockSocketService socketService;
  StreamSubscription? gameEndedSubscription;
  StreamSubscription? winnerAnnouncementSubscription;

  TestableGameEndService(this.socketService);

  void initialize() {
    // Cancel any existing subscriptions
    gameEndedSubscription?.cancel();
    winnerAnnouncementSubscription?.cancel();

    // Listen for gameEnded events
    gameEndedSubscription = socketService.gameEndedStream.listen((data) {
      print('TestableGameEndService: Received gameEnded event: $data');
    });

    // Listen for winnerAnnouncement events
    winnerAnnouncementSubscription =
        socketService.winnerAnnouncementStream.listen((data) {
      print('TestableGameEndService: Received winnerAnnouncement event: $data');
    });
  }

  Future<void> endGame(int challengeId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? prefs.getString('token');

      if (token == null || token.isEmpty) {
        print('TestableGameEndService: No token found for ending game');
        return;
      }

      socketService.endGame(token, challengeId.toString());
    } catch (e) {
      print('TestableGameEndService: Error ending game: $e');
    }
  }

  void dispose() {
    gameEndedSubscription?.cancel();
    winnerAnnouncementSubscription?.cancel();
  }
}

void main() {
  group('GameEndService Tests', () {
    late MockSocketService mockSocketService;
    late TestableGameEndService gameEndService;

    setUp(() {
      // Create mock socket service
      mockSocketService = MockSocketService();

      // Create the service with the mock socket
      gameEndService = TestableGameEndService(mockSocketService);
    });

    tearDown(() {
      // Clean up
      gameEndService.dispose();
      mockSocketService.dispose();
    });

    test('endGame calls socket service with correct parameters', () async {
      // Setup SharedPreferences mock
      SharedPreferences.setMockInitialValues({'auth_token': 'test-token'});

      // Call endGame
      await gameEndService.endGame(123);

      // Verify that endGame was called on the socket service with correct parameters
      expect(mockSocketService.endGameToken, equals('test-token'));
      expect(mockSocketService.endGameChallengeId, equals('123'));
    });

    test('initialize sets up listeners for game end events', () {
      // Initialize the service
      gameEndService.initialize();

      // Verify that subscriptions are created
      expect(gameEndService.gameEndedSubscription, isNotNull);
      expect(gameEndService.winnerAnnouncementSubscription, isNotNull);
    });

    test('gameEndedStream listener receives and processes data correctly',
        () async {
      // Initialize the service
      gameEndService.initialize();

      // Create test data
      final testData = {
        'isWinner': true,
        'message': 'You won!',
        'finalState': {'score': 500, 'level': 3},
      };

      // Add data to the stream
      mockSocketService.gameEndedController.add(testData);

      // Wait for the stream to process
      await Future.delayed(const Duration(milliseconds: 100));

      // We can't directly verify the data was processed, but the test passes if no exceptions are thrown
      expect(true, isTrue);
    });

    test(
        'winnerAnnouncementStream listener receives and processes data correctly',
        () async {
      // Initialize the service
      gameEndService.initialize();

      // Create test data
      final testData = {
        'winnerId': 123,
        'challengeId': 456,
        'winnerScore': 500,
        'winnerRank': 1,
        'winnerLevel': 3,
      };

      // Add data to the stream
      mockSocketService.winnerAnnouncementController.add(testData);

      // Wait for the stream to process
      await Future.delayed(const Duration(milliseconds: 100));

      // We can't directly verify the data was processed, but the test passes if no exceptions are thrown
      expect(true, isTrue);
    });

    test('dispose cancels subscriptions', () {
      // Initialize the service
      gameEndService.initialize();

      // Verify that subscriptions exist
      expect(gameEndService.gameEndedSubscription, isNotNull);
      expect(gameEndService.winnerAnnouncementSubscription, isNotNull);

      // Dispose the service
      gameEndService.dispose();

      // After dispose, the service should have cleaned up its subscriptions
      // We can't directly test if they're canceled, but we can verify the test completes
      expect(true, isTrue);
    });
  });
}
