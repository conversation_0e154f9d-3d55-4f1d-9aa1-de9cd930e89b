import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:achawach/services/auth_handler.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

import 'package:shared_preferences/shared_preferences.dart';
import '../models/category.dart' as models;
import '../models/challenge.dart';
import '../models/profile.dart';
import '../models/leaderboard_entry.dart';
import '../models/recent_activity.dart';
import '../core/constants/constants.dart';

class ApiService {
  static const String baseUrl = AppConstants.apiBaseUrl;
  static const String tokenKey = 'auth_token';
  String? _authToken;

  ApiService() {
    _loadToken();
  }

  Future<void> _loadToken() async {
    final prefs = await SharedPreferences.getInstance();
    _authToken = prefs.getString(tokenKey);
  }

  Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(tokenKey, token);
    _authToken = token;
  }

  Future<void> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(tokenKey);
    _authToken = null;
  }

  Map<String, String> get _headers {
    final headers = {'Content-Type': 'application/json'};
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    return headers;
  }

  Future<void> login(String phoneNumber, String password) async {
    try {
      debugPrint('Attempting login to: $baseUrl/auth/login');

      // Create the request body
      final requestBody = json.encode({
        'phoneNumber': phoneNumber,
        'password': password,
      });

      // Make the request with a timeout
      final response = await http
          .post(
        Uri.parse('$baseUrl/auth/login'),
        headers: {'Content-Type': 'application/json'},
        body: requestBody,
      )
          .timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw TimeoutException(
              'Login request timed out. Server might be unavailable.');
        },
      );

      debugPrint('Login response status code: ${response.statusCode}');

      // Try to parse the response body
      Map<String, dynamic> jsonResponse;
      try {
        jsonResponse = json.decode(response.body);
      } catch (e) {
        debugPrint('Failed to parse response body: $e');
        throw const FormatException('Invalid response format from server');
      }

      if (response.statusCode == 200 && jsonResponse['status'] == 'success') {
        debugPrint('Login successful, saving token');
        final token = jsonResponse['data']['token'];
        await _saveToken(token);
      } else {
        // Handle different status codes
        if (response.statusCode == 401) {
          throw Exception('Invalid phone number or password');
        } else if (response.statusCode == 404) {
          throw Exception('User not found');
        } else if (response.statusCode >= 500) {
          throw Exception('Server error. Please try again later');
        } else {
          throw Exception(jsonResponse['message'] ?? 'Login failed');
        }
      }
    } catch (e) {
      debugPrint('Login error: $e');
      if (e is SocketException) {
        throw Exception(
            'Cannot connect to server. Please check your internet connection.');
      } else if (e is TimeoutException) {
        throw Exception('Request timed out. Server might be unavailable.');
      } else if (e is FormatException) {
        throw Exception('Invalid response from server. Please try again.');
      } else {
        throw Exception('Error during login: $e');
      }
    }
  }

  Future register(
    String fullName,
    String phoneNumber,
    String password, {
    String? profileImage,
    String? avatar,
    List<String>? categories,
  }) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/auth/register'),
      );

      // Add form fields
      request.fields['fullName'] = fullName;
      request.fields['phoneNumber'] = phoneNumber;
      request.fields['password'] = password;
      request.fields['isProfileImageAvailable'] =
          profileImage != null && profileImage.isNotEmpty ? '1' : '0';

      if (avatar != null) {
        request.fields['avatar'] = avatar;
      }
      if (categories != null && categories.isNotEmpty) {
        request.fields['categories'] = jsonEncode(categories);
      }

      // Add profile image file
      if (profileImage != null && profileImage.isNotEmpty) {
        File file = File(profileImage);

        bool fileExists = await file.exists();

        if (fileExists) {
          // Check the file size to ensure it's not empty
          int fileSize = await file.length();

          if (fileSize > 0) {
            // Get file extension to determine content type
            String extension = profileImage.split('.').last.toLowerCase();
            String contentType = 'image/jpeg'; // Default

            if (extension == 'png') {
              contentType = 'image/png';
            } else if (extension == 'jpg' || extension == 'jpeg') {
              contentType = 'image/jpeg';
            }

            // Create the MultipartFile with explicit content type
            final multipartFile = await http.MultipartFile.fromPath(
              'profileImage', // This is the field name the server expects
              file.path,
              contentType: MediaType.parse(contentType),
            );

            request.files.add(multipartFile);
          } else {
            print("Warning: File exists but is empty (0 bytes)");
          }
        } else {
          print("Warning: File does not exist at path: $profileImage");
        }
      }

      // Add debug information to headers
      request.headers['Accept'] = 'application/json';

      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (jsonResponse['status'] == 'success') {
          print("Registration successful!");
          final token = jsonResponse['data']['token'];
          await _saveToken(token);
          return jsonResponse['data'];
        } else {
          throw Exception(jsonResponse['message'] ?? 'Registration failed');
        }
      } else {
        throw Exception('${jsonResponse['message']}');
      }
    } catch (e) {
      throw Exception('$e');
    }
  }

  Future<List<models.Category>> getCategories() async {
    try {
      await _loadToken(); // Ensure token is loaded
      final response = await http.get(
        Uri.parse('$baseUrl/categories'),
        headers: _headers,
      );
      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          final List<dynamic> data = jsonResponse['data'];
          return data.map((json) => models.Category.fromJson(json)).toList();
        } else {
          throw Exception(
              jsonResponse['message'] ?? 'Failed to load categories');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        await clearToken();
        throw Exception('Failed to load categories: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching categories: $e'); // For debugging
      await clearToken();
      throw Exception('Error fetching categories: $e');
    }
  }

  Future<List<Challenge>> getChallenges() async {
    try {
      await _loadToken(); // Ensure token is loaded
      final response = await http.get(
        Uri.parse('$baseUrl/challenges'),
        headers: _headers,
      );

      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          final List<dynamic> data = jsonResponse['data'];
          return data.map((json) => Challenge.fromJson(json)).toList();
        } else {
          throw Exception(
              jsonResponse['message'] ?? 'Failed to load challenges');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to load challenges: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching challenges: $e'); // For debugging
      throw Exception('Error fetching challenges: $e');
    }
  }

  Future<Profile> getProfile() async {
    try {
      await _loadToken(); // Ensure token is loaded
      final response = await http.get(
        Uri.parse('$baseUrl/profile'),
        headers: _headers,
      );

      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          return Profile.fromJson(jsonResponse['data']);
        } else {
          throw Exception(jsonResponse['message'] ?? 'Failed to load profile');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to load profile: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching profile: $e'); // For debugging
      throw Exception('Error fetching profile: $e');
    }
  }

  Future<Profile> getTotalEarned() async {
    try {
      await _loadToken(); // Ensure token is loaded
      final response = await http.get(
        Uri.parse('$baseUrl/user-earned'),
        headers: _headers,
      );
      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          return Profile.fromJson(jsonResponse['data']);
        } else {
          throw Exception(jsonResponse['message'] ?? 'Failed to load profile');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to load profile: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching profile: $e'); // For debugging
      throw Exception('Error fetching profile: $e');
    }
  }

  Future<List<LeaderboardEntry>> getLeaderboard(int challengeId) async {
    try {
      await _loadToken();
      final response = await http.get(
        Uri.parse('$baseUrl/challenges/$challengeId/leaderboard'),
        headers: _headers,
      );

      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          final List<dynamic> data = jsonResponse['data'];
          return data.map((json) => LeaderboardEntry.fromJson(json)).toList();
        } else {
          throw Exception(
              jsonResponse['message'] ?? 'Failed to load leaderboard');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to load leaderboard: ${response.statusCode}');
      }
    } catch (e) {
      
      throw Exception('Error fetching leaderboard: $e');
    }
  }

  Future<Map<String, dynamic>> startChallenge(int challengeId) async {
    try {
      await _loadToken();
      final response = await http.post(
        Uri.parse('$baseUrl/challenges/$challengeId/start'),
        headers: _headers,
      );
      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          return jsonResponse['data'];
        } else {
          throw Exception(
              jsonResponse['message'] ?? 'Failed to start challenge');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to start challenge: ${response.statusCode}');
      }
    } catch (e) {
      print('Error starting challenge: $e');
      throw Exception('Error starting challenge: $e');
    }
  }

  Future<List<Challenge>> getCategoryStartedChallenges(int categoryId) async {
    try {
      await _loadToken();
      final response = await http.get(
        Uri.parse('$baseUrl/challenges/$categoryId/started'),
        headers: _headers,
      );
      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          final List<dynamic> data = jsonResponse['data'];
          return data.map((json) => Challenge.fromJson(json)).toList();
        } else {
          throw Exception(
              jsonResponse['message'] ?? 'Failed to load started challenges');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception(
            'Failed to load started challenges: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching started challenges: $e');
      throw Exception('Error fetching started challenges: $e');
    }
  }

  Future<List<Challenge>> getNotStartedChallenges(categoryId) async {
    try {
      await _loadToken();
      final response = await http.get(
        Uri.parse('$baseUrl/challenges/$categoryId/not-started'),
        headers: _headers,
      );

      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          final List<dynamic> data = jsonResponse['data'];
          return data.map((json) => Challenge.fromJson(json)).toList();
        } else {
          throw Exception(jsonResponse['message'] ??
              'Failed to load not started challenges');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception(
            'Failed to load not started challenges: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching not started challenges: $e');
      throw Exception('Error fetching not started challenges: $e');
    }
  }

  Future<Map<String, dynamic>> getChallengeQuestions(int challengeId) async {
    try {
      await _loadToken();
      final response = await http.get(
        Uri.parse('$baseUrl/challenges/$challengeId/questions'),
        headers: _headers,
      );

      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          return jsonResponse['data'];
        } else {
          throw Exception(
              jsonResponse['message'] ?? 'Failed to load questions');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to load questions: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching challenge questions: $e');
      throw Exception('Error fetching challenge questions: $e');
    }
  }

  Future<Map<String, dynamic>> submitAnswer(
      int challengeId, int questionId, int answerId) async {
    try {
      await _loadToken();
      final response = await http.post(
        Uri.parse('$baseUrl/challenges/$challengeId/answer'),
        headers: _headers,
        body: json.encode({
          'questionId': questionId,
          'answerId': answerId,
        }),
      );

      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          return jsonResponse['data'];
        } else {
          throw Exception(jsonResponse['message'] ?? 'Failed to submit answer');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to submit answer: ${response.statusCode}');
      }
    } catch (e) {
      print('Error submitting answer: $e');
      throw Exception('Error submitting answer: $e');
    }
  }

  Future<Map<String, dynamic>> getChallengeLife(int challengeId) async {
    try {
      await _loadToken();
      final response = await http.get(
        Uri.parse('$baseUrl/challenges/$challengeId/life'),
        headers: _headers,
      );

      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          return jsonResponse['data'];
        } else {
          throw Exception(jsonResponse['message'] ?? 'Failed to get life');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to get life: ${response.statusCode}');
      }
    } catch (e) {
      print('Error getting challenge life: $e');
      throw Exception('Error getting challenge life: $e');
    }
  }

  Future<Map<String, dynamic>> decreaseChallengeLife(
      int challengeId, int qId) async {
    try {
      await _loadToken();
      final response = await http.put(
        Uri.parse('$baseUrl/challenges/$challengeId/$qId/life/decrease'),
        headers: _headers,
      );

      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          return jsonResponse['data'];
        } else {
          throw Exception(jsonResponse['message'] ?? 'Failed to decrease life');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to decrease life: ${response.statusCode}');
      }
    } catch (e) {
      print('Error decreasing challenge life: $e');
      throw Exception('Error decreasing challenge life: $e');
    }
  }

  // Add method to fetch top challenges
  Future<List<Map<String, dynamic>>> getTopChallenges() async {
    try {
      await _loadToken();
      final response = await http.get(
        Uri.parse('$baseUrl/challenges/special/top'),
        headers: _headers,
      );
      await AuthHandler.handleResponse(response);
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);

        if (jsonResponse['status'] == 'success') {
          final List<dynamic> challengesData = jsonResponse['data'];
          return challengesData
              .map((challenge) => Map<String, dynamic>.from(challenge))
              .toList();
        } else {
          throw Exception(
              jsonResponse['message'] ?? 'Failed to load top challenges');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        await clearToken();
        throw Exception(
            'Failed to load top challenges: ${response.statusCode}');
      }
    } catch (e) {
      await clearToken();
      print('Error fetching top challenges: $e');
      throw Exception('Failed to load top challenges: $e');
    }
  }

  Future<List<Map<String, dynamic>>> searchChallenges({
    String? search,
    int? categoryId,
    int? minWinningRules,
    int? minReward,
    int? maxReward,
    String? status,
    int? minUserLife,
    String? createdDate,
    String? startDate,
    String? endDate,
    String? participationStatus,
    String sortBy = 'popularity',
    int page = 1,
    int limit = 20,
  }) async {
    try {
      await _loadToken();

      final Map<String, String> queryParams = {
        if (search != null && search.isNotEmpty) 'search': search,
        if (categoryId != null) 'category_id': categoryId.toString(),
        if (minWinningRules != null)
          'min_winning_rules': minWinningRules.toString(),
        if (minReward != null) 'min_reward': minReward.toString(),
        if (maxReward != null) 'max_reward': maxReward.toString(),
        if (status != null) 'status': status,
        if (minUserLife != null) 'min_user_life': minUserLife.toString(),
        if (createdDate != null) 'created_date': createdDate,
        if (startDate != null) 'start_date': startDate,
        if (endDate != null) 'end_date': endDate,
        if (participationStatus != null)
          'participation_status': participationStatus,
        'sort_by': sortBy,
        'page': page.toString(),
        'limit': limit.toString(),
      };

      final uri = Uri.parse('$baseUrl/challenges/search')
          .replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _headers);

      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] == 'success') {
          // The data is a Map containing 'challenges' and 'pagination'
          final Map<String, dynamic> data = jsonResponse['data'];

          // Extract the challenges list from the data
          final List<dynamic> challenges = data['challenges'];

          // You might want to store pagination info somewhere if needed
          // final Map<String, dynamic> pagination = data['pagination'];

          return challenges
              .map((challenge) => Map<String, dynamic>.from(challenge))
              .toList();
        } else {
          throw Exception(
              jsonResponse['message'] ?? 'Failed to search challenges');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to search challenges: ${response.statusCode}');
      }
    } catch (e) {
      print('Error searching challenges: $e');
      throw Exception('Error searching challenges: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getStartedChallenges(
      [int? categoryId]) async {
    if (categoryId != null) {
      final challenges = await getCategoryStartedChallenges(categoryId);

      return challenges.map((c) => c.toJson()).toList();
    }

    try {
      await _loadToken();
      final response = await http.get(
        Uri.parse('$baseUrl/challenges/started'),
        headers: _headers,
      );
      await AuthHandler.handleResponse(response);
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);

        if (jsonResponse['status'] == 'success') {
          final List<dynamic> data = jsonResponse['data'];
          
          return data.map((item) => Map<String, dynamic>.from(item)).toList();
        } else {
          throw jsonResponse['message'] ?? 'Failed to load started challenges';
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw 'Unauthorized access';
      } else {
        throw 'Failed to load started challenges';
      }
    } catch (e) {
      print('Error loading started challenges: $e');
      throw Exception('Error loading started challenges: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getJoinedChallenges() async {
    try {
      await _loadToken();
      final response = await http.get(
        Uri.parse('$baseUrl/challenges/joined'),
        headers: _headers,
      );
      await AuthHandler.handleResponse(response);
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);

        if (jsonResponse['status'] == 'success') {
          final List<dynamic> data = jsonResponse['data'];
         
          return data.map((item) => Map<String, dynamic>.from(item)).toList();
        } else {
          throw jsonResponse['message'] ?? 'Failed to load started challenges';
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw 'Unauthorized access';
      } else {
        throw 'Failed to load started challenges';
      }
    } catch (e) {
      print('Error loading started challenges: $e');
      throw Exception('Error loading started challenges: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchCategories() async {
    final response =
        await http.get(Uri.parse('$baseUrl/settings/questions/categories'));
    await AuthHandler.handleResponse(response);
    if (response.statusCode == 200) {
      final decodedResponse = json.decode(response.body);
      if (decodedResponse['status'] == 'success') {
        return List<Map<String, dynamic>>.from(decodedResponse['data']);
      } else {
        throw Exception(
            'Failed to load categories: ${decodedResponse['message']}');
      }
    } else {
      throw Exception(
          'Failed to load categories: HTTP status ${response.statusCode}');
    }
  }

  Future<List<Map<String, dynamic>>> getCompletedChallenges() async {
    try {
      await _loadToken();
      final response = await http.get(
        Uri.parse('$baseUrl/challenges/completed'),
        headers: _headers,
      );
      await AuthHandler.handleResponse(response);
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);

        if (jsonResponse['status'] == 'success') {
          final List<dynamic> data = jsonResponse['data'];
          return data.map((item) => Map<String, dynamic>.from(item)).toList();
        } else {
          throw jsonResponse['message'] ??
              'Failed to load completed challenges';
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw 'Unauthorized access';
      } else {
        throw 'Failed to load completed challenges';
      }
    } catch (e) {
      print('Error loading completed challenges: $e');
      // Return mock data for development
      return [];
    }
  }

  Future<List<RecentActivity>> getRecentActivity() async {
    try {
      await _loadToken();
      final response = await http.get(
        Uri.parse('$baseUrl/recent-activity'),
        headers: _headers,
      );
      await AuthHandler.handleResponse(response);
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);

        if (jsonResponse['status'] == 'success') {
          final List<dynamic> data = jsonResponse['data'];
          return data.map((item) => RecentActivity.fromJson(item)).toList();
        } else {
          throw jsonResponse['message'] ?? 'Failed to load recent activity';
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw 'Unauthorized access';
      } else {
        throw 'Failed to load recent activity';
      }
    } catch (e) {
      print('Error loading recent activity: $e');
      throw Exception('Error loading recent activity: $e');
    }
  }

  Future<void> changePassword(String oldPassword, String newPassword) async {
    try {
      await _loadToken();
      final response = await http.post(
        Uri.parse('$baseUrl/auth/change-password'),
        headers: _headers,
        body: json.encode({
          'oldPassword': oldPassword,
          'newPassword': newPassword,
        }),
      );
      await AuthHandler.handleResponse(response);
      final Map<String, dynamic> jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        if (jsonResponse['status'] != 'success') {
          throw Exception(jsonResponse['message'] ?? 'Failed to change password');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to change password: ${response.statusCode}');
      }
    } catch (e) {
      print('Error changing password: $e');
      throw Exception('Error changing password: $e');
    }
  }
}
