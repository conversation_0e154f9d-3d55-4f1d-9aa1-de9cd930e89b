import 'package:shared_preferences/shared_preferences.dart';

/// A utility class to manage saved login credentials
class CredentialsManager {
  // Keys for SharedPreferences
  static const String _rememberMeKey = 'remember_me';
  static const String _phoneNumberKey = 'saved_phone_number';
  static const String _countryCodeKey = 'saved_country_code';
  static const String _passwordKey = 'saved_password'; // Note: In a production app, consider more secure storage

  /// Save user credentials if remember me is enabled
  static Future<void> saveCredentials({
    required bool rememberMe,
    required String phoneNumber,
    required String countryCode,
    required String password,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    
    // Always save the remember me preference
    await prefs.setBool(_rememberMeKey, rememberMe);
    
    if (rememberMe) {
      // Only save credentials if remember me is enabled
      await prefs.setString(_phoneNumberKey, phoneNumber);
      await prefs.setString(_countryCodeKey, countryCode);
      await prefs.setString(_passwordKey, password);
    } else {
      // Clear saved credentials if remember me is disabled
      await clearCredentials(keepRememberMePreference: true);
    }
  }

  /// Get the saved remember me preference
  static Future<bool> getRememberMe() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_rememberMeKey) ?? false;
  }

  /// Get the saved phone number
  static Future<String?> getPhoneNumber() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_phoneNumberKey);
  }

  /// Get the saved country code
  static Future<String?> getCountryCode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_countryCodeKey);
  }

  /// Get the saved password
  static Future<String?> getPassword() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_passwordKey);
  }

  /// Clear all saved credentials
  static Future<void> clearCredentials({bool keepRememberMePreference = false}) async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.remove(_phoneNumberKey);
    await prefs.remove(_countryCodeKey);
    await prefs.remove(_passwordKey);
    
    if (!keepRememberMePreference) {
      await prefs.remove(_rememberMeKey);
    }
  }

  /// Check if credentials are saved
  static Future<bool> hasSavedCredentials() async {
    final rememberMe = await getRememberMe();
    if (!rememberMe) return false;
    
    final phoneNumber = await getPhoneNumber();
    final password = await getPassword();
    
    return phoneNumber != null && phoneNumber.isNotEmpty && 
           password != null && password.isNotEmpty;
  }
}
