import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:achawach/screens/auth/login_screen.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

// Simple mock for login functionality
class SimpleLoginMock {
  Future<void> login(String phoneNumber, String password) async {
    // Mock successful login
    return;
  }
}

void main() {
  group('LoginScreen Tests', () {
    // Create a simplified mock version of LoginScreen for testing
    Widget createLoginScreen() {
      return const MaterialApp(
        home: LoginScreen(),
      );
    }

    testWidgets('LoginScreen renders correctly', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(createLoginScreen());

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify UI elements are displayed
      expect(find.text('Welcome Back!'), findsOneWidget);
      expect(find.text('Sign in to continue'), findsOneWidget);
      expect(find.text('Phone Number'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
    });

    testWidgets('LoginScreen has password field', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(createLoginScreen());

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify password field is present
      expect(find.byType(TextFormField), findsAtLeastNWidgets(1));

      // Verify password label is displayed
      expect(find.text('Password'), findsOneWidget);
    });

    testWidgets('LoginScreen has phone number field',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(createLoginScreen());

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify phone number field is present
      expect(find.text('Phone Number'), findsOneWidget);
      expect(find.byType(InternationalPhoneNumberInput), findsOneWidget);
    });

    testWidgets('LoginScreen has welcome text', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(createLoginScreen());

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify welcome text is displayed
      expect(find.text('Welcome Back!'), findsOneWidget);
      expect(find.text('Sign in to continue'), findsOneWidget);
    });
  });
}
