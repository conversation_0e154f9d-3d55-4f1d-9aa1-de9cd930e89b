// import 'package:achawach/widgets/full_page_loader.dart';
// import 'package:flutter/material.dart';

// /// Examples of how to use the different loader widgets across the app
// class LoaderUsageExamples {
  
//   /// Example 1: Using ChallengeLoader in a challenge-related screen
//   static Widget challengeScreenExample() {
//     return StatefulWidget(
//       child: _ChallengeScreenExample(),
//     );
//   }

//   /// Example 2: Using GameLoader when starting a game
//   static Widget gameScreenExample() {
//     return StatefulWidget(
//       child: _GameScreenExample(),
//     );
//   }

//   /// Example 3: Using ProfileLoader when loading user profile
//   static Widget profileScreenExample() {
//     return StatefulWidget(
//       child: _ProfileScreenExample(),
//     );
//   }

//   /// Example 4: Using custom FullPageLoader with custom parameters
//   static Widget customLoaderExample() {
//     return const FullPageLoader(
//       title: 'Custom Loading...',
//       subtitle: 'This is a custom loader with different settings',
//       icon: Icons.settings_rounded,
//       primaryColor: Colors.purple,
//       secondaryColor: Colors.pink,
//     );
//   }

//   /// Example 5: Using DataLoader with custom message
//   static Widget dataLoaderExample() {
//     return const DataLoader(
//       message: 'Syncing your progress with the server...',
//     );
//   }
// }

// class _ChallengeScreenExample extends StatefulWidget {
//   @override
//   State<_ChallengeScreenExample> createState() => _ChallengeScreenExampleState();
// }

// class _ChallengeScreenExampleState extends State<_ChallengeScreenExample> {
//   bool _isLoading = true;

//   @override
//   void initState() {
//     super.initState();
//     _loadChallengeData();
//   }

//   Future<void> _loadChallengeData() async {
//     // Simulate loading challenge data
//     await Future.delayed(const Duration(seconds: 3));
    
//     if (mounted) {
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     if (_isLoading) {
//       return const ChallengeLoader();
//     }

//     return Scaffold(
//       appBar: AppBar(title: const Text('Challenge Screen')),
//       body: const Center(
//         child: Text('Challenge content loaded!'),
//       ),
//     );
//   }
// }

// class _GameScreenExample extends StatefulWidget {
//   @override
//   State<_GameScreenExample> createState() => _GameScreenExampleState();
// }

// class _GameScreenExampleState extends State<_GameScreenExample> {
//   bool _isLoading = true;

//   @override
//   void initState() {
//     super.initState();
//     _startGame();
//   }

//   Future<void> _startGame() async {
//     // Simulate game initialization
//     await Future.delayed(const Duration(seconds: 2));
    
//     if (mounted) {
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     if (_isLoading) {
//       return const GameLoader();
//     }

//     return Scaffold(
//       appBar: AppBar(title: const Text('Game Screen')),
//       body: const Center(
//         child: Text('Game started!'),
//       ),
//     );
//   }
// }

// class _ProfileScreenExample extends StatefulWidget {
//   @override
//   State<_ProfileScreenExample> createState() => _ProfileScreenExampleState();
// }

// class _ProfileScreenExampleState extends State<_ProfileScreenExample> {
//   bool _isLoading = true;

//   @override
//   void initState() {
//     super.initState();
//     _loadProfile();
//   }

//   Future<void> _loadProfile() async {
//     // Simulate loading profile data
//     await Future.delayed(const Duration(seconds: 2));
    
//     if (mounted) {
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     if (_isLoading) {
//       return const ProfileLoader();
//     }

//     return Scaffold(
//       appBar: AppBar(title: const Text('Profile Screen')),
//       body: const Center(
//         child: Text('Profile loaded!'),
//       ),
//     );
//   }
// }

/// Usage patterns for different scenarios:
/// 
/// 1. **Challenge-related screens**: Use `ChallengeLoader()`
///    - Challenge details screen
///    - Challenge start screen
///    - Challenge results screen
/// 
/// 2. **Game-related screens**: Use `GameLoader()`
///    - Game initialization
///    - Level loading
///    - Game start screen
/// 
/// 3. **Profile-related screens**: Use `ProfileLoader()`
///    - User profile screen
///    - Settings screen
///    - Account management
/// 
/// 4. **Leaderboard screens**: Use `LeaderboardLoader()`
///    - Leaderboard screen
///    - Rankings screen
///    - Competition results
/// 
/// 5. **Generic data loading**: Use `DataLoader(message: 'Custom message')`
///    - API calls
///    - File downloads
///    - Data synchronization
/// 
/// 6. **Custom scenarios**: Use `FullPageLoader()` with custom parameters
///    - Custom colors matching specific themes
///    - Custom icons for specific contexts
///    - Custom messages for specific operations
/// 
/// **Implementation Pattern:**
/// ```dart
/// class YourScreen extends StatefulWidget {
///   @override
///   State<YourScreen> createState() => _YourScreenState();
/// }
/// 
/// class _YourScreenState extends State<YourScreen> {
///   bool _isLoading = true;
/// 
///   @override
///   void initState() {
///     super.initState();
///     _loadData();
///   }
/// 
///   Future<void> _loadData() async {
///     // Your loading logic here
///     await Future.delayed(const Duration(seconds: 2));
///     
///     if (mounted) {
///       setState(() {
///         _isLoading = false;
///       });
///     }
///   }
/// 
///   @override
///   Widget build(BuildContext context) {
///     if (_isLoading) {
///       return const ChallengeLoader(); // or any other appropriate loader
///     }
/// 
///     return Scaffold(
///       // Your actual screen content
///     );
///   }
/// }
/// ```
