import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/core/utils/theme_utils.dart';
import 'package:achawach/providers/settings_provider.dart';
import 'package:achawach/screens/settings/change_password.dart';
import 'package:achawach/widgets/cool_app_bar.dart';
import 'package:achawach/widgets/drawer.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: CoolAppBar(
        title: "Settings",
        leading: IconButton(
          icon: const Icon(Icons.menu, color: AppColors.text),
          onPressed: () {
            _scaffoldKey.currentState?.openDrawer();
          },
        ),
      ),
      drawer: const AppDrawer(),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.background,
              AppColors.background.withValues(alpha: 0.8),
              AppColors.primary.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 24),
                  _buildSectionHeader('Sound'),
                  const SizedBox(height: 16),
                  _buildSoundSettings(),
                  const SizedBox(height: 24),
                  _buildSectionHeader('Account and Security'),
                  const SizedBox(height: 16),
                  _buildChangePasswordSection(),
                  const SizedBox(height: 24),
                  _buildSectionHeader('About'),
                  const SizedBox(height: 16),
                  _buildAboutSection(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: ThemeUtils.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 2,
          width: 100,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    );
  }

  Widget _buildSoundSettings() {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return _buildSettingCard(
          icon: Icons.volume_up,
          title: 'Sound Effects',
          subtitle: 'Enable or disable sound effects in the app',
          trailing: Switch(
            value: settings.isSoundEnabled,
            onChanged: (value) {
              settings.setSoundEnabled(value);
              _showSnackBar(
                  value ? 'Sound effects enabled' : 'Sound effects disabled');
            },
            activeColor: AppColors.primary,
          ),
        );
      },
    );
  }

  Widget _buildAboutSection() {
    return _buildSettingCard(
      icon: Icons.info_outline,
      title: 'App Version',
      subtitle: '1.0.0',
      onTap: () {
        _showAboutDialog();
      },
    );
  }

  Widget _buildChangePasswordSection() {
    return _buildSettingCard(
      icon: Icons.lock_outline,
      title: 'Change password',
      subtitle: 'Ensure your account stays protected',
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const ChangePasswordScreen()),
        );
      },
    );
  }

  Widget _buildSettingCard({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: ThemeUtils.getTextColor(context),
                      ),
                    ),
                    Text(
                      subtitle,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: ThemeUtils.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              if (trailing != null) trailing,
            ],
          ),
        ),
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'About Achawach',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: ThemeUtils.getTextColor(context),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Achawach is a trivia challenge app that tests your knowledge across various categories.',
              style: GoogleFonts.poppins(
                color: AppColors.text,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Version: 1.0.0',
              style: GoogleFonts.poppins(
                color: ThemeUtils.getSecondaryTextColor(context),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Close',
              style: GoogleFonts.poppins(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}
