import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/core/utils/font_utils.dart';
import 'package:achawach/models/question.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:audioplayers/audioplayers.dart';

class QuestionsContent extends StatefulWidget {
  final Question question;
  const QuestionsContent({super.key, required this.question});
  @override
  State<QuestionsContent> createState() => _QuestionsContentState();
}

class _QuestionsContentState extends State<QuestionsContent>
    with SingleTickerProviderStateMixin {
  bool _isVideoPlaying = false;
  bool _isAudioPlaying = false;
  VideoPlayerController? _videoController;
  final AudioPlayer _questionAudioPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _initializeMedia();
  }

  Future<void> _initializeMedia() async {
    final question = widget.question;

    // Cleanup previous media
    await _videoController?.dispose();
    _videoController = null;
    await _questionAudioPlayer.stop();

    if (question.type.toLowerCase() == 'video' &&
        question.filePath.isNotEmpty) {
      try {
        _videoController =
            VideoPlayerController.networkUrl(Uri.parse(question.filePath))
              ..initialize().then((_) {
                setState(() {});
              }).catchError((error) {
                // Error initializing video
              });
      } catch (e) {
        // Error creating video controller
      }
    } else if (question.type.toLowerCase() == 'audio' &&
        question.filePath.isNotEmpty) {
      try {
        await _questionAudioPlayer.setSource(UrlSource(question.filePath));
      } catch (e) {
        // Error setting audio source
      }
    }
  }

  @override
  void dispose() {
    _videoController?.dispose();
    _questionAudioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _buildQuestionContent(widget.question, context);
  }

  Widget _buildQuestionContent(Question question, BuildContext context) {
    switch (question.type.toLowerCase()) {
      case 'image':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              question.question,
              style: AppFonts.rubik(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (question.filePath.isNotEmpty)
              Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.25,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: CachedNetworkImage(
                    imageUrl: question.filePath,
                    fit: BoxFit.contain,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[200],
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                    errorWidget: (context, url, error) {
                      // Error loading image
                      return Container(
                        color: Colors.grey[200],
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error,
                                size: 50, color: Colors.red),
                            const SizedBox(height: 8),
                            Text(
                              'Failed to load image\n$url',
                              style: TextStyle(color: Colors.red[700]),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
          ],
        );

      case 'video':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              question.question,
              style: AppFonts.rubik(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (question.filePath.isNotEmpty && _videoController != null)
              Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.25,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      AspectRatio(
                        aspectRatio: _videoController!.value.aspectRatio,
                        child: VideoPlayer(_videoController!),
                      ),
                      if (_videoController!.value.isInitialized)
                        Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.black.withValues(alpha: 0.5),
                          ),
                          child: IconButton(
                            iconSize: 32,
                            icon: Icon(
                              _isVideoPlaying ? Icons.pause : Icons.play_arrow,
                              color: Colors.white,
                            ),
                            onPressed: () {
                              setState(() {
                                _isVideoPlaying = !_isVideoPlaying;
                                _isVideoPlaying
                                    ? _videoController!.play()
                                    : _videoController!.pause();
                              });
                            },
                          ),
                        ),
                    ],
                  ),
                ),
              )
            else if (question.filePath.isNotEmpty)
              Container(
                height: 200,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        );

      case 'audio':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              question.question,
              style: AppFonts.rubik(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (question.filePath.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      _isAudioPlaying ? Icons.graphic_eq : Icons.audiotrack,
                      size: 48,
                      color: AppColors.primary,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () async {
                        if (_isAudioPlaying) {
                          await _questionAudioPlayer.pause();
                        } else {
                          await _questionAudioPlayer.resume();
                        }
                        setState(() {
                          _isAudioPlaying = !_isAudioPlaying;
                        });
                      },
                      icon: Icon(
                          _isAudioPlaying ? Icons.pause : Icons.play_arrow),
                      label:
                          Text(_isAudioPlaying ? 'Pause Audio' : 'Play Audio'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        );

      case 'text':
      default:
        return Text(
          question.question,
          style: AppFonts.rubik(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        );
    }
  }
}
