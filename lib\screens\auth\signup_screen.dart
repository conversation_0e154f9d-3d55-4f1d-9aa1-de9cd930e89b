import 'package:flutter/material.dart';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/services/api_service.dart';
import 'package:achawach/screens/home/<USER>';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'dart:math' as math;
import 'dart:io';
import 'package:image_picker/image_picker.dart';

class SignupScreen extends StatefulWidget {
  const SignupScreen({super.key});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _apiService = ApiService();
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  String? _errorMessage;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Progress indicator variables
  double _signupProgress = 0.0;
  int _currentStep = 0;
  final int _totalSteps = 4;

  // Phone number variables
  final TextEditingController _phoneController = TextEditingController();
  String initialCountry = 'ET';
  PhoneNumber number = PhoneNumber(isoCode: 'ET');
  String? _phoneNumber;
  bool _isPhoneNumberValid = false; // ADDED: To track phone validity

  // Game elements
  final List<String> _avatarChoices = [
    '🦊',
    '🐯',
    '🦁',
    '🐼',
    '🐨',
    '🐮',
    '🐷'
  ];
  String? _selectedAvatar;
  bool _showConfetti = false;

  // Photo upload
  File? _profileImage;
  final ImagePicker _picker = ImagePicker();

  // Category selection
  List<Map<String, dynamic>> _categories = [];
  final List<String> _selectedCategories = [];
  bool _categoriesLoading = true; // Add loading state for categories

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _selectedAvatar = _avatarChoices[0];
    _loadCategories(); // Load categories from API on init

    _fullNameController.addListener(() {
      setState(() {});
    });
    _passwordController.addListener(() {
      setState(() {});
    });
    _confirmPasswordController.addListener(() {
      setState(() {});
    });
    _phoneController.addListener(() {
      setState(() {});
    });
  }

  Future<void> _loadCategories() async {
    setState(() {
      _categoriesLoading = true;
      _errorMessage = null;
    });
    try {
      final categoriesData = await _apiService.fetchCategories();
      setState(() {
        _categories = categoriesData.map((category) {
          return {...category, 'selected': false};
        }).toList();
        _categoriesLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load categories: $e';
        _categoriesLoading = false;
      });
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'Icons.science':
        return Icons.science;
      case 'Icons.history':
        return Icons.history;
      case 'Icons.sports_soccer':
        return Icons.sports_soccer;
      case 'Icons.computer':
        return Icons.computer;
      case 'Icons.palette':
        return Icons.palette;
      case 'Icons.public':
        return Icons.public;
      case 'Icons.music_note':
        return Icons.music_note;
      case 'Icons.movie':
        return Icons.movie;
      case 'Icons.restaurant':
        return Icons.restaurant;
      default:
        return Icons.category;
    }
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.35),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    _animationController.forward();
  }

  void _updateProgress() {
    setState(() {
      _signupProgress = (_currentStep + 1) / _totalSteps;
    });
  }

  void _showSuccessConfetti() {
    setState(() {
      _showConfetti = true;
    });
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showConfetti = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        setState(() {
          _profileImage = File(pickedFile.path);
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to pick image: $e';
      });
    }
  }

  void _showImagePicker() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(Icons.photo_library, color: Colors.purple),
                title: const Text('Photo Library'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera, color: Colors.purple),
                title: const Text('Camera'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.camera);
                },
              ),
              if (_profileImage != null)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text('Remove Photo'),
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _profileImage = null;
                    });
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  void _toggleCategory(String categoryId) {
    final index =
        _categories.indexWhere((cat) => cat['id'].toString() == categoryId);
    if (index != -1) {
      setState(() {
        _categories[index]['selected'] = !_categories[index]['selected'];

        if (_categories[index]['selected']) {
          _selectedCategories.add(categoryId);
        } else {
          _selectedCategories.remove(categoryId);
        }

        if (_errorMessage != null) {
          _errorMessage = null;
        }
      });
    }
  }

  void _validateCurrentStep() {
    bool isValid = false;

    switch (_currentStep) {
      case 0: // Avatar/Photo selection
        isValid = _selectedAvatar != null || _profileImage != null;
        break;
      case 1: // Full name and categories
        isValid = _fullNameController.text.isNotEmpty &&
            _selectedCategories.isNotEmpty;
        break;
      case 2: // Phone number
        // FIX: Use the validation flag from the package instead of a manual length check
        isValid = _isPhoneNumberValid;
        break;
      case 3: // Passwords
        isValid = _passwordController.text.isNotEmpty &&
            _passwordController.text.length >= 6 &&
            _confirmPasswordController.text == _passwordController.text;
        break;
    }

    if (isValid) {
      if (_currentStep < _totalSteps - 1) {
        setState(() {
          _errorMessage = null;
          _currentStep++;
          _updateProgress();
          _animationController.reset();
          _animationController.forward();
        });
      } else {
        _signup();
      }
    } else {
      // Show appropriate validation error
      switch (_currentStep) {
        case 0:
          setState(() {
            _errorMessage = 'Please select an avatar or upload a photo';
          });
          break;
        case 1:
          if (_fullNameController.text.isEmpty) {
            setState(() {
              _errorMessage = 'Please enter your full name';
            });
          } else {
            setState(() {
              _errorMessage = 'Please select at least one category of interest';
            });
          }
          break;
        case 2:
          // FIX: Updated error message to be more generic
          setState(() {
            _errorMessage = 'Please enter a valid phone number';
          });
          break;
        case 3:
          if (_passwordController.text.isEmpty) {
            setState(() {
              _errorMessage = 'Please enter a password';
            });
          } else if (_passwordController.text.length < 6) {
            setState(() {
              _errorMessage = 'Password must be at least 6 characters';
            });
          } else {
            setState(() {
              _errorMessage = 'Passwords do not match';
            });
          }
          break;
      }
    }
  }

  Future<void> _signup() async {
    if (!_formKey.currentState!.validate() || _phoneNumber == null) {
      setState(() {
        _errorMessage = 'Please check your information';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _apiService.register(
        _fullNameController.text.trim(),
        _phoneNumber!,
        _passwordController.text,
        profileImage: _profileImage?.path,
        avatar: _selectedAvatar,
        categories: _selectedCategories,
      );

      _showSuccessConfetti();

      if (mounted) {
        Future.delayed(const Duration(seconds: 1), () {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        });
      }
    } catch (e) {
      String message = e.toString();
      if (message.startsWith('Exception: ')) {
        message = message.replaceFirst('Exception: ', '');
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.red),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  bool get _isNextButtonEnabled {
    switch (_currentStep) {
      case 0: // Avatar/Photo selection
        return _selectedAvatar != null || _profileImage != null;
      case 1: // Full name and categories
        return _fullNameController.text.isNotEmpty &&
            _selectedCategories.isNotEmpty;
      case 2: // Phone number
        // FIX: Use the validation flag from the package
        return _isPhoneNumberValid;
      case 3: // Passwords
        return _passwordController.text.isNotEmpty &&
            _passwordController.text.length >= 6 &&
            _confirmPasswordController.text == _passwordController.text;
      default:
        return false;
    }
  }

  Widget _buildAvatarAndPhotoSelection() {
    return Column(
      children: [
        const Text(
          'Choose Your Profile',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: AppColors.text,
          ),
        ),
        const SizedBox(height: 24),
        GestureDetector(
          onTap: _showImagePicker,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
              image: _profileImage != null
                  ? DecorationImage(
                      image: FileImage(_profileImage!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: _profileImage == null
                ? const Icon(
                    Icons.add_a_photo,
                    size: 40,
                    color: AppColors.primary,
                  )
                : Stack(
                    alignment: Alignment.bottomRight,
                    children: [
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: AppColors.primary,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.edit,
                            size: 20,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        ),
        const SizedBox(height: 20),
        Text(
          'Or choose an avatar',
          style: TextStyle(
            fontSize: 16,
            color: AppColors.text.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 16,
          runSpacing: 16,
          alignment: WrapAlignment.center,
          children: _avatarChoices.map((avatar) {
            bool isSelected = avatar == _selectedAvatar;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedAvatar = avatar;
                  if (_errorMessage != null) {
                    _errorMessage = null;
                  }
                });
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: 70,
                height: 70,
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(35),
                  border: Border.all(
                    color:
                        isSelected ? AppColors.primary : Colors.grey.shade300,
                    width: isSelected ? 3 : 1,
                  ),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.3),
                            blurRadius: 10,
                            spreadRadius: 2,
                          )
                        ]
                      : null,
                ),
                child: Center(
                  child: Text(
                    avatar,
                    style: const TextStyle(
                      fontSize: 36,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildNameAndCategorySelection() {
    return Column(
      children: [
        const Text(
          'What\'s Your Name?',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: AppColors.text,
          ),
        ),
        const SizedBox(height: 24),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: TextFormField(
            controller: _fullNameController,
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: 'Your Full Name',
              hintStyle: TextStyle(color: Colors.grey[400]),
              prefixIcon: const Icon(
                Icons.person_outline,
                color: AppColors.secondary,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your full name';
              }
              return null;
            },
            onChanged: (value) {
              if (_errorMessage != null) {
                setState(() {
                  _errorMessage = null;
                });
              }
            },
          ),
        ),
        const SizedBox(height: 32),
        const Text(
          'Select Your Interests',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: AppColors.text,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Choose categories for your challenge questions',
          style: TextStyle(
            fontSize: 14,
            color: AppColors.text.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        _categoriesLoading
            ? const CircularProgressIndicator()
            : _categories.isEmpty && _errorMessage == null
                ? const Text("No categories available.")
                : _errorMessage != null
                    ? Text(_errorMessage!)
                    : GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          childAspectRatio: 0.9,
                        ),
                        itemCount: _categories.length,
                        itemBuilder: (context, index) {
                          final category = _categories[index];
                          final bool isSelected = category['selected'];

                          return GestureDetector(
                            onTap: () =>
                                _toggleCategory(category['id'].toString()),
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? AppColors.primary.withValues(alpha: 0.1)
                                    : Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: isSelected
                                      ? AppColors.primary
                                      : Colors.grey.shade300,
                                  width: isSelected ? 2 : 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.03),
                                    blurRadius: 8,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 50,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? AppColors.primary
                                          : AppColors.primary
                                              .withValues(alpha: 0.1),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      _getIconData(category['icon']),
                                      color: isSelected
                                          ? Colors.white
                                          : AppColors.primary,
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Flexible(
                                    // 👈 prevents text from overflowing
                                    child: Text(
                                      category['name'],
                                      textAlign: TextAlign.center,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        color: AppColors.text,
                                        fontWeight: isSelected
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                        fontSize: 13,
                                      ),
                                    ),
                                  ),
                                  if (isSelected)
                                    const Padding(
                                      padding: EdgeInsets.only(top: 4),
                                      child: Icon(
                                        Icons.check_circle,
                                        color: AppColors.primary,
                                        size: 18,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
      ],
    );
  }

  Widget _buildPhoneInput() {
    return _buildPhoneInputWidget();
  }

  Widget _buildPasswordInput() {
    return _buildPasswordInputWidget();
  }

  Widget _buildPhoneInputWidget() {
    return Column(
      children: [
        const Text(
          'Your Phone Number',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: AppColors.text,
          ),
        ),
        const SizedBox(height: 24),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
          child: InternationalPhoneNumberInput(
            onInputChanged: (PhoneNumber number) {
              setState(() {
                _phoneNumber = number.phoneNumber;
                if (_errorMessage != null) {
                  _errorMessage = null;
                }
              });
            },
            // FIX: This callback tells us if the number is valid for the selected country
            onInputValidated: (bool value) {
              setState(() {
                _isPhoneNumberValid = value;
              });
            },
            selectorConfig: const SelectorConfig(
              selectorType: PhoneInputSelectorType.DROPDOWN,
            ),
            ignoreBlank: false,
            autoValidateMode: AutovalidateMode.disabled,
            selectorTextStyle: const TextStyle(color: AppColors.text),
            initialValue: number,
            textFieldController: _phoneController,
            formatInput: true,
            keyboardType: const TextInputType.numberWithOptions(
              signed: true,
              decimal: true,
            ),
            inputDecoration: InputDecoration(
              border: InputBorder.none,
              hintText: 'Phone Number',
              hintStyle: TextStyle(color: Colors.grey[400]),
              contentPadding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
      ],
    );
  }

  // Widget _buildPasswordInputWidget() {
  //   return Column(
  //     children: [
  //       const Text(
  //         'Create Password',
  //         style: TextStyle(
  //           fontSize: 22,
  //           fontWeight: FontWeight.bold,
  //           color: AppColors.text,
  //         ),
  //       ),
  //       const SizedBox(height: 24),
  //       Container(
  //         margin: const EdgeInsets.only(bottom: 16),
  //         decoration: BoxDecoration(
  //           color: Colors.white,
  //           borderRadius: BorderRadius.circular(20),
  //           boxShadow: [
  //             BoxShadow(
  //               color: Colors.black.withValues(alpha: 0.05),
  //               blurRadius: 15,
  //               offset: const Offset(0, 5),
  //             ),
  //           ],
  //         ),
  //         child: TextFormField(
  //           controller: _passwordController,
  //           obscureText: _obscurePassword,
  //           decoration: InputDecoration(
  //             border: InputBorder.none,
  //             hintText: 'Password (min. 6 characters)',
  //             hintStyle: TextStyle(color: Colors.grey[400]),
  //             prefixIcon: const Icon(
  //               Icons.lock_outline,
  //               color: AppColors.secondary,
  //             ),
  //             suffixIcon: IconButton(
  //               icon: Icon(
  //                 _obscurePassword ? Icons.visibility_off : Icons.visibility,
  //                 color: Colors.grey,
  //               ),
  //               onPressed: () {
  //                 setState(() {
  //                   _obscurePassword = !_obscurePassword;
  //                 });
  //               },
  //             ),
  //             contentPadding: const EdgeInsets.symmetric(
  //               horizontal: 16,
  //               vertical: 16,
  //             ),
  //           ),
  //           validator: (value) {
  //             if (value == null || value.isEmpty) {
  //               return 'Please enter a password';
  //             }
  //             if (value.length < 6) {
  //               return 'Password must be at least 6 characters long';
  //             }
  //             return null;
  //           },
  //           onChanged: (value) {
  //             if (_errorMessage != null) {
  //               setState(() {
  //                 _errorMessage = null;
  //               });
  //             }
  //           },
  //         ),
  //       ),
  //       Container(
  //         decoration: BoxDecoration(
  //           color: Colors.white,
  //           borderRadius: BorderRadius.circular(20),
  //           boxShadow: [
  //             BoxShadow(
  //               color: Colors.black.withValues(alpha: 0.05),
  //               blurRadius: 15,
  //               offset: const Offset(0, 5),
  //             ),
  //           ],
  //         ),
  //         child: TextFormField(
  //           controller: _confirmPasswordController,
  //           obscureText: _obscureConfirmPassword,
  //           decoration: InputDecoration(
  //             border: InputBorder.none,
  //             hintText: 'Confirm Password',
  //             hintStyle: TextStyle(color: Colors.grey[400]),
  //             prefixIcon: const Icon(
  //               Icons.lock_outline,
  //               color: AppColors.secondary,
  //             ),
  //             suffixIcon: IconButton(
  //               icon: Icon(
  //                 _obscureConfirmPassword
  //                     ? Icons.visibility_off
  //                     : Icons.visibility,
  //                 color: Colors.grey,
  //               ),
  //               onPressed: () {
  //                 setState(() {
  //                   _obscureConfirmPassword = !_obscureConfirmPassword;
  //                 });
  //               },
  //             ),
  //             contentPadding: const EdgeInsets.symmetric(
  //               horizontal: 16,
  //               vertical: 16,
  //             ),
  //           ),
  //           validator: (value) {
  //             if (value == null || value.isEmpty) {
  //               return 'Please confirm your password';
  //             }
  //             if (value != _passwordController.text) {
  //               return 'Passwords do not match';
  //             }
  //             return null;
  //           },
  //           onChanged: (value) {
  //             if (_errorMessage != null) {
  //               setState(() {
  //                 _errorMessage = null;
  //               });
  //             }
  //           },
  //         ),
  //       ),
  //     ],
  //   );
  // }
  Widget _buildPasswordInputWidget() {
    return Column(
      children: [
        const Text(
          'Create Password',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: AppColors.text,
          ),
        ),
        const SizedBox(height: 24),

        // --- PASSWORD INPUT ---
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: TextFormField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: 'Password',
              hintStyle: TextStyle(color: Colors.grey[400]),
              prefixIcon: const Icon(
                Icons.lock_outline,
                color: AppColors.secondary,
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword ? Icons.visibility_off : Icons.visibility,
                  color: Colors.grey,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a password';
              }

              // Strong password RegExp
              final passwordRegex = RegExp(
                r'^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#\$&*~]).{8,}$',
              );

              if (!passwordRegex.hasMatch(value)) {
                return 'Password must be at least 8 chars,\ninclude upper, lower, number & special char';
              }

              return null;
            },
            onChanged: (value) {
              if (_errorMessage != null) {
                setState(() {
                  _errorMessage = null;
                });
              }
            },
          ),
        ),

        // --- CONFIRM PASSWORD INPUT ---
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: TextFormField(
            controller: _confirmPasswordController,
            obscureText: _obscureConfirmPassword,
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: 'Confirm Password',
              hintStyle: TextStyle(color: Colors.grey[400]),
              prefixIcon: const Icon(
                Icons.lock_outline,
                color: AppColors.secondary,
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscureConfirmPassword
                      ? Icons.visibility_off
                      : Icons.visibility,
                  color: Colors.grey,
                ),
                onPressed: () {
                  setState(() {
                    _obscureConfirmPassword = !_obscureConfirmPassword;
                  });
                },
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please confirm your password';
              }
              if (value != _passwordController.text) {
                return 'Passwords do not match';
              }
              return null;
            },
            onChanged: (value) {
              if (_errorMessage != null) {
                setState(() {
                  _errorMessage = null;
                });
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildConfetti() {
    return _buildConfettiWidget();
  }

  Widget _buildProgressIndicator() {
    return _buildProgressIndicatorWidget();
  }

  Widget _buildErrorMessage() {
    return _buildErrorMessageWidget();
  }

  Widget _buildConfettiWidget() {
    if (!_showConfetti) return const SizedBox.shrink();

    return IgnorePointer(
      child: Stack(
        children: List.generate(20, (index) {
          final random = math.Random();
          final size = random.nextInt(10) + 5.0;
          final color = [
            Colors.red,
            Colors.blue,
            Colors.green,
            Colors.yellow,
            Colors.purple,
            Colors.orange,
          ][random.nextInt(6)];

          return Positioned(
            left: random.nextDouble() * MediaQuery.of(context).size.width,
            top: random.nextDouble() * MediaQuery.of(context).size.height * 0.5,
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0, end: 1),
              duration: Duration(seconds: 1 + random.nextInt(2)),
              builder: (context, value, child) {
                return Transform.translate(
                  offset: Offset(
                    0,
                    value * 200 + (random.nextDouble() * 50),
                  ),
                  child: Opacity(
                    opacity: 1 - value,
                    child: Container(
                      width: size,
                      height: size,
                      decoration: BoxDecoration(
                        color: color,
                        shape: random.nextBool()
                            ? BoxShape.circle
                            : BoxShape.rectangle,
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        }),
      ),
    );
  }

  Widget _buildProgressIndicatorWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: LinearProgressIndicator(
              value: _signupProgress,
              backgroundColor: Colors.grey[200],
              valueColor:
                  const AlwaysStoppedAnimation<Color>(AppColors.primary),
              minHeight: 8,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Step ${_currentStep + 1} of $_totalSteps',
            style: const TextStyle(
              color: AppColors.secondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorMessageWidget() {
    if (_errorMessage == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[700]),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: Colors.red[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget currentStepWidget;

    switch (_currentStep) {
      case 0:
        currentStepWidget = _buildAvatarAndPhotoSelection();
        break;
      case 1:
        currentStepWidget = _buildNameAndCategorySelection();
        break;
      case 2:
        currentStepWidget = _buildPhoneInput();
        break;
      case 3:
        currentStepWidget = _buildPasswordInput();
        break;
      default:
        currentStepWidget = Container();
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: _currentStep > 0
            ? IconButton(
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.text,
                ),
                onPressed: () {
                  setState(() {
                    if (_currentStep > 0) {
                      _errorMessage = null;
                      _currentStep--;
                      _updateProgress();
                      _animationController.reset();
                      _animationController.forward();
                    }
                  });
                },
              )
            : null,
        title: const Text(
          'Sign Up',
          style: TextStyle(
            color: AppColors.text,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Stack(
        children: [
          Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              children: [
                const SizedBox(height: 16),
                _buildProgressIndicator(),
                const SizedBox(height: 24),
                // Main content
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Column(
                        children: [
                          // Error message if any
                          _buildErrorMessage(),
                          // Current step content with animations
                          SlideTransition(
                            position: _slideAnimation,
                            child: FadeTransition(
                              opacity: _fadeAnimation,
                              child: currentStepWidget,
                            ),
                          ),
                          const SizedBox(height: 40),
                        ],
                      ),
                    ),
                  ),
                ),
                // Bottom button
                SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading || !_isNextButtonEnabled
                            ? null
                            : _validateCurrentStep,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          disabledBackgroundColor:
                              AppColors.primary.withValues(alpha: 0.5),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 32, vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                          elevation: 5,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 24,
                                width: 24,
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                  strokeWidth: 3,
                                ),
                              )
                            : Text(
                                _currentStep < _totalSteps - 1
                                    ? 'Next'
                                    : 'Sign Up',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (_showConfetti) _buildConfetti(),
        ],
      ),
    );
  }
}
