import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:achawach/widgets/game_end_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Simple mock classes for testing
class MockSocketService {
  final StreamController<Map<String, dynamic>> gameEndedController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> winnerAnnouncementController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  String? endGameToken;
  String? endGameChallengeId;
  bool isConnected = false;
  
  Stream<Map<String, dynamic>> get gameEndedStream => gameEndedController.stream;
  Stream<Map<String, dynamic>> get winnerAnnouncementStream => winnerAnnouncementController.stream;
  
  void endGame(String token, String challengeId) {
    endGameToken = token;
    endGameChallengeId = challengeId;
  }
  
  void dispose() {
    gameEndedController.close();
    winnerAnnouncementController.close();
  }
}

// Simple mock for GameEndService
class MockGameEndService {
  final MockSocketService socketService;
  bool gameEndScreenShown = false;
  Map<String, dynamic>? lastGameEndData;
  bool? lastIsWinnerAnnouncement;
  
  MockGameEndService(this.socketService);
  
  void showGameEndScreen(BuildContext context, Map<String, dynamic> data, {bool isWinnerAnnouncement = false}) {
    gameEndScreenShown = true;
    lastGameEndData = data;
    lastIsWinnerAnnouncement = isWinnerAnnouncement;
    
    // Navigate to a GameEndScreen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => GameEndScreen(
          isWinner: isWinnerAnnouncement ? false : (data['isWinner'] ?? false),
          message: data['message'] ?? 'Game has ended!',
          finalState: data['finalState'] is Map ? Map<String, dynamic>.from(data['finalState']) : {},
          winnerMessage: data['winnerMessage'],
          loserMessage: data['loserMessage'],
          winnerId: data['winnerId'],
          challengeId: data['challengeId'],
          winnerScore: data['winnerScore'],
          winnerRank: data['winnerRank'],
          winnerLevel: data['winnerLevel'],
          onContinue: () {
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }
  
  Future<void> endGame(int challengeId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? prefs.getString('token');
      
      if (token == null || token.isEmpty) {
       
        return;
      }
      
      socketService.endGame(token, challengeId.toString());
    } catch (e) {
      print('MockGameEndService: Error ending game: $e');
    }
  }
}

// A simple widget that sets up game end listeners
class GameEndListenerWidget extends StatefulWidget {
  final MockSocketService socketService;
  final MockGameEndService gameEndService;

  const GameEndListenerWidget({
    super.key,
    required this.socketService,
    required this.gameEndService,
  });

  @override
  State<GameEndListenerWidget> createState() => _GameEndListenerWidgetState();
}

class _GameEndListenerWidgetState extends State<GameEndListenerWidget> {
  StreamSubscription? _gameEndedSubscription;
  StreamSubscription? _winnerAnnouncementSubscription;

  @override
  void initState() {
    super.initState();
    _setupGameEndListeners();
  }

  void _setupGameEndListeners() {
    // Cancel any existing subscriptions
    _gameEndedSubscription?.cancel();
    _winnerAnnouncementSubscription?.cancel();

    // Listen for game ended events
    _gameEndedSubscription = widget.socketService.gameEndedStream.listen((data) {
      
      if (mounted) {
        // Show the game end screen
        widget.gameEndService.showGameEndScreen(context, data);
      }
    });

    // Listen for winner announcement events
    _winnerAnnouncementSubscription =
        widget.socketService.winnerAnnouncementStream.listen((data) {
     
      if (mounted) {
        // Show the winner announcement screen
        widget.gameEndService.showGameEndScreen(context, data,
            isWinnerAnnouncement: true);
      }
    });
  }

  @override
  void dispose() {
    _gameEndedSubscription?.cancel();
    _winnerAnnouncementSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Game End Listener')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Waiting for game end events...'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                widget.gameEndService.endGame(123);
              },
              child: const Text('End Game'),
            ),
          ],
        ),
      ),
    );
  }
}

void main() {
  group('Game End Integration Tests', () {
    late MockSocketService mockSocketService;
    late MockGameEndService mockGameEndService;

    setUp(() {
      // Create mock services
      mockSocketService = MockSocketService();
      mockGameEndService = MockGameEndService(mockSocketService);
      
      // Setup SharedPreferences mock
      SharedPreferences.setMockInitialValues({'auth_token': 'test-token'});
    });

    tearDown(() {
      // Clean up
      mockSocketService.dispose();
    });

    testWidgets('GameEndListenerWidget shows game end screen when gameEnded event is received',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: GameEndListenerWidget(
            socketService: mockSocketService,
            gameEndService: mockGameEndService,
          ),
        ),
      );
      
      // Verify that the listener widget is displayed
      expect(find.text('Waiting for game end events...'), findsOneWidget);
      
      // Create test data for a winner
      final winnerData = {
        'isWinner': true,
        'message': 'You won!',
        'finalState': {'score': 500, 'level': 3},
        'winnerMessage': 'Congratulations!',
        'loserMessage': 'Better luck next time!',
        'winnerId': 123,
        'challengeId': 456,
        'winnerScore': 500,
        'winnerRank': 1,
        'winnerLevel': 3,
      };
      
      // Add data to the stream to simulate receiving a gameEnded event
      mockSocketService.gameEndedController.add(winnerData);
      
      // Wait for the event to be processed
      await tester.pump(const Duration(milliseconds: 500));
      
      // Verify that the game end service was called with the correct data
      expect(mockGameEndService.gameEndScreenShown, isTrue);
      expect(mockGameEndService.lastGameEndData, equals(winnerData));
      expect(mockGameEndService.lastIsWinnerAnnouncement, isFalse);
    });

    testWidgets('GameEndListenerWidget shows winner announcement screen when winnerAnnouncement event is received',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: GameEndListenerWidget(
            socketService: mockSocketService,
            gameEndService: mockGameEndService,
          ),
        ),
      );
      
      // Verify that the listener widget is displayed
      expect(find.text('Waiting for game end events...'), findsOneWidget);
      
      // Create test data for a winner announcement
      final winnerAnnouncementData = {
        'message': 'User 123 won the challenge!',
        'finalState': {'score': 500, 'level': 3},
        'winnerMessage': 'Congratulations!',
        'loserMessage': 'Better luck next time!',
        'winnerId': 123,
        'challengeId': 456,
        'winnerScore': 500,
        'winnerRank': 1,
        'winnerLevel': 3,
      };
      
      // Add data to the stream to simulate receiving a winnerAnnouncement event
      mockSocketService.winnerAnnouncementController.add(winnerAnnouncementData);
      
      // Wait for the event to be processed
      await tester.pump(const Duration(milliseconds: 500));
      
      // Verify that the game end service was called with the correct data
      expect(mockGameEndService.gameEndScreenShown, isTrue);
      expect(mockGameEndService.lastGameEndData, equals(winnerAnnouncementData));
      expect(mockGameEndService.lastIsWinnerAnnouncement, isTrue);
    });

    testWidgets('endGame button calls socket service with correct parameters',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: GameEndListenerWidget(
            socketService: mockSocketService,
            gameEndService: mockGameEndService,
          ),
        ),
      );
      
      // Tap the End Game button
      await tester.tap(find.text('End Game'));
      await tester.pump();
      
      // Verify that endGame was called on the socket service with correct parameters
      expect(mockSocketService.endGameToken, equals('test-token'));
      expect(mockSocketService.endGameChallengeId, equals('123'));
    });
  });
}
