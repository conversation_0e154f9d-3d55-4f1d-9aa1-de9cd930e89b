import 'package:flutter/material.dart';
import '../models/category.dart';
import '../models/challenge.dart';
import '../models/question.dart';
import '../services/api_service.dart';

class ChallengeProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();

  List<Category> _categories = [];
  List<Challenge> _challenges = [];
  List<Challenge> _startedChallenges = [];
  List<Challenge> _notStartedChallenges = [];
  List<Question> _questions = [];

  Map<String, dynamic>? _questionsSummary;
  Map<String, dynamic>? _challengeLife;
  bool _isLoading = false;
  String? _error;

  List<Category> get categories => _categories;
  List<Challenge> get challenges => _challenges;
  List<Challenge> get startedChallenges => _startedChallenges;
  List<Challenge> get notStartedChallenges => _notStartedChallenges;
  List<Question> get questions => _questions;

  Map<String, dynamic>? get questionsSummary => _questionsSummary;
  Map<String, dynamic>? get challengeLife => _challengeLife;
  bool get isLoading => _isLoading;
  String? get error => _error;
  void prepareForRefresh() {
  // Clear the lists that the UI is displaying
  _startedChallenges = [];
  _notStartedChallenges = [];
  _error = null;

  // Set loading to true and notify the UI to show the spinner
  _isLoading = true;
  notifyListeners(); // This is the most important part!
}

  Future<void> fetchCategories() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      _categories = await _apiService.getCategories();
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> fetchChallenges() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      _challenges = await _apiService.getChallenges();
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void stopLoading() {
  _isLoading = false;
  notifyListeners();
}

  // Future<void> fetchStartedChallenges(int categoryId) async {
  //   try {
  //     _isLoading = true;
  //     _error = null;
  //     notifyListeners();

  //     final startedChallengesData =
  //         await _apiService.getStartedChallenges(categoryId);
  //     _startedChallenges = startedChallengesData
  //         .map((data) => Challenge.fromJson(data))
  //         .toList();
  //   } catch (e) {
  //     _error = e.toString();
  //   } finally {
  //     _isLoading = false;
  //     notifyListeners();
  //   }
  // }

  // Future<void> fetchNotStartedChallenges(int categoryId) async {
  //   try {
  //     _isLoading = true;
  //     _error = null;
  //     // notifyListeners();

  //     _notStartedChallenges =
  //         await _apiService.getNotStartedChallenges(categoryId);
  //   } catch (e) {
  //     _error = e.toString();
  //   } finally {
  //     _isLoading = false;
  //     notifyListeners();
  //   }
  // }

  Future<void> fetchStartedChallenges(int categoryId) async {
  try {
    final startedChallengesData =
        await _apiService.getStartedChallenges(categoryId);
    _startedChallenges = startedChallengesData
        .map((data) => Challenge.fromJson(data))
        .toList();
  } catch (e) {
    _error = e.toString();
    // Re-throw the error so the UI's catch block can handle it
    rethrow;
  }
  // REMOVE notifyListeners() FROM THE finally BLOCK
}


Future<void> fetchNotStartedChallenges(int categoryId) async {
  try {
    _notStartedChallenges =
        await _apiService.getNotStartedChallenges(categoryId);
  } catch (e) {
    _error = e.toString();
    // Re-throw the error
    rethrow;
  }
  // REMOVE notifyListeners() FROM THE finally BLOCK
}

  Future<void> fetchChallengeQuestions(int challengeId) async {
    try {
      _isLoading = true;
      _error = null;
      // notifyListeners();

      final data = await _apiService.getChallengeQuestions(challengeId);
      _questions = (data['questions'] as List<dynamic>)
          .map((q) => Question.fromJson(q))
          .toList();
      _questionsSummary = data['summary'];
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<Map<String, dynamic>> startChallenge(
      int challengeId, int categoryId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final result = await _apiService.startChallenge(challengeId);
      await fetchStartedChallenges(categoryId); // Refresh started challenges
      await fetchNotStartedChallenges(
          categoryId); // Refresh not started challenges
      return result;
    } catch (e) {
      _error = e.toString();
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<Map<String, dynamic>> submitAnswer(
      int challengeId, int questionId, int answerId, int categoryId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final result =
          await _apiService.submitAnswer(challengeId, questionId, answerId);

      // Update started challenges after submitting answer
      await fetchStartedChallenges(categoryId);

      return result;
    } catch (e) {
      _error = e.toString();
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> getChallengeLife(int challengeId) async {
    try {
      _isLoading = true;
      _error = null;
      // notifyListeners();

      _challengeLife = await _apiService.getChallengeLife(challengeId);
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> decreaseChallengeLife(int challengeId, int qId) async {
    try {
      _challengeLife =
          await _apiService.decreaseChallengeLife(challengeId, qId);
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      rethrow;
    }
  }
}
