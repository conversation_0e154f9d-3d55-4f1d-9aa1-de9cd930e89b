import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/services/api_service.dart';
import 'package:flutter/material.dart';


class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final ApiService _apiService = ApiService();

  bool _obscureCurrent = true;
  bool _obscureNew = true;
  bool _obscureConfirm = true;
  bool _isLoading = false; // New loading state variable

  String _passwordStrength = "";

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _updatePassword() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });
      try {
        await _apiService.changePassword(
          _currentPasswordController.text,
          _newPasswordController.text,
        );
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("✅ Password updated successfully!"),
            backgroundColor: AppColors.success,
          ),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Failed to update password: $e"),
            backgroundColor: AppColors.error,
          ),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Simple password strength checker
  void _checkPasswordStrength(String value) {
    if (value.isEmpty) {
      setState(() => _passwordStrength = "");
      return;
    }
    if (value.length < 6) {
      setState(() => _passwordStrength = "Weak");
    } else if (RegExp(r'^(?=.*[A-Z])(?=.*[a-z])(?=.*\d).{8,}$').hasMatch(value)) {
      setState(() => _passwordStrength = "Strong");
    } else {
      setState(() => _passwordStrength = "Medium");
    }
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    required bool obscure,
    required VoidCallback toggle,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 18),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(18),
        border: Border.all(color: AppColors.grey300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues( alpha:0.03),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        obscureText: obscure,
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: hint,
          hintStyle: TextStyle(color: AppColors.grey500, fontSize: 15),
          prefixIcon: CircleAvatar(
            radius: 16,
            backgroundColor: AppColors.primary.withValues( alpha:0.1),
            child: Icon(icon, color: AppColors.primary, size: 18),
          ),
          suffixIcon: IconButton(
            icon: Icon(obscure ? Icons.visibility_off : Icons.visibility, color: AppColors.grey600),
            onPressed: toggle,
          ),
        ),
        validator: validator,
        onChanged: onChanged,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text("Change Password", style: TextStyle(fontWeight: FontWeight.w600)),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: AppColors.text,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Secure your account",
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: AppColors.text),
              ),
              const SizedBox(height: 8),
              Text(
                "Please enter your current password and choose a strong new one.",
                style: TextStyle(color: AppColors.grey600, fontSize: 14),
              ),
              const SizedBox(height: 28),

              // Current Password
              _buildPasswordField(
                controller: _currentPasswordController,
                hint: "Current Password",
                icon: Icons.lock_outline,
                obscure: _obscureCurrent,
                toggle: () => setState(() => _obscureCurrent = !_obscureCurrent),
                validator: (value) =>
                    (value == null || value.isEmpty) ? "Enter your current password" : null,
              ),

              // New Password
              _buildPasswordField(
                controller: _newPasswordController,
                hint: "New Password",
                icon: Icons.lock_reset,
                obscure: _obscureNew,
                toggle: () => setState(() => _obscureNew = !_obscureNew),
                validator: (value) {
                  if (value == null || value.isEmpty) return "Enter a new password";
                  final regex = RegExp(r'^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#\$&*~]).{8,}$');
                  if (!regex.hasMatch(value)) {
                    return "Must be 8+ chars, upper, lower, number & special char";
                  }
                  return null;
                },
                onChanged: _checkPasswordStrength,
              ),

              // Password Strength Indicator
              if (_passwordStrength.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(left: 6, bottom: 20),
                  child: Row(
                    children: [
                      Text(
                        "Strength: $_passwordStrength",
                        style: TextStyle(
                          color: _passwordStrength == "Weak"
                              ? AppColors.error
                              : _passwordStrength == "Medium"
                                  ? AppColors.accent
                                  : AppColors.success,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          height: 5,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: _passwordStrength == "Weak"
                                ? AppColors.error.withValues( alpha:0.4)
                                : _passwordStrength == "Medium"
                                    ? AppColors.accent.withValues( alpha:0.4)
                                    : AppColors.success.withValues( alpha:0.4),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Confirm Password
              _buildPasswordField(
                controller: _confirmPasswordController,
                hint: "Confirm Password",
                icon: Icons.lock,
                obscure: _obscureConfirm,
                toggle: () => setState(() => _obscureConfirm = !_obscureConfirm),
                validator: (value) {
                  if (value == null || value.isEmpty) return "Confirm your password";
                  if (value != _newPasswordController.text) return "Passwords do not match";
                  return null;
                },
              ),

              const SizedBox(height: 20),

              // Update Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _updatePassword,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
                    elevation: 4,
                    shadowColor: AppColors.primary.withValues( alpha: 0.3),
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator(color: AppColors.white)
                      : const Text("Update Password", style: TextStyle(fontSize: 16)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
