import 'package:achawach/route_observer.dart';
import 'package:achawach/screens/challenges/challenge.dart';
import 'package:flutter/material.dart';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/widgets/animated_bottom_bar.dart';
import 'package:achawach/services/api_service.dart';
import 'package:google_fonts/google_fonts.dart';

import 'dart:async';

import 'package:intl/intl.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen>
    with SingleTickerProviderStateMixin, RouteAware {
  final ApiService _apiService = ApiService();
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;
  bool _isLoading = false;
  String? _error;
  List<Map<String, dynamic>> _searchResults = [];

  // Filter states
  int? _selectedCategoryId;
  double _minReward = 0;
  String _sortBy = 'popularity';
  bool _isFilterExpanded = false;
  DateTime? _createdDate;
  DateTime? _startDate;
  DateTime? _endDate;
  String? _participationStatus;

  late AnimationController _filterAnimationController;
  late Animation<double> _filterAnimation;

  @override
  void initState() {
    super.initState();
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _filterAnimation = CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    _searchController.dispose();
    _debounce?.cancel();
    _filterAnimationController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final ModalRoute? route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void didPopNext() {
    super.didPopNext();
    _searchController.clear();
    _searchResults = [];
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query);
    });
  }

  Future<void> _performSearch(String query) async {
    if (query.isEmpty &&
        _selectedCategoryId == null &&
        _minReward == 0 &&
        _createdDate == null &&
        _startDate == null &&
        _endDate == null &&
        _participationStatus == null) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await _apiService.searchChallenges(
        search: query,
        categoryId: _selectedCategoryId,
        maxReward: _minReward.toInt(),
        sortBy: _sortBy,
        createdDate: _createdDate?.toIso8601String(),
        startDate: _startDate?.toIso8601String(),
        endDate: _endDate?.toIso8601String(),
        participationStatus: _participationStatus,
      );

      setState(() {
        _searchResults = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.background,
              AppColors.background.withOpacity(0.8),
              AppColors.primary.withOpacity(0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildSearchHeader(),
              SizeTransition(
                sizeFactor: _filterAnimation,
                child: _buildFilters(),
              ),
              Expanded(
                child: _buildSearchResults(),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar:
          const SafeArea(child: AnimatedBottomBar(currentRoute: '/search')),
      extendBody: true,
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    onChanged: _onSearchChanged,
                    decoration: const InputDecoration(
                      hintText: 'Search challenges...',
                      prefixIcon: Icon(Icons.search, color: Colors.grey),
                      border: InputBorder.none,
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isFilterExpanded = !_isFilterExpanded;
                  });
                  if (_isFilterExpanded) {
                    _filterAnimationController.forward();
                  } else {
                    _filterAnimationController.reverse();
                  }
                },
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _isFilterExpanded ? AppColors.primary : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.tune,
                    color: _isFilterExpanded ? Colors.white : Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withOpacity(0.1),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFilterSection(
            'Maximum Reward',
            Slider(
              value: _minReward,
              min: 0,
              max: 10000,
              divisions: 100,
              label: '${_minReward.round()}',
              activeColor: AppColors.primary,
              inactiveColor: AppColors.primary.withOpacity(0.2),
              onChanged: (value) {
                setState(() {
                  _minReward = value;
                });
              },
              onChangeEnd: (value) {
                _performSearch(_searchController.text);
              },
            ),
          ),
          const SizedBox(height: 12),
          _buildFilterSection(
            'Participation Status',
            Wrap(
              spacing: 8,
              children: ['Not Joined', 'Joined', 'Ended'].map<Widget>((status) {
                final isSelected = _participationStatus == status;
                return FilterChip(
                  label: Text(status),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _participationStatus = selected ? status : null;
                    });
                    _performSearch(_searchController.text);
                  },
                  backgroundColor: Colors.white,
                  selectedColor: AppColors.primary.withOpacity(0.2),
                  checkmarkColor: AppColors.primary,
                  labelStyle: TextStyle(
                    color: isSelected ? AppColors.primary : Colors.grey[700],
                  ),
                );
              }).toList(),
            ),
          ),
          // const SizedBox(height: 12),
          // _buildDateFilter('Created Date', _createdDate, (date) {
          //   setState(() {
          //     _createdDate = date;
          //   });
          //   _performSearch(_searchController.text);
          // }),
          // const SizedBox(height: 12),
          // _buildDateFilter('Start Date', _startDate, (date) {
          //   setState(() {
          //     _startDate = date;
          //   });
          //   _performSearch(_searchController.text);
          // }),
          // const SizedBox(height: 12),
          // _buildDateFilter('End Date', _endDate, (date) {
          //   setState(() {
          //     _endDate = date;
          //   });
          //   _performSearch(_searchController.text);
          // }),
        ],
      ),
    );
  }

  Widget _buildDateFilter(
      String title, DateTime? date, Function(DateTime) onDateChanged) {
    return Row(
      children: [
        Text(title),
        const Spacer(),
        TextButton(
          onPressed: () async {
            final selectedDate = await showDatePicker(
              context: context,
              initialDate: date ?? DateTime.now(),
              firstDate: DateTime(2000),
              lastDate: DateTime(2100),
            );
            if (selectedDate != null) {
              onDateChanged(selectedDate);
            }
          },
          child: Text(
            date != null
                ? DateFormat('yyyy-MM-dd').format(date)
                : 'Select Date',
          ),
        ),
      ],
    );
  }

  Widget _buildFilterSection(String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        content,
      ],
    );
  }

  Widget _buildSearchResults() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _performSearch(_searchController.text),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No challenges found',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final challenge = _searchResults[index];
        return _buildChallengeCard(challenge);
      },
    );
  }

  Widget _buildChallengeCard(Map<String, dynamic> challenge) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            final String participationStatus =
                challenge['participation_status'] ?? 'Not Joined';
            final bool isStarted = participationStatus == 'Joined';
            final bool isEnded = participationStatus == 'Ended';

            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ChallengeScreen(
                  selectedChallenge: {
                    'id': challenge['id'] ?? 0,
                    'name': challenge['name'] ?? '',
                    'winningPoints': challenge['winning_points'] ?? 0,
                    'reward': challenge['reward'] ?? 0,
                    'status': isStarted
                        ? 1
                        : isEnded
                            ? 2
                            : 0,
                    'score': challenge['score'] ?? 0,
                    'categoryId': challenge['category_id'] ?? 0,
                    'level': challenge['level'] ?? 0,
                    'rank': challenge['rank'] ?? 0,
                    'winning_rules': {
                      'level': challenge['winning_rules'] != null
                          ? challenge['winning_rules']['level'] ?? 1
                          : 1,
                      'winning_points': challenge['winning_rules'] != null
                          ? challenge['winning_rules']['winning_points'] ??
                              challenge['winning_points'] ??
                              0
                          : challenge['winning_points'] ?? 0,
                      'rank': challenge['winning_rules'] != null
                          ? challenge['winning_rules']['rank'] ?? 1
                          : 1
                    },
                    'startDate':
                        challenge['start_date'] ?? '2025-04-21T14:09:56.000Z',
                    'endDate':
                        challenge['end_date'] ?? '2025-05-28T13:41:00.000Z',
                  },
                ),
              ),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            challenge['name'] ?? '',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: _isChallengeActive(challenge['end_date'])
                                  ? Colors.green.withOpacity(0.1)
                                  : Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _isChallengeActive(challenge['end_date'])
                                  ? 'Active'
                                  : 'Ended',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: _isChallengeActive(challenge['end_date'])
                                    ? Colors.green
                                    : Colors.red,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '${NumberFormat("#,##0.00", "en_US").format(challenge['reward'])} ETB',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  challenge['description'] ??
                      'Your Efforts Deserve Rewards! Earn points, unlock exclusive bonuses.',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    _buildChallengeInfo(
                      Icons.calendar_today,
                      'Start: ${_formatDate(challenge['start_date'] ?? '2025-04-21T14:09:56.000Z')}',
                      Colors.blue,
                    ),
                    const SizedBox(width: 16),
                    _buildChallengeInfo(
                      Icons.event,
                      'End: ${_formatDate(challenge['end_date'] ?? '2025-05-28T13:41:00.000Z')}',
                      Colors.red,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChallengeInfo(IconData icon, String text, Color color) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          text,
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  String _formatDate(String dateString) {
    try {
      final DateTime date = DateTime.parse(dateString);
      return DateFormat('MMM dd, yyyy').format(date);
    } catch (e) {
      return 'Invalid date';
    }
  }

  bool _isChallengeActive(String? endDateString) {
    if (endDateString == null) return true; // Assume active if no end date

    try {
      final DateTime endDate = DateTime.parse(endDateString);
      final DateTime now = DateTime.now();
      return now.isBefore(endDate);
    } catch (e) {
      return true; // Assume active if date parsing fails
    }
  }
}
