import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Simple widget for testing joined challenges screen functionality
class SimpleJoinedChallengesWidget extends StatelessWidget {
  const SimpleJoinedChallengesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: DefaultTabController(
        length: 2,
        child: Scaffold(
          appBar: AppBar(
            title: const Text('My Challenges'),
            bottom: const TabBar(
              tabs: [
                Tab(text: 'In Progress'),
                Tab(text: 'Ended'),
              ],
            ),
          ),
          body: TabBarView(
            children: [
              // In Progress tab
              _buildInProgressTab(),

              // Ended tab
              _buildEndedTab(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInProgressTab() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        _buildChallengeCard(
          'Test Challenge 1',
          'This is a test challenge',
          100,
          500,
          0.5,
          true,
        ),
        const Si<PERSON><PERSON><PERSON>(height: 16),
        _buildChallengeCard(
          'Test Challenge 2',
          'This is another test challenge',
          200,
          1000,
          0.75,
          true,
        ),
      ],
    );
  }

  Widget _buildEndedTab() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        _buildChallengeCard(
          'Completed Challenge 1',
          'This is a completed challenge',
          150,
          750,
          1.0,
          false,
          completedAt: '2023-06-30',
          rank: 1,
          totalQuestions: 10,
          correctAnswers: 8,
          completionTime: '5m 30s',
        ),
      ],
    );
  }

  Widget _buildChallengeCard(
    String name,
    String description,
    int reward,
    int winningPoints,
    double progress,
    bool isStarted, {
    String? completedAt,
    int? rank,
    int? totalQuestions,
    int? correctAnswers,
    String? completionTime,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              name,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(description),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.emoji_events, color: Colors.amber),
                const SizedBox(width: 8),
                Text('$reward Birr'),
                const Spacer(),
                const Icon(Icons.star, color: Colors.orange),
                const SizedBox(width: 8),
                Text('$winningPoints pts'),
              ],
            ),
            const SizedBox(height: 16),
            if (isStarted) ...[
              Row(
                children: [
                  const Text('Progress:'),
                  const SizedBox(width: 8),
                  Text('${(progress * 100).toInt()}%'),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(value: progress),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {},
                child: const Text('Continue Challenge'),
              ),
            ] else ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Rank'),
                      Text('#$rank'),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Questions'),
                      Text('$totalQuestions'),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Correct'),
                      Text('$correctAnswers'),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text('Completed on $completedAt'),
            ],
          ],
        ),
      ),
    );
  }
}

void main() {
  group('JoinedChallengesScreen Tests', () {
    testWidgets('JoinedChallengesScreen renders correctly',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleJoinedChallengesWidget());

      // Verify app bar title is displayed
      expect(find.text('My Challenges'), findsOneWidget);

      // Verify tabs are displayed
      expect(find.text('In Progress'), findsOneWidget);
      expect(find.text('Ended'), findsOneWidget);

      // Verify in-progress challenges are displayed
      expect(find.text('Test Challenge 1'), findsOneWidget);
      expect(find.text('Test Challenge 2'), findsOneWidget);

      // Verify progress indicators are displayed
      expect(find.byType(LinearProgressIndicator), findsNWidgets(2));
      expect(find.text('50%'), findsOneWidget);
      expect(find.text('75%'), findsOneWidget);

      // Verify continue buttons are displayed
      expect(find.text('Continue Challenge'), findsNWidgets(2));
    });

    testWidgets('JoinedChallengesScreen can switch tabs',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleJoinedChallengesWidget());

      // Initially on the In Progress tab
      expect(find.text('Test Challenge 1'), findsOneWidget);
      expect(find.text('Completed Challenge 1'), findsNothing);

      // Tap on the Ended tab
      await tester.tap(find.text('Ended'));
      await tester.pumpAndSettle();

      // Now on the Ended tab
      expect(find.text('Test Challenge 1'), findsNothing);
      expect(find.text('Completed Challenge 1'), findsOneWidget);

      // Verify completed challenge details are displayed
      expect(find.text('Rank'), findsOneWidget);
      expect(find.text('#1'), findsOneWidget);
      expect(find.text('Questions'), findsOneWidget);
      expect(find.text('10'), findsOneWidget);
      expect(find.text('Correct'), findsOneWidget);
      expect(find.text('8'), findsOneWidget);
      expect(find.text('Completed on 2023-06-30'), findsOneWidget);
    });
  });
}
