import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Simple widget for testing profile screen functionality
class SimpleProfileWidget extends StatelessWidget {
  const SimpleProfileWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Profile'),
          actions: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {},
            ),
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: () {},
            ),
          ],
        ),
        body: const SingleChildScrollView(
          child: Column(
            children: [
              // Profile header
              SizedBox(
                height: 200,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircleAvatar(
                        radius: 50,
                        child: Text('🦊', style: TextStyle(fontSize: 40)),
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Test User',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '+************',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Profile details
              Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Account Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text('Full Name: Test User'),
                    Text('Phone Number: +************'),
                    Text('Lives: 3'),
                    SizedBox(height: 24),
                    Text(
                      'Selected Categories',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text('Sports'),
                    Text('Science'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

void main() {
  group('Profile Screen Tests', () {
    testWidgets('Profile screen renders correctly',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleProfileWidget());

      // Verify app bar title is displayed
      expect(find.text('Profile'), findsOneWidget);

      // Verify profile information is displayed
      expect(find.text('Test User'), findsAtLeastNWidgets(1));
      expect(find.text('+************'), findsAtLeastNWidgets(1));
      expect(find.text('Lives: 3'), findsOneWidget);

      // Verify categories are displayed
      expect(find.text('Selected Categories'), findsOneWidget);
      expect(find.text('Sports'), findsOneWidget);
      expect(find.text('Science'), findsOneWidget);
    });

    testWidgets('Profile screen shows edit and logout buttons',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleProfileWidget());

      // Verify edit button is displayed
      expect(find.byIcon(Icons.edit), findsOneWidget);

      // Verify logout button is displayed
      expect(find.byIcon(Icons.logout), findsOneWidget);
    });

    testWidgets('Profile screen shows account information section',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(const SimpleProfileWidget());

      // Verify account information section is displayed
      expect(find.text('Account Information'), findsOneWidget);
      expect(find.textContaining('Full Name'), findsOneWidget);
      expect(find.textContaining('Phone Number'), findsOneWidget);
    });
  });
}
