import 'dart:async';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/services/media_service.dart';
import 'package:achawach/services/socket_service.dart';
import 'package:achawach/services/game_end_service.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ScoreLifeLevel extends StatefulWidget {
  final int challengeId;
  final bool showScore;
  final bool showLife;
  final bool showLevel;
  final MediaService mediaService;
  final Function(int oldLevel, int newLevel, int bonusPoints,int score)? onLevelUp;
  final Function(Map<String, dynamic> lifeData)? onLifeChange;

  const ScoreLifeLevel(
      {super.key,
      required this.challengeId,
      this.showScore = true,
      this.showLife = true,
      this.showLevel = true,
      this.onLevelUp,
      this.onLifeChange,
      required this.mediaService});

  @override
  State<ScoreLifeLevel> createState() => _ScoreLifeLevelState();
}

class _ScoreLifeLevelState extends State<ScoreLifeLevel> {
  final SocketService _socketService = SocketService.instance;
  final GameEndService _gameEndService = GameEndService.instance;

  StreamSubscription? _gameStateSubscription;
  StreamSubscription? _errorSubscription;
  StreamSubscription? _gameEndedSubscription;

  Map<String, dynamic>? _lifeData;
  int _score = 0;
  int _oldLevel = 1;
  bool _isLoading = false;
  String? _error;
  bool _isGameEnded = false;

  @override
  void initState() {
    super.initState();
    // _loadLifeData();
    _setupSocketConnection();
  }

  @override
  void didUpdateWidget(ScoreLifeLevel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.challengeId != widget.challengeId) {
      // Challenge ID changed, reload data
      // _loadLifeData();
      _setupSocketConnection();
    }
  }

  @override
  void dispose() {
    _gameStateSubscription?.cancel();
    _errorSubscription?.cancel();
    _gameEndedSubscription?.cancel();
    _socketService.disconnect();
    // Don't disconnect the socket here as it might be needed by other components
    // Removing _socketService.disconnect() to prevent premature socket disconnection
    super.dispose();
  }

  Future<void> _setupSocketConnection() async {
    // Cancel existing subscriptions
    _gameStateSubscription?.cancel();
    _errorSubscription?.cancel();
    _gameEndedSubscription?.cancel();
    // Don't disconnect the socket here, let the SocketService handle reconnection
    _socketService.disconnect();

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? prefs.getString('token');

      if (token == null || token.isEmpty) {
        setState(() {
          _error = 'No authentication token found';
        });
        return;
      }

      await _socketService.connect(token, widget.challengeId.toString());

      // Listen for game end events
      _gameEndedSubscription = _socketService.gameEndedStream.listen((data) {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _error = '';
          });

          // Use the GameEndService to show the game end screen
          debugPrint('ScoreLifeLevel: Received game end event: $data');

          // Delay slightly to ensure the widget tree is stable
          _gameEndService.showGameEndScreen(context, data, widget.mediaService);
        }
      });
      _gameStateSubscription = _socketService.gameStateStream.listen((state) {
        if (mounted) {
          setState(() {
            final int previousLevel = _oldLevel;
            _score = state['score'] ?? 0;

            // Debug print to see what's coming from the socket
            print('Received gameState from socket: $state');
            print('Bonus value from socket: ${state['bonus']}');

            // Update life data
            _lifeData = {
              'life': state['life'] ?? 0,
              'max': state['maxLife'] ?? 10,
              'level': state['level'] ?? 1,
              'bonus': state['bonus'] ?? 0,
              'score': state['score'] ?? 0,
              'status': 1,
            };

            // Notify parent about life changes
            if (widget.onLifeChange != null) {
              widget.onLifeChange!(_lifeData!);
            }

            // Check for level up
            final int newLevel = _lifeData?['level'] ?? 1;
            if (newLevel > previousLevel &&
                previousLevel > 0 &&
                widget.onLevelUp != null) {
              // Properly cast and provide default value for bonus points
              final int bonusPoints = _lifeData?['bonus'] is int
                  ? _lifeData!['bonus']
                  : (_lifeData?['bonus'] != null
                      ? int.tryParse(_lifeData!['bonus'].toString()) ?? 0
                      : 0);

              // Immediately trigger the level up callback
              // This ensures the level up screen appears right after the level change
              Future.microtask(() {
                if (mounted) {
                  if (newLevel > previousLevel && widget.onLevelUp != null) {
                    widget.onLevelUp!(previousLevel, newLevel, bonusPoints,
                        _score); // Pass score
                  }
                }
              });
            }

            _oldLevel = newLevel;
          });
        }
      });

      // Listen for errors
      _errorSubscription = _socketService.errorStream.listen((error) {
        if (mounted) {
          setState(() {
            _error = error;
          });
        }
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Score display
        if (widget.showScore)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 20),
                const SizedBox(width: 4),
                Text(
                  _score.toString(),
                  style: GoogleFonts.rubik(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

        if (widget.showScore && (widget.showLife || widget.showLevel))
          const SizedBox(width: 8),

        // Life and Level display
        if (widget.showLife || widget.showLevel)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Life display
                if (widget.showLife) ...[
                  const Icon(Icons.favorite, color: Colors.red, size: 20),
                  const SizedBox(width: 4),
                  Text(
                    _lifeData != null ? '${_lifeData!['life'] ?? 0}' : '0',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],

                // Add spacing between life and level if both are shown
                if (widget.showLife && widget.showLevel)
                  const SizedBox(width: 8),

                // Level display
                if (widget.showLevel)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.star, color: Colors.amber, size: 14),
                        const SizedBox(width: 2),
                        Text(
                          'Lvl ${_lifeData != null ? (_lifeData!['level'] ?? 1) : 1}',
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
      ],
    );
  }
}
