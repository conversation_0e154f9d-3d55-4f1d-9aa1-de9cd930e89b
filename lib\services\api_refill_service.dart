
import 'package:achawach/services/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ApiRefillService {
 final Dio _dio = DioClient.instance;
 
 

  Future<void> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
  }

  /// Checks if the user's lives should be refilled after 24 hours of having 0 lives
  /// Requires the challenge_id to check life status for a specific challenge
  /// Returns a map with the updated life data if successful
  Future<Map<String, dynamic>> checkLife(int challengeId) async {
    try {
      final response = await _dio.post(
        '/check-life',
        data: {'challenge_id': challengeId},
      );

      print('Check Life Response: ${response.data}');

      if (response.statusCode == 200) {
        if (response.data['status'] == 'success') {
          return response.data['data'];
        } else {
          throw Exception(response.data['message'] ?? 'Failed to check life');
        }
      } else if (response.statusCode == 401) {
        await clearToken();
        throw Exception('Unauthorized: Please login again');
      } else {
        throw Exception('Failed to check life: ${response.statusCode}');
      }
    } catch (e) {
      print('Error checking life: $e');
      throw Exception('Error checking life: $e');
    }
  }
}
