Stack trace:
Frame         Function      Args
0007FFFF9AB0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF89B0) msys-2.0.dll+0x2118E
0007FFFF9AB0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9AB0  0002100469F2 (00021028DF99, 0007FFFF9968, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9AB0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9AB0  00021006A545 (0007FFFF9AC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9AC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA388E0000 ntdll.dll
7FFA36F10000 KERNEL32.DLL
7FFA35B90000 KERNELBASE.dll
7FFA37950000 USER32.dll
7FFA35B60000 win32u.dll
7FFA387F0000 GDI32.dll
7FFA36190000 gdi32full.dll
7FFA35F90000 msvcp_win.dll
7FFA36040000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA38270000 advapi32.dll
7FFA378A0000 msvcrt.dll
7FFA37750000 sechost.dll
7FFA38340000 RPCRT4.dll
7FFA35000000 CRYPTBASE.DLL
7FFA365D0000 bcryptPrimitives.dll
7FFA37860000 IMM32.DLL
