// import 'package:achawach/core/constants/app_colors.dart';
// import 'package:achawach/core/utils/credentials_manager.dart';
// import 'package:achawach/core/utils/page_transitions.dart';
// import 'package:achawach/services/api_service.dart';
// import 'package:achawach/routers/app_router.dart';
// import 'package:achawach/screens/home/<USER>';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';

// class LoginScreen extends StatefulWidget {
//   const LoginScreen({Key? key}) : super(key: key);

//   @override
//   _LoginScreenState createState() => _LoginScreenState();
// }

// class _LoginScreenState extends State<LoginScreen>
//     with SingleTickerProviderStateMixin {
//   final _formKey = GlobalKey<FormState>();
//   bool _isPasswordVisible = false;
//   bool _rememberMe = false;
//   bool _isLoading = false;
//   bool _loginSuccessful = false; // Track login success for animation
//   late AnimationController _animationController;
//   late Animation<double> _fadeAnimation;
//   String _selectedCountryCode = '+251'; // Default country code for Ethiopia
//   bool _submitted = false; // Tracks form submission
//   String? _phoneError; // Stores phone number error message
//   final ApiService _apiService = ApiService();

//   final _phoneController = TextEditingController();
//   final _passwordController = TextEditingController();

//   final List<Map<String, String>> _countryCodes = [
//     {'code': '+251', 'name': 'Ethiopia'},
//     {'code': '+254', 'name': 'Kenya'},
//     {'code': '+234', 'name': 'Nigeria'},
//     {'code': '+1', 'name': 'United States'},
//     {'code': '+44', 'name': 'United Kingdom'},
//   ];

//   @override
//   void initState() {
//     super.initState();
//     _animationController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 1200),
//     );
//     _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
//       CurvedAnimation(
//         parent: _animationController,
//         curve: Curves.easeInOut,
//       ),
//     );
//     _animationController.forward();

//     // Load saved credentials if available
//     _loadSavedCredentials();
//   }

//   Future<void> _loadSavedCredentials() async {
//     try {
//       final rememberMe = await CredentialsManager.getRememberMe();
//       final hasCredentials = await CredentialsManager.hasSavedCredentials();

//       if (rememberMe && hasCredentials) {
//         final phoneNumber = await CredentialsManager.getPhoneNumber();
//         final countryCode = await CredentialsManager.getCountryCode();
//         final password = await CredentialsManager.getPassword();

//         if (mounted) {
//           setState(() {
//             _rememberMe = rememberMe;
//             if (phoneNumber != null) _phoneController.text = phoneNumber;
//             if (countryCode != null) _selectedCountryCode = countryCode;
//             if (password != null) _passwordController.text = password;
//           });
//         }
//       }
//     } catch (e) {
//       // Handle any errors silently - just won't load saved credentials
//       // In a production app, use a proper logging framework
//       debugPrint('Error loading saved credentials: $e');
//     }
//   }

//   Future<void> _login() async {
//     // First set submitted to true to trigger validation
//     setState(() {
//       _submitted = true;
//     });

//     // Wait for the next frame to ensure validation has completed
//     await Future.delayed(Duration.zero);

//     // Check if form is valid
//     if (!_formKey.currentState!.validate()) {
//       return; // Don't proceed if validation fails
//     }

//     // Start loading
//     setState(() {
//       _isLoading = true;
//     });

//     final phoneNumber = '$_selectedCountryCode${_phoneController.text}';
//     final password = _passwordController.text;

//     debugPrint('Attempting login with phone: $phoneNumber');

//     try {
//       // Save credentials if remember me is checked
//       await CredentialsManager.saveCredentials(
//         rememberMe: _rememberMe,
//         phoneNumber: _phoneController.text,
//         countryCode: _selectedCountryCode,
//         password: password,
//       );

//       // Attempt to login
//       await _apiService.login(phoneNumber, password);

//       // Show login success animation
//       if (mounted) {
//         setState(() {
//           _loginSuccessful = true;
//         });

//         // Short delay to show success message before navigation
//         await Future.delayed(const Duration(seconds: 2));
//         if (mounted) {
//           setState(() {
//             _loginSuccessful = false;
//           });
//           // Use custom transition animation instead of named route
//           Navigator.of(context).pushReplacement(
//             PageTransitions.scaleFade(
//               page: const HomeScreen(),
//               duration: const Duration(milliseconds: 600),
//             ),
//           );
//         }
//       }
//     } catch (e) {
//       // Show detailed error message
//       if (mounted) {
//         debugPrint('Login error details: $e');
//         String errorMessage = 'Login failed';

//         // Extract more specific error message if available
//         if (e.toString().contains('SocketException') ||
//             e.toString().contains('Connection refused')) {
//           errorMessage =
//               'Cannot connect to server. Please check your internet connection or contact support.';
//         } else if (e.toString().contains('401')) {
//           errorMessage = 'Invalid phone number or password';
//         } else if (e.toString().contains('timeout')) {
//           errorMessage = 'Request timed out. Please try again.';
//         } else {
//           // Use the error message from the server if available
//           errorMessage =
//               'Login failed: ${e.toString().split('Exception:').last.trim()}';
//         }

//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text(errorMessage),
//             backgroundColor: AppColors.error,
//             duration: const Duration(seconds: 3),
//           ),
//         );
//       }
//     } finally {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     }
//   }

//   @override
//   void dispose() {
//     _animationController.dispose();
//     _phoneController.dispose();
//     _passwordController.dispose();
//     // Reset login success state
//     _loginSuccessful = false;
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: AppColors.grey100,
//       body: Stack(
//         children: [
//           SafeArea(
//             child: FadeTransition(
//               opacity: _fadeAnimation,
//               child: SingleChildScrollView(
//                 padding: const EdgeInsets.symmetric(
//                     horizontal: 24.0, vertical: 32.0),
//                 child: Form(
//                   key: _formKey,
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.stretch,
//                     children: [
//                       _buildHeader(),
//                       const SizedBox(height: 48),
//                       _buildPhoneField(),
//                       const SizedBox(height: 16),
//                       _buildPasswordField(),
//                       const SizedBox(height: 24),
//                       _buildRememberMeAndForgotPassword(),
//                       const SizedBox(height: 32),
//                       _buildLoginButton(),
//                       const SizedBox(height: 24),
//                       _buildSignUpLink(),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//           ),

//           // Login success animation
//           if (_loginSuccessful)
//             Positioned(
//               top: 100,
//               left: 0,
//               right: 0,
//               child: TweenAnimationBuilder<double>(
//                 duration: const Duration(milliseconds: 700),
//                 curve: Curves.elasticOut,
//                 tween: Tween(begin: 0.0, end: 1.0),
//                 builder: (context, value, child) {
//                   return Transform.scale(
//                     scale: value,
//                     child: Center(
//                       child: Container(
//                         padding: const EdgeInsets.symmetric(
//                           horizontal: 24,
//                           vertical: 12,
//                         ),
//                         decoration: BoxDecoration(
//                           gradient: LinearGradient(
//                             colors: [
//                               Colors.green[500]!,
//                               Colors.green[700]!,
//                             ],
//                             begin: Alignment.topLeft,
//                             end: Alignment.bottomRight,
//                           ),
//                           borderRadius: BorderRadius.circular(30),
//                           boxShadow: [
//                             BoxShadow(
//                               color: Colors.green.withValues(alpha: 0.3),
//                               blurRadius: 15,
//                               offset: const Offset(0, 8),
//                             ),
//                           ],
//                         ),
//                         child: const Row(
//                           mainAxisSize: MainAxisSize.min,
//                           children: [
//                             Icon(Icons.check_circle,
//                                 color: Colors.white, size: 24),
//                             SizedBox(width: 12),
//                             Text(
//                               'Login Successful!',
//                               style: TextStyle(
//                                 color: Colors.white,
//                                 fontWeight: FontWeight.bold,
//                                 fontSize: 16,
//                                 letterSpacing: 0.5,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                   );
//                 },
//               ),
//             ),
//         ],
//       ),
//     );
//   }

//   Widget _buildHeader() {
//     return Column(
//       children: [

//         Container(
//           padding: const EdgeInsets.all(20),
//           decoration: const BoxDecoration(
//             shape: BoxShape.circle,
//           ),
//           child: Image.asset(
//             'assets/images/icon.png', // replace with your actual image path
//             width: 80,
//             height: 80,
//           ),
//         ),

//         const SizedBox(height: 16),
//         const Text(
//           'Welcome Back',
//           style: TextStyle(
//             fontSize: 32,
//             fontWeight: FontWeight.w700,
//             color: AppColors.grey800,
//           ),
//         ),
//         const SizedBox(height: 8),
//         const Text(
//           'Sign in to your account',
//           style: TextStyle(
//             fontSize: 16,
//             color: AppColors.grey600,
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildPhoneField() {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         // Always reserve space for error message to prevent layout shift
//         SizedBox(
//           height: 20,
//           child: _phoneError != null
//               ? Padding(
//                   padding: const EdgeInsets.only(left: 16, bottom: 4),
//                   child: Text(
//                     _phoneError!,
//                     style:
//                         const TextStyle(color: AppColors.error, fontSize: 12),
//                   ),
//                 )
//               : null,
//         ),
//         TextFormField(
//           controller: _phoneController,
//           keyboardType: TextInputType.phone,
//           inputFormatters: [
//             FilteringTextInputFormatter.digitsOnly,
//             LengthLimitingTextInputFormatter(9),
//           ],
//           autovalidateMode: _submitted
//               ? AutovalidateMode.onUserInteraction
//               : AutovalidateMode.disabled,
//           decoration: InputDecoration(
//             labelText: 'Phone Number',
//             hintText: '9XXXXXXXX',
//             prefixIcon: GestureDetector(
//               onTap: () => _showCountryCodePicker(context),
//               child: Container(
//                 padding: const EdgeInsets.symmetric(horizontal: 8),
//                 margin: const EdgeInsets.only(right: 8),
//                 child: Row(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Icon(
//                       Icons.phone,
//                       color: AppColors.withAlpha(AppColors.primary, 179),
//                       size: 20,
//                     ),
//                     const SizedBox(width: 4),
//                     Text(
//                       _selectedCountryCode,
//                       style: const TextStyle(
//                         fontSize: 16,
//                         color: AppColors.primary,
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                     const Icon(
//                       Icons.arrow_drop_down,
//                       color: AppColors.primary,
//                       size: 20,
//                     ),
//                     Container(
//                       height: 24,
//                       width: 1,
//                       color: AppColors.withAlpha(AppColors.primary, 128),
//                       margin: const EdgeInsets.symmetric(horizontal: 4),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//             filled: true,
//             fillColor: AppColors.white,
//             border: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(12),
//               borderSide: BorderSide(
//                   color: AppColors.withAlpha(AppColors.primary, 128)),
//             ),
//             enabledBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(12),
//               borderSide: BorderSide(
//                 color: _phoneError != null
//                     ? AppColors.error
//                     : AppColors.withAlpha(AppColors.primary, 128),
//               ),
//             ),
//             focusedBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(12),
//               borderSide: BorderSide(
//                 color:
//                     _phoneError != null ? AppColors.error : AppColors.primary,
//                 width: 2,
//               ),
//             ),
//             errorStyle: const TextStyle(height: 0, fontSize: 0),
//           ),
//           validator: (value) {
//             if (value == null || value.isEmpty) {
//               _phoneError = 'Please enter your phone number';
//               return _phoneError;
//             } else if (value.length != 9) {
//               _phoneError = 'Phone number must be 9 digits';
//               return _phoneError;
//             }
//             _phoneError = null;
//             return null;
//           },
//         ),
//       ],
//     );
//   }

//   Widget _buildPasswordField() {
//     return TextFormField(
//       controller: _passwordController,
//       obscureText: !_isPasswordVisible,
//       decoration: InputDecoration(
//         labelText: 'Password',
//         hintText: 'Enter your password',
//         prefixIcon: Icon(
//           Icons.lock_outline,
//           color: AppColors.withAlpha(AppColors.primary, 179),
//         ),
//         suffixIcon: IconButton(
//           icon: Icon(
//             _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
//             color: AppColors.withAlpha(AppColors.primary, 179),
//           ),
//           onPressed: () {
//             setState(() {
//               _isPasswordVisible = !_isPasswordVisible;
//             });
//           },
//         ),
//         filled: true,
//         fillColor: AppColors.white,
//         border: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(12),
//           borderSide:
//               BorderSide(color: AppColors.withAlpha(AppColors.primary, 128)),
//         ),
//         enabledBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(12),
//           borderSide:
//               BorderSide(color: AppColors.withAlpha(AppColors.primary, 128)),
//         ),
//         focusedBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(12),
//           borderSide: const BorderSide(color: AppColors.primary, width: 2),
//         ),
//         errorBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(12),
//           borderSide: const BorderSide(color: AppColors.error, width: 2),
//         ),
//       ),
//       validator: (value) {
//         if (value == null || value.isEmpty) {
//           return 'Please enter your password';
//         }
//         return null;
//       },
//     );
//   }

//   Widget _buildRememberMeAndForgotPassword() {
//     return Row(
//       children: [
//         // Make the entire row clickable for better UX
//         GestureDetector(
//           onTap: () {
//             setState(() {
//               _rememberMe = !_rememberMe;
//             });
//           },
//           child: Row(
//             children: [
//               Checkbox(
//                 value: _rememberMe,
//                 onChanged: (value) {
//                   setState(() {
//                     _rememberMe = value ?? false;
//                   });
//                 },
//                 activeColor: AppColors.primary,
//                 checkColor: AppColors.white,
//                 shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(4),
//                 ),
//               ),
//               const Text(
//                 'Remember me',
//                 style: TextStyle(
//                   color: AppColors.grey700,
//                   fontSize: 14,
//                 ),
//               ),
//             ],
//           ),
//         ),
//         const Spacer(),
//         TextButton(
//           onPressed: () {
//             // Handle forgot password
//             ScaffoldMessenger.of(context).showSnackBar(
//               const SnackBar(
//                 content: Text('Forgot password feature coming soon'),
//                 backgroundColor: AppColors.info,
//                 duration: Duration(seconds: 2),
//               ),
//             );
//           },
//           child: const Text(
//             'Forgot Password?',
//             style: TextStyle(
//               color: AppColors.primary,
//               fontWeight: FontWeight.w600,
//               fontSize: 14,
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildLoginButton() {
//     return ElevatedButton(
//       onPressed: _isLoading ? null : _login,
//       style: ElevatedButton.styleFrom(
//         backgroundColor: AppColors.primary,
//         foregroundColor: AppColors.white,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(12),
//         ),
//         padding: const EdgeInsets.symmetric(vertical: 16),
//         elevation: 4,
//         disabledBackgroundColor: AppColors.withAlpha(AppColors.primary, 128),
//       ),
//       child: _isLoading
//           ? const SizedBox(
//               height: 20,
//               width: 20,
//               child: CircularProgressIndicator(
//                 strokeWidth: 2,
//                 valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
//               ),
//             )
//           : const Text(
//               'Sign In',
//               style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
//             ),
//     );
//   }

//   Widget _buildSignUpLink() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         const Text(
//           "Don't have an account? ",
//           style: TextStyle(
//             color: AppColors.grey600,
//             fontSize: 14,
//           ),
//         ),
//         TextButton(
//           onPressed: () {
//             Navigator.of(context).pushNamed(AppRouter.signupRoute);
//           },
//           child: const Text(
//             'Sign Up',
//             style: TextStyle(
//               color: AppColors.primary,
//               fontWeight: FontWeight.w600,
//               fontSize: 14,
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   void _showCountryCodePicker(BuildContext context) {
//     showModalBottomSheet(
//       context: context,
//       isScrollControlled: true,
//       backgroundColor: Colors.transparent,
//       builder: (context) => DraggableScrollableSheet(
//         initialChildSize: 0.7,
//         maxChildSize: 0.9,
//         minChildSize: 0.5,
//         builder: (context, scrollController) => Container(
//           decoration: BoxDecoration(
//             color: AppColors.grey100,
//             borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
//           ),
//           child: Column(
//             children: [
//               Container(
//                 margin: const EdgeInsets.symmetric(vertical: 12),
//                 width: 40,
//                 height: 4,
//                 decoration: BoxDecoration(
//                   color: AppColors.grey400,
//                   borderRadius: BorderRadius.circular(2),
//                 ),
//               ),
//               Padding(
//                 padding: const EdgeInsets.all(16),
//                 child: const Text(
//                   'Select Country Code',
//                   style: TextStyle(
//                     fontSize: 18,
//                     fontWeight: FontWeight.bold,
//                     color: AppColors.grey800,
//                   ),
//                 ),
//               ),
//               Padding(
//                 padding: const EdgeInsets.symmetric(horizontal: 16),
//                 child: TextField(
//                   decoration: InputDecoration(
//                     hintText: 'Search country',
//                     prefixIcon: Icon(
//                       Icons.search,
//                       color: AppColors.withAlpha(AppColors.primary, 179),
//                     ),
//                     filled: true,
//                     fillColor: AppColors.white,
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(12),
//                       borderSide: BorderSide(
//                           color: AppColors.withAlpha(AppColors.primary, 128)),
//                     ),
//                     enabledBorder: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(12),
//                       borderSide: BorderSide(
//                           color: AppColors.withAlpha(AppColors.primary, 128)),
//                     ),
//                   ),
//                   onChanged: (value) {
//                     // Implement search functionality
//                   },
//                 ),
//               ),
//               const SizedBox(height: 8),
//               Expanded(
//                 child: ListView.builder(
//                   controller: scrollController,
//                   itemCount: _countryCodes.length,
//                   itemBuilder: (context, index) {
//                     final country = _countryCodes[index];
//                     final isSelected = _selectedCountryCode == country['code'];
//                     return ListTile(
//                       leading: isSelected
//                           ? const Icon(Icons.check_circle,
//                               color: AppColors.primary)
//                           : const Icon(Icons.language,
//                               color: AppColors.grey500),
//                       title: Text(
//                         country['name']!,
//                         style: TextStyle(
//                           color: AppColors.grey800,
//                           fontWeight:
//                               isSelected ? FontWeight.bold : FontWeight.normal,
//                         ),
//                       ),
//                       trailing: Text(
//                         country['code']!,
//                         style: TextStyle(
//                           color: isSelected
//                               ? AppColors.primary
//                               : AppColors.grey600,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                       tileColor: isSelected
//                           ? AppColors.withAlpha(AppColors.primary, 20)
//                           : null,
//                       onTap: () {
//                         setState(() {
//                           _selectedCountryCode = country['code']!;
//                         });
//                         Navigator.pop(context);
//                       },
//                     );
//                   },
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'package:achawach/core/constants/app_colors.dart';
import 'package:achawach/core/utils/credentials_manager.dart';
import 'package:achawach/core/utils/page_transitions.dart';
import 'package:achawach/services/api_service.dart';
import 'package:achawach/routers/app_router.dart';
import 'package:achawach/screens/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  bool _isPasswordVisible = false;
  bool _rememberMe = false;
  bool _isLoading = false;
  bool _loginSuccessful = false; // Track login success for animation
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String _selectedCountryCode = '+251'; // Default country code for Ethiopia
  bool _submitted = false; // Tracks form submission
  String? _phoneError; // Stores phone number error message
  final ApiService _apiService = ApiService();

  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();

  // Map to hold the expected phone number length for each country code.
  final Map<String, int> _phoneLengthMap = {
    '+251': 9, // Ethiopia
    '+254': 9, // Kenya
    '+234': 10, // Nigeria
    '+1': 10, // United States
    '+44': 10, // United Kingdom
  };

  final List<Map<String, String>> _countryCodes = [
    {'code': '+251', 'name': 'Ethiopia'},
    {'code': '+254', 'name': 'Kenya'},
    {'code': '+234', 'name': 'Nigeria'},
    {'code': '+1', 'name': 'United States'},
    {'code': '+44', 'name': 'United Kingdom'},
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.forward();

    // Load saved credentials if available
    _loadSavedCredentials();
  }

  Future<void> _loadSavedCredentials() async {
    try {
      final rememberMe = await CredentialsManager.getRememberMe();
      final hasCredentials = await CredentialsManager.hasSavedCredentials();

      if (rememberMe && hasCredentials) {
        final phoneNumber = await CredentialsManager.getPhoneNumber();
        final countryCode = await CredentialsManager.getCountryCode();
        final password = await CredentialsManager.getPassword();

        if (mounted) {
          setState(() {
            _rememberMe = rememberMe;
            if (phoneNumber != null) _phoneController.text = phoneNumber;
            if (countryCode != null) _selectedCountryCode = countryCode;
            if (password != null) _passwordController.text = password;
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading saved credentials: $e');
    }
  }

  Future<void> _login() async {
    setState(() {
      _submitted = true;
    });

    await Future.delayed(Duration.zero);

    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final phoneNumber = '$_selectedCountryCode${_phoneController.text}';
    final password = _passwordController.text;

    debugPrint('Attempting login with phone: $phoneNumber');

    try {
      await CredentialsManager.saveCredentials(
        rememberMe: _rememberMe,
        phoneNumber: _phoneController.text,
        countryCode: _selectedCountryCode,
        password: password,
      );

      await _apiService.login(phoneNumber, password);

      if (mounted) {
        setState(() {
          _loginSuccessful = true;
        });

        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          setState(() {
            _loginSuccessful = false;
          });
          Navigator.of(context).pushReplacement(
            PageTransitions.scaleFade(
              page: const HomeScreen(),
              duration: const Duration(milliseconds: 600),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        debugPrint('Login error details: $e');
        String errorMessage = 'Login failed';
        if (e.toString().contains('SocketException') ||
            e.toString().contains('Connection refused')) {
          errorMessage =
              'Cannot connect to server. Please check your internet connection or contact support.';
        } else if (e.toString().contains('401')) {
          errorMessage = 'Invalid phone number or password';
        } else if (e.toString().contains('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        } else {
          errorMessage =
              'Login failed: ${e.toString().split('Exception:').last.trim()}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _loginSuccessful = false;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.grey100,
      body: Stack(
        children: [
          SafeArea(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                    horizontal: 24.0, vertical: 32.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildHeader(),
                      const SizedBox(height: 48),
                      _buildPhoneField(),
                      const SizedBox(height: 16),
                      _buildPasswordField(),
                      const SizedBox(height: 24),
                      _buildRememberMeAndForgotPassword(),
                      const SizedBox(height: 32),
                      _buildLoginButton(),
                      const SizedBox(height: 24),
                      _buildSignUpLink(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          if (_loginSuccessful)
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 700),
                curve: Curves.elasticOut,
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: value,
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.green[500]!,
                              Colors.green[700]!,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.green.withOpacity(0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.check_circle,
                                color: Colors.white, size: 24),
                            SizedBox(width: 12),
                            Text(
                              'Login Successful!',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: Image.asset(
            'assets/images/icon.png',
            width: 80,
            height: 80,
          ),
        ),
        const SizedBox(height: 16),
        const Text(
          'Welcome Back',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.w700,
            color: AppColors.grey800,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Sign in to your account',
          style: TextStyle(
            fontSize: 16,
            color: AppColors.grey600,
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneField() {
    // Get the expected length for the current country code
    final int expectedLength = _phoneLengthMap[_selectedCountryCode] ?? 9;
    final String hintText = 'X' * expectedLength;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 20,
          child: _phoneError != null
              ? Padding(
                  padding: const EdgeInsets.only(left: 16, bottom: 4),
                  child: Text(
                    _phoneError!,
                    style:
                        const TextStyle(color: AppColors.error, fontSize: 12),
                  ),
                )
              : null,
        ),
        TextFormField(
          controller: _phoneController,
          keyboardType: TextInputType.phone,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            // Use the dynamic length for the formatter
            LengthLimitingTextInputFormatter(expectedLength),
          ],
          autovalidateMode: _submitted
              ? AutovalidateMode.onUserInteraction
              : AutovalidateMode.disabled,
          decoration: InputDecoration(
            labelText: 'Phone Number',
            hintText: hintText,
            prefixIcon: GestureDetector(
              onTap: () => _showCountryCodePicker(context),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                margin: const EdgeInsets.only(right: 8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.phone,
                      color: AppColors.primary.withOpacity(0.7),
                      size: 20,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _selectedCountryCode,
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Icon(
                      Icons.arrow_drop_down,
                      color: AppColors.primary,
                      size: 20,
                    ),
                    Container(
                      height: 24,
                      width: 1,
                      color: AppColors.primary.withOpacity(0.5),
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                    ),
                  ],
                ),
              ),
            ),
            filled: true,
            fillColor: AppColors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary.withOpacity(0.5)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _phoneError != null
                    ? AppColors.error
                    : AppColors.primary.withOpacity(0.5),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color:
                    _phoneError != null ? AppColors.error : AppColors.primary,
                width: 2,
              ),
            ),
            errorStyle: const TextStyle(height: 0, fontSize: 0),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              _phoneError = 'Please enter your phone number';
              return _phoneError;
            } else if (value.length != expectedLength) {
              _phoneError = 'Phone number must be $expectedLength digits';
              return _phoneError;
            }
            _phoneError = null;
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      decoration: InputDecoration(
        labelText: 'Password',
        hintText: 'Enter your password',
        prefixIcon: Icon(
          Icons.lock_outline,
          color: AppColors.primary.withValues(alpha: 0.7),
        ),
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
            color: AppColors.primary.withValues(alpha: 0.7),
          ),
          onPressed: () {
            setState(() {
              _isPasswordVisible = !_isPasswordVisible;
            });
          },
        ),
        filled: true,
        fillColor: AppColors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary.withOpacity(0.5)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary.withOpacity(0.5)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your password';
        }
        return null;
      },
    );
  }

  Widget _buildRememberMeAndForgotPassword() {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _rememberMe = !_rememberMe;
            });
          },
          child: Row(
            children: [
              Checkbox(
                value: _rememberMe,
                onChanged: (value) {
                  setState(() {
                    _rememberMe = value ?? false;
                  });
                },
                activeColor: AppColors.primary,
                checkColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const Text(
                'Remember me',
                style: TextStyle(
                  color: AppColors.grey700,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        const Spacer(),
        TextButton(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Forgot password feature coming soon'),
                backgroundColor: AppColors.info,
                duration: Duration(seconds: 2),
              ),
            );
          },
          child: const Text(
            'Forgot Password?',
            style: TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _login,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 16),
        elevation: 4,
        disabledBackgroundColor: AppColors.primary.withOpacity(0.5),
      ),
      child: _isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
              ),
            )
          : const Text(
              'Sign In',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
    );
  }

  Widget _buildSignUpLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          "Don't have an account? ",
          style: TextStyle(
            color: AppColors.grey600,
            fontSize: 14,
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pushNamed(AppRouter.signupRoute);
          },
          child: const Text(
            'Sign Up',
            style: TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  void _showCountryCodePicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: AppColors.grey100,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.symmetric(vertical: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.grey400,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const Padding(
                padding: EdgeInsets.all(16),
                child: Text(
                  'Select Country Code',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.grey800,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search country',
                    prefixIcon: Icon(
                      Icons.search,
                      color: AppColors.primary.withValues(alpha: 0.7),
                    ),
                    filled: true,
                    fillColor: AppColors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          BorderSide(color: AppColors.primary.withOpacity(0.5)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          BorderSide(color: AppColors.primary.withOpacity(0.5)),
                    ),
                  ),
                  onChanged: (value) {
                    // Implement search functionality
                  },
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  itemCount: _countryCodes.length,
                  itemBuilder: (context, index) {
                    final country = _countryCodes[index];
                    final isSelected = _selectedCountryCode == country['code'];
                    return ListTile(
                      leading: isSelected
                          ? const Icon(Icons.check_circle,
                              color: AppColors.primary)
                          : const Icon(Icons.language,
                              color: AppColors.grey500),
                      title: Text(
                        country['name']!,
                        style: TextStyle(
                          color: AppColors.grey800,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      trailing: Text(
                        country['code']!,
                        style: TextStyle(
                          color: isSelected
                              ? AppColors.primary
                              : AppColors.grey600,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      tileColor: isSelected
                          ? AppColors.primary.withOpacity(0.05)
                          : null,
                      onTap: () {
                        setState(() {
                          _selectedCountryCode = country['code']!;
                          // Clear any previous error when the country changes.
                          _phoneError = null;
                        });
                        // Re-validate the form to give the user immediate feedback.
                        if (_submitted) {
                          _formKey.currentState?.validate();
                        }
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
