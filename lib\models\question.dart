import 'category.dart';

class Choice {
  final int id;
  final String choice;
  final int isAnswer;
  final int qId;

  Choice({
    required this.id,
    required this.choice,
    required this.isAnswer,
    required this.qId,
  });

  factory Choice.fromJson(Map<String, dynamic> json) {
    return Choice(
      id: json['id'] ?? 0,
      choice: json['choice'] ?? '',
      isAnswer: json['isAnswer'] ?? 0,
      qId: json['qId'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'choice': choice,
      'isAnswer': isAnswer,
      'qId': qId,
    };
  }
}

class Question {
  final int id;
  final String question;
  final String type;
  final String filePath;
  final String strength;
  final List<Choice> choices;
  final Category category;
  final int time;

  Question({
    required this.id,
    required this.question,
    required this.type,
    required this.filePath,
    required this.strength,
    required this.choices,
    required this.category,
    required this.time,
  });

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      id: json['id'] ?? 0,
      question: json['question'] ?? '',
      type: json['type'] ?? '',
      filePath: json['file_path'] ?? '',
      strength: json['strength'] ?? '',
      choices: (json['choices'] as List<dynamic>?)
              ?.map((choice) => Choice.fromJson(choice))
              .toList() ??
          [],
      category: Category.fromJson(json['category'] ?? {}),
      time: json['time'] ?? 60,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'type': type,
      'file_path': filePath,
      'strength': strength,
      'choices': choices.map((choice) => choice.toJson()).toList(),
      'category': category.toJson(),
      'time': time,
    };
  }
}
