import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:achawach/providers/challenge_provider.dart';
import 'package:achawach/providers/settings_provider.dart';
import 'package:achawach/models/question.dart';
import 'package:achawach/models/category.dart';
import 'package:achawach/models/challenge.dart';

import 'dart:async';

// Create a simple mock implementation of ChallengeProvider for testing
class MockChallengeProvider implements ChallengeProvider {
  List<Question> _questions = [];
  Map<String, dynamic> _summary = {'total_questions': 0, 'total_points': 0};
  bool throwError = false;
  Map<String, dynamic> _lifeData = {'life': 3};
  Map<String, dynamic> _answerResponse = {
    'isCorrect': true,
    'progress': {'level': 1, 'score': 10, 'life': 3}
  };
  bool levelUp = false;

  @override
  List<Question> get questions => _questions;

  @override
  Map<String, dynamic>? get questionsSummary => _summary;

  // Set mock questions for testing
  void setMockQuestions(List<Question> questions) {
    _questions = questions;
    _summary = {
      'total_questions': questions.length,
      'total_points': questions.length * 10
    };
  }

  // Set mock life data for testing
  void setMockLifeData(int life) {
    _lifeData = {'life': life};
  }

  // Set mock answer response for testing
  void setMockAnswerResponse(
      {bool isCorrect = true,
      int level = 1,
      int score = 10,
      int life = 3,
      bool leveledUp = false}) {
    _answerResponse = {
      'isCorrect': isCorrect,
      'progress': {'level': level, 'score': score, 'life': life},
      'leveledUp': leveledUp
    };
    levelUp = leveledUp;
  }

  @override
  Future<void> fetchChallengeQuestions(int challengeId) async {
    if (throwError) {
      throw Exception('Failed to load questions');
    }
    // Don't use real delays in tests
    return;
  }

  @override
  Future<Map<String, dynamic>> submitAnswer(
      int challengeId, int questionId, int answerId, int categoryId) async {
    // Don't use real delays in tests
    return _answerResponse;
  }

  @override
  Future<Map<String, dynamic>> decreaseChallengeLife(
      int challengeId, int questionId) async {
    // Don't use real delays in tests
    _lifeData['life'] = (_lifeData['life'] as int) - 1;
    return _lifeData;
  }

  @override
  Future<Map<String, dynamic>> startChallenge(
      int challengeId, int categoryId) async {
    // Don't use real delays in tests
    return {
      'status': 'success',
      'data': {'id': challengeId}
    };
  }

  // Implement remaining required methods from ChallengeProvider
  @override
  List<Category> get categories => [];

  @override
  List<Challenge> get challenges => [];

  @override
  List<Challenge> get startedChallenges => [];

  @override
  List<Challenge> get notStartedChallenges => [];

  @override
  Map<String, dynamic>? get challengeLife => _lifeData;

  @override
  bool get isLoading => false;

  @override
  String? get error => null;

  @override
  Future<void> fetchCategories() async {}

  @override
  Future<void> fetchChallenges() async {}

  @override
  Future<void> fetchStartedChallenges(int categoryId) async {}

  @override
  Future<void> fetchNotStartedChallenges(int categoryId) async {}

  @override
  Future<void> getChallengeLife(int challengeId) async {}

  // ChangeNotifier methods
  @override
  void addListener(VoidCallback listener) {}

  @override
  void dispose() {}

  @override
  bool get hasListeners => false;

  @override
  void notifyListeners() {}

  @override
  void removeListener(VoidCallback listener) {}
}

// Simple mock implementation of SettingsProvider
class MockSettingsProvider implements SettingsProvider {
  bool _soundEnabled = false;
  bool _isDarkMode = false;

  @override
  bool get isSoundEnabled => _soundEnabled;

  @override
  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
  }

  @override
  Future<void> toggleSound() async {
    _soundEnabled = !_soundEnabled;
  }

  // Implement remaining required methods from SettingsProvider
  @override
  bool get isDarkMode => _isDarkMode;

  @override
  Future<void> setDarkMode(bool value) async {
    _isDarkMode = value;
  }

  @override
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
  }

  // Additional methods not in the interface but might be used in tests
  String get language => 'en';

  // ChangeNotifier methods
  @override
  void addListener(VoidCallback listener) {}

  @override
  void dispose() {}

  @override
  bool get hasListeners => false;

  @override
  void notifyListeners() {}

  @override
  void removeListener(VoidCallback listener) {}
}

// Simple mock timer class for testing
class MockTimer implements Timer {
  @override
  bool get isActive => true;

  @override
  int get tick => 0;

  @override
  void cancel() {}
}

// A simplified mock version of SpecialChallengeStartScreen for testing
class MockSpecialChallengeStartScreen extends StatelessWidget {
  final Map<String, dynamic> selectedChallenge;
  final List<Question> questions;

  const MockSpecialChallengeStartScreen({
    super.key,
    required this.selectedChallenge,
    required this.questions,
  });

  @override
  Widget build(BuildContext context) {
    // Parse start_date and end_date if available
    DateTime? startDate;
    DateTime? endDate;

    if (selectedChallenge['start_date'] != null) {
      startDate = DateTime.tryParse(selectedChallenge['start_date'].toString());
    }

    if (selectedChallenge['end_date'] != null) {
      endDate = DateTime.tryParse(selectedChallenge['end_date'].toString());
    }

    final currentQuestion = questions.isNotEmpty ? questions[0] : null;

    return Scaffold(
      appBar: AppBar(
        title: Text(selectedChallenge['name'] ?? 'Special Challenge'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {},
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Challenge dates if available
          if (startDate != null || endDate != null)
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.calendar_today),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _formatDates(startDate, endDate),
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ),

          // Timer
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: LinearProgressIndicator(
              value: currentQuestion != null ? 0.5 : 0,
            ),
          ),

          // Question
          if (currentQuestion != null)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                currentQuestion.question,
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),

          // Choices
          if (currentQuestion != null)
            Expanded(
              child: ListView.builder(
                itemCount: currentQuestion.choices.length,
                itemBuilder: (context, index) {
                  final choice = currentQuestion.choices[index];
                  return ListTile(
                    title: Text(choice.choice),
                    onTap: () {},
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  String _formatDates(DateTime? startDate, DateTime? endDate) {
    if (startDate != null && endDate != null) {
      return "${_formatDate(startDate)} - ${_formatDate(endDate)}";
    } else if (startDate != null) {
      return "Starts: ${_formatDate(startDate)}";
    } else if (endDate != null) {
      return "Ends: ${_formatDate(endDate)}";
    }
    return "Date not specified";
  }

  String _formatDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }
}

void main() {
  group('SpecialChallengeStartScreen Tests', () {
    testWidgets(
        'SpecialChallengeStartScreen displays challenge dates correctly',
        (WidgetTester tester) async {
      // Create a mock challenge data with start_date and end_date
      final mockChallenge = {
        'id': 1,
        'name': 'Test Challenge',
        'status': 'Started',
        'start_date': '2023-06-01T00:00:00.000Z',
        'end_date': '2023-06-30T23:59:59.000Z',
      };

      // Create test category
      final testCategory = Category(
        id: 1,
        name: 'Test Category',
        icon: 'Icons.category',
        color: 'blue',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Create test questions
      final List<Question> testQuestions = [
        Question(
          id: 1,
          question: 'What is 2+2?',
          type: 'text',
          filePath: '',
          strength: 'medium',
          time: 30,
          category: testCategory,
          choices: [
            Choice(id: 1, choice: '3', isAnswer: 0, qId: 1),
            Choice(id: 2, choice: '4', isAnswer: 1, qId: 1),
            Choice(id: 3, choice: '5', isAnswer: 0, qId: 1),
          ],
        ),
      ];

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: MockSpecialChallengeStartScreen(
            selectedChallenge: mockChallenge,
            questions: testQuestions,
          ),
        ),
      );

      // Verify challenge name is displayed in the app bar
      expect(find.text('Test Challenge'), findsOneWidget);

      // Verify dates are displayed
      expect(find.textContaining('2023-06-01'), findsOneWidget);
      expect(find.textContaining('2023-06-30'), findsOneWidget);

      // Verify question is displayed
      expect(find.text('What is 2+2?'), findsOneWidget);

      // Verify choices are displayed
      expect(find.text('3'), findsOneWidget);
      expect(find.text('4'), findsOneWidget);
      expect(find.text('5'), findsOneWidget);
    });

    testWidgets('SpecialChallengeStartScreen handles missing dates gracefully',
        (WidgetTester tester) async {
      // Create a mock challenge data without dates
      final mockChallenge = {
        'id': 2,
        'name': 'Challenge Without Dates',
        'status': 'Started',
      };

      // Create test category
      final testCategory = Category(
        id: 1,
        name: 'Test Category',
        icon: 'Icons.category',
        color: 'blue',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Create test questions
      final List<Question> testQuestions = [
        Question(
          id: 1,
          question: 'Sample Question',
          type: 'text',
          filePath: '',
          strength: 'medium',
          time: 30,
          category: testCategory,
          choices: [
            Choice(id: 1, choice: 'Option A', isAnswer: 0, qId: 1),
            Choice(id: 2, choice: 'Option B', isAnswer: 1, qId: 1),
          ],
        ),
      ];

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: MockSpecialChallengeStartScreen(
            selectedChallenge: mockChallenge,
            questions: testQuestions,
          ),
        ),
      );

      // Verify challenge name is displayed in the app bar
      expect(find.text('Challenge Without Dates'), findsOneWidget);

      // Verify no date container is shown
      expect(find.byIcon(Icons.calendar_today), findsNothing);

      // Verify question is displayed
      expect(find.text('Sample Question'), findsOneWidget);
    });

    testWidgets(
        'SpecialChallengeStartScreen has a close button that can be tapped',
        (WidgetTester tester) async {
      // Create a flag to track if the close button was pressed
      bool closeButtonPressed = false;

      // Create a testable widget with a Navigator that can track pop events
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(
              leading: IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  closeButtonPressed = true;
                },
              ),
            ),
            body: const Text('Close button test'),
          ),
        ),
      );

      // Find and tap the close button
      await tester.tap(find.byIcon(Icons.close));
      await tester.pump();

      // Verify that the close button was pressed
      expect(closeButtonPressed, isTrue);
    });

    test('MockChallengeProvider can handle life management', () {
      // Create a stub provider
      final provider = MockChallengeProvider();

      // Set mock life data
      provider.setMockLifeData(1);

      // Verify that the life data is set correctly
      expect(provider._lifeData['life'], equals(1));

      // Simulate decreasing life
      provider.decreaseChallengeLife(123, 456);

      // Verify that the life was decreased
      expect(provider._lifeData['life'], equals(0));
    });

    test('MockChallengeProvider can handle level-up functionality', () {
      // Create a stub provider
      final provider = MockChallengeProvider();

      // Set up the answer response to indicate a level-up
      provider.setMockAnswerResponse(
        isCorrect: true,
        level: 2, // Level increased from 1 to 2
        score: 100,
        leveledUp: true,
      );

      // Verify that the level-up flag is set
      expect(provider.levelUp, isTrue);

      // Verify that the answer response contains the correct data
      final answerResponse = provider.submitAnswer(123, 1, 2, 456);
      expect(answerResponse, completes);
    });
  });
}
