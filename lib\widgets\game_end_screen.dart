import 'package:flutter/material.dart';
import 'package:achawach/core/constants/app_colors.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:confetti/confetti.dart';
import 'package:lottie/lottie.dart';
import 'dart:math' as math;
import 'package:achawach/core/utils/theme_utils.dart';

class GameEndScreen extends StatefulWidget {
  final bool isWinner;
  final String message;
  final Map<String, dynamic> finalState;
  final VoidCallback onContinue;
  final String? winnerMessage;
  final String? loserMessage;
  final int? winnerId;
  final int? challengeId;
  final int? winnerScore;
  final int? winnerRank;
  final int? winnerLevel;

  const GameEndScreen({
    Key? key,
    required this.isWinner,
    required this.message,
    required this.finalState,
    required this.onContinue,
    this.winnerMessage,
    this.loserMessage,
    this.winnerId,
    this.challengeId,
    this.winnerScore,
    this.winnerRank,
    this.winnerLevel,
  }) : super(key: key);

  @override
  State<GameEndScreen> createState() => _GameEndScreenState();
}

class _GameEndScreenState extends State<GameEndScreen>
    with SingleTickerProviderStateMixin {
  late ConfettiController _confettiController;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
   double rewared = 0;
  @override
  void initState() {
    super.initState();
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 5));

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.35),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.65, curve: Curves.elasticOut),
    ));

    // Start animations
    _animationController.forward();
    if (widget.isWinner) {
      _confettiController.play();
    }
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get theme-aware colors
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDarkMode ? Colors.grey[900] : AppColors.background;
    final textColor = ThemeUtils.getTextColor(context);
    final cardColor = isDarkMode ? Colors.grey[850] : Colors.white;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        // When back button is pressed, use the onContinue callback
        // This ensures consistent navigation behavior
        widget.onContinue();
      },
      child: Scaffold(
        backgroundColor: backgroundColor,
        body: Stack(
          children: [
            // Background design elements
            Positioned.fill(
              child: CustomPaint(
                painter: BackgroundPainter(
                  isWinner: widget.isWinner,
                  isDarkMode: isDarkMode,
                ),
              ),
            ),

            // Main content
            Center(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Title with scale animation
                          ScaleTransition(
                            scale: _scaleAnimation,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 40,
                                vertical: 20,
                              ),
                              decoration: BoxDecoration(
                                color: widget.isWinner
                                    ? Colors.amber
                                    : AppColors.primary,
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: (widget.isWinner
                                            ? Colors.amber
                                            : AppColors.primary)
                                        .withAlpha(128),
                                    blurRadius: 20,
                                    spreadRadius: 5,
                                  ),
                                ],
                              ),
                              child: Text(
                                widget.isWinner ? 'WINNER!' : 'GAME OVER',
                                style: GoogleFonts.rubik(
                                  fontSize: 32,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  letterSpacing: 2,
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 40),

                          // Trophy or game over icon
                          Container(
                            width: 200,
                            height: 200,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: (widget.isWinner
                                          ? Colors.amber
                                          : AppColors.primary)
                                      .withAlpha(128),
                                  blurRadius: 30,
                                  spreadRadius: 10,
                                ),
                              ],
                            ),
                            child: Center(
                              child: widget.isWinner
                                  ? Lottie.asset(
                                      'assets/animations/trophy.json',
                                      width: 150,
                                      height: 150,
                                      fit: BoxFit.contain,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return const Icon(
                                          Icons.emoji_events,
                                          size: 100,
                                          color: Colors.amber,
                                        );
                                      },
                                    )
                                  : const Icon(
                                      Icons.sports_score,
                                      size: 100,
                                      color: AppColors.primary,
                                    ),
                            ),
                          ),

                          const SizedBox(height: 30),

                          // Message
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: cardColor,
                              borderRadius: BorderRadius.circular(15),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black
                                      .withAlpha(isDarkMode ? 50 : 20),
                                  blurRadius: 10,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                Text(
                                  widget.isWinner
                                      ? widget.winnerMessage ??
                                          'Congratulations!'
                                      : widget.loserMessage ??
                                          'Better luck next time!',
                                  style: GoogleFonts.rubik(
                                    fontSize: 22,
                                    fontWeight: FontWeight.bold,
                                    color: textColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 10),
                                Text(
                                  widget.message,
                                  style: GoogleFonts.rubik(
                                    fontSize: 16,
                                    color: ThemeUtils.getSecondaryTextColor(
                                        context),
                                  ),
                                  textAlign: TextAlign.center,
                                ),

                                const SizedBox(height: 20),

                                // Final state info
                                if (widget.finalState.isNotEmpty) ...[
                                  Divider(
                                    color: isDarkMode
                                        ? Colors.grey[600]
                                        : Colors.grey[300],
                                  ),
                                  const SizedBox(height: 10),
                                  Text(
                                    'Final Results',
                                    style: GoogleFonts.rubik(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: textColor,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  _buildFinalStateInfo(),
                                ],
                              ],
                            ),
                          ),

                          const SizedBox(height: 30),

                          // Continue button
                          ElevatedButton(
                            onPressed: widget.onContinue,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: widget.isWinner
                                  ? Colors.amber
                                  : AppColors.primary,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 32,
                                vertical: 16,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                              elevation: 5,
                              shadowColor: (widget.isWinner
                                      ? Colors.amber
                                      : AppColors.primary)
                                  .withAlpha(128),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  widget.isWinner
                                      ? Icons.celebration
                                      : Icons.home,
                                  size: 24,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Continue',
                                  style: GoogleFonts.rubik(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Confetti for winner
            if (widget.isWinner)
              Align(
                alignment: Alignment.topCenter,
                child: ConfettiWidget(
                  confettiController: _confettiController,
                  blastDirection: math.pi / 2,
                  maxBlastForce: 5,
                  minBlastForce: 1,
                  emissionFrequency: 0.05,
                  numberOfParticles: 20,
                  gravity: 0.1,
                  colors: const [
                    Colors.green,
                    Colors.blue,
                    Colors.pink,
                    Colors.orange,
                    Colors.purple,
                    Colors.yellow,
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinalStateInfo() {
    final textColor = ThemeUtils.getTextColor(context);
    final secondaryTextColor = ThemeUtils.getSecondaryTextColor(context);

    // Keys to exclude
    final Set<String> excludedKeys = {
      'id',
      'userId',
      'challenge_id',
      'createdAt',
      'updatedAt',
      'life',
    };

    return Column(
      children: widget.finalState.entries.map((entry) {
        // Skip if value is complex or key is in the exclusion list
        if ((entry.value is! Map && entry.value is! List) &&
            !excludedKeys.contains(entry.key)) {
          if (entry.key == 'Rewared') {
            rewared = entry.value;
          }
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatKey(entry.key),
                  style: GoogleFonts.rubik(
                    fontWeight: FontWeight.w500,
                    color: secondaryTextColor,
                  ),
                ),
                Text(
                  entry.value.toString(),
                  style: GoogleFonts.rubik(
                    fontWeight: FontWeight.bold,
                    color: textColor,
                  ),
                ),
              ],
            ),
          );
        } else {
          return const SizedBox.shrink();
        }
      }).toList(),
    );
  }

  String _formatKey(String key) {
    // Convert camelCase or snake_case to Title Case
    final formattedKey = key
        .replaceAllMapped(RegExp(r'([A-Z])'), (match) => ' ${match.group(0)}')
        .replaceAll('_', ' ')
        .trim();

    return formattedKey.substring(0, 1).toUpperCase() +
        formattedKey.substring(1);
  }
}

class BackgroundPainter extends CustomPainter {
  final bool isWinner;
  final bool isDarkMode;

  BackgroundPainter({required this.isWinner, this.isDarkMode = false});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;

    // Create a gradient background
    final Rect rect = Offset.zero & size;

    if (isWinner) {
      // Winner background - golden gradient (adjusted for dark mode)
      if (isDarkMode) {
        paint.shader = const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF8B6000), // Darker gold
            Color(0xFFAA7700), // Dark gold
            Color(0xFFCC8800), // Medium gold
            Color(0xFFDDAA00), // Light gold
          ],
          stops: [0.1, 0.4, 0.7, 0.9],
        ).createShader(rect);
      } else {
        paint.shader = const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFFC107),
            Color(0xFFFFD54F),
            Color(0xFFFFE082),
            Color(0xFFFFECB3),
          ],
          stops: [0.1, 0.4, 0.7, 0.9],
        ).createShader(rect);
      }
    } else {
      // Loser background - blue/orange gradient (adjusted for dark mode)
      if (isDarkMode) {
        paint.shader = LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.shade900,
            Colors.blue.shade800,
            Colors.blue.shade700,
            Colors.blue.shade600,
          ],
          stops: const [0.1, 0.4, 0.7, 0.9],
        ).createShader(rect);
      } else {
        paint.shader = LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withAlpha(50),
            AppColors.primary.withAlpha(100),
            AppColors.primary.withAlpha(150),
            AppColors.primary.withAlpha(200),
          ],
          stops: const [0.1, 0.4, 0.7, 0.9],
        ).createShader(rect);
      }
    }

    canvas.drawRect(rect, paint);

    // Add some decorative circles with appropriate contrast for dark mode
    final circlePaint = Paint()
      ..style = PaintingStyle.fill
      ..color = (isDarkMode ? Colors.white : Colors.white).withAlpha(30);

    // Draw several circles of different sizes
    canvas.drawCircle(
      Offset(size.width * 0.1, size.height * 0.1),
      size.width * 0.15,
      circlePaint,
    );

    canvas.drawCircle(
      Offset(size.width * 0.8, size.height * 0.3),
      size.width * 0.1,
      circlePaint,
    );

    canvas.drawCircle(
      Offset(size.width * 0.3, size.height * 0.8),
      size.width * 0.12,
      circlePaint,
    );

    canvas.drawCircle(
      Offset(size.width * 0.9, size.height * 0.9),
      size.width * 0.08,
      circlePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is BackgroundPainter &&
        (oldDelegate.isWinner != isWinner ||
            oldDelegate.isDarkMode != isDarkMode);
  }
}
