import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Simple mock implementation for testing edge cases
class TestableSocketService {
  bool connectCalled = false;
  bool disconnectCalled = false;
  bool reconnectCalled = false;
  String? lastEmittedEvent;
  Map<String, dynamic>? lastEmittedData;
  List<String> errorLog = [];
  bool simulateConnectionError = false;
  bool simulateEmitError = false;
  bool simulateReconnectError = false;
  bool _isConnected = false;
  
  // Stream controllers
  final StreamController<Map<String, dynamic>> gameEndedController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> winnerAnnouncementController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  // Streams
  Stream<Map<String, dynamic>> get gameEndedStream => gameEndedController.stream;
  Stream<Map<String, dynamic>> get winnerAnnouncementStream => winnerAnnouncementController.stream;
  
  // Connection status
  bool get isConnected => _isConnected;
  
  // Connect method
  Future<void> connect(String token, String challengeId) async {
    if (simulateConnectionError) {
      errorLog.add('Connection error');
      throw Exception('Simulated connection error');
    }
    connectCalled = true;
    _isConnected = true;
  }
  
  // Disconnect method
  void disconnect() {
    disconnectCalled = true;
    _isConnected = false;
  }
  
  // Reconnect method
  void reconnect(String token) {
    if (simulateReconnectError) {
      errorLog.add('Reconnection error');
      throw Exception('Simulated reconnection error');
    }
    reconnectCalled = true;
    _isConnected = true;
  }
  
  // Emit method
  void emit(String event, Map<String, dynamic> data) {
    if (simulateEmitError) {
      errorLog.add('Emit error: $event');
      throw Exception('Simulated emit error');
    }
    lastEmittedEvent = event;
    lastEmittedData = data;
  }
  
  // End game method
  void endGame(String token, String challengeId) {
    if (!_isConnected) {
      errorLog.add('Cannot end game: not connected');
      return;
    }
    emit('endGame', {'token': token, 'challengeId': challengeId});
  }
  
  // Helper method to simulate receiving socket events
  void simulateEvent(String event, Map<String, dynamic> data) {
    switch (event) {
      case 'gameEnded':
        gameEndedController.add(data);
        break;
      case 'winnerAnnouncement':
        winnerAnnouncementController.add(data);
        break;
      default:
        errorLog.add('Unknown event: $event');
    }
  }
  
  // Dispose method
  void dispose() {
    gameEndedController.close();
    winnerAnnouncementController.close();
  }
}

void main() {
  group('SocketService Edge Cases', () {
    late TestableSocketService socketService;

    setUp(() {
      socketService = TestableSocketService();
      SharedPreferences.setMockInitialValues({'auth_token': 'test-token'});
    });

    tearDown(() {
      socketService.dispose();
    });

    test('Handles connection errors gracefully', () async {
      socketService.simulateConnectionError = true;
      
      // This should throw with the simulated error
      expect(() => socketService.connect('test-token', '123'), throwsException);
      
      expect(socketService.errorLog, contains('Connection error'));
      expect(socketService.isConnected, isFalse);
    });

    test('Handles reconnection after disconnect', () async {
      await socketService.connect('test-token', '123');
      expect(socketService.isConnected, isTrue);
      
      socketService.disconnect();
      expect(socketService.isConnected, isFalse);
      
      socketService.reconnect('test-token');
      expect(socketService.isConnected, isTrue);
      expect(socketService.reconnectCalled, isTrue);
    });

    test('Handles reconnection errors gracefully', () {
      socketService.simulateReconnectError = true;
      
      // This should throw with the simulated error
      expect(() => socketService.reconnect('test-token'), throwsException);
      
      expect(socketService.errorLog, contains('Reconnection error'));
      expect(socketService.isConnected, isFalse);
    });

    test('Handles emit errors gracefully', () async {
      await socketService.connect('test-token', '123');
      socketService.simulateEmitError = true;
      
      // This should throw with the simulated error
      expect(() => socketService.emit('testEvent', {'data': 'test'}), throwsException);
      
      expect(socketService.errorLog, contains('Emit error: testEvent'));
    });

    test('Handles multiple rapid connects and disconnects', () async {
      // Connect
      await socketService.connect('test-token', '123');
      expect(socketService.isConnected, isTrue);
      
      // Disconnect
      socketService.disconnect();
      expect(socketService.isConnected, isFalse);
      
      // Connect again
      await socketService.connect('test-token', '123');
      expect(socketService.isConnected, isTrue);
      
      // Disconnect again
      socketService.disconnect();
      expect(socketService.isConnected, isFalse);
      
      // Verify the sequence worked correctly
      expect(socketService.connectCalled, isTrue);
      expect(socketService.disconnectCalled, isTrue);
    });

    test('Handles multiple events in rapid succession', () async {
      socketService.connect('test-token', '123');
      
      // Set up counters to track events
      int gameEndedCount = 0;
      int winnerAnnouncementCount = 0;
      
      // Listen for events
      socketService.gameEndedStream.listen((_) {
        gameEndedCount++;
      });
      
      socketService.winnerAnnouncementStream.listen((_) {
        winnerAnnouncementCount++;
      });
      
      // Simulate multiple events in rapid succession
      for (int i = 0; i < 5; i++) {
        socketService.simulateEvent('gameEnded', {'count': i});
        socketService.simulateEvent('winnerAnnouncement', {'count': i});
      }
      
      // Allow time for events to be processed
      await Future.delayed(const Duration(milliseconds: 100));
      
      // Verify all events were received
      expect(gameEndedCount, 5);
      expect(winnerAnnouncementCount, 5);
    });

    test('Handles endGame with invalid challenge ID', () async {
      await socketService.connect('test-token', '123');
      
      // Call endGame with various invalid IDs
      socketService.endGame('test-token', '');
      socketService.endGame('test-token', 'invalid');
      socketService.endGame('test-token', '0');
      socketService.endGame('test-token', '-1');
      
      // The test passes if no exceptions are thrown
      expect(true, isTrue);
    });

    test('Handles endGame with invalid token', () async {
      await socketService.connect('test-token', '123');
      
      // Call endGame with various invalid tokens
      socketService.endGame('', '123');
      socketService.endGame('invalid-token', '123');
      
      // The test passes if no exceptions are thrown
      expect(true, isTrue);
    });

    test('Handles lifecycle correctly when app is backgrounded', () async {
      // Connect
      await socketService.connect('test-token', '123');
      expect(socketService.isConnected, isTrue);
      
      // Simulate app going to background by disconnecting
      socketService.disconnect();
      expect(socketService.isConnected, isFalse);
      
      // Simulate app coming back to foreground by reconnecting
      socketService.reconnect('test-token');
      expect(socketService.isConnected, isTrue);
      
      // Verify the sequence worked correctly
      expect(socketService.connectCalled, isTrue);
      expect(socketService.disconnectCalled, isTrue);
      expect(socketService.reconnectCalled, isTrue);
    });
  });
}
