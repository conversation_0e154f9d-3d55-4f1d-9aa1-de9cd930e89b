import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart'; // For number formatting


class RewardRulesCard extends StatelessWidget {
  final RewardRules? rewardRules;
  // final int currentScore; // No longer directly used for display
  // final int? currentLevel; // No longer directly used for display
  final int? currentRank; // This is important for highlighting

  const RewardRulesCard({
    super.key,
    required this.rewardRules,
    // required this.currentScore,
    // this.currentLevel,
    this.currentRank,
  });

  @override
  Widget build(BuildContext context) {
    // Check if new reward distribution rules are provided
    if (rewardRules == null ||
        rewardRules!.totalRewardPool == null ||
        rewardRules!.rewardDistribution == null ||
        rewardRules!.rewardDistribution!.isEmpty) {
      return Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Reward Rules',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Reward distribution details are not available for this challenge.',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    final totalPool = rewardRules!.totalRewardPool!;
    final distribution = rewardRules!.rewardDistribution!;
    final currencyFormatter =
        NumberFormat.currency(locale: 'en_US', symbol: '', decimalDigits: 0);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Reward Rules',
                  style: GoogleFonts.poppins(
                    fontSize: 20, // Increased size
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  'Total: ${currencyFormatter.format(totalPool)}',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).primaryColor, // Use theme color
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Rewards are distributed as follows:',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 12),
            ListView.separated(
              shrinkWrap: true,
              physics:
                  const NeverScrollableScrollPhysics(), // Important for nesting in Column
              itemCount: distribution.length,
              itemBuilder: (context, index) {
                final tier = distribution[index];
                return _buildRewardTierItem(
                  context: context,
                  tier: tier,
                  totalPool: totalPool,
                  currentUserRank: currentRank,
                  formatter: currencyFormatter,
                );
              },
              separatorBuilder: (context, index) => const Divider(height: 20),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRewardTierItem({
    required BuildContext context,
    required RewardTier tier,
    required double totalPool,
    required int? currentUserRank,
    required NumberFormat formatter,
  }) {
    final double tierTotalReward = totalPool * tier.percentageOfPool;
    final int winnersInTier = tier.numberOfWinnersInTier;
    final double rewardPerWinner =
        winnersInTier > 0 ? tierTotalReward / winnersInTier : tierTotalReward;

    bool isCurrentUserInThisTier = false;
    if (currentUserRank != null) {
      isCurrentUserInThisTier = tier.isRankInTier(currentUserRank);
    }

    return Container(
      padding: isCurrentUserInThisTier
          ? const EdgeInsets.all(10)
          : const EdgeInsets.symmetric(vertical: 6),
      decoration: BoxDecoration(
        color: isCurrentUserInThisTier
            ? Theme.of(context).primaryColor.withOpacity(0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isCurrentUserInThisTier
            ? Border.all(color: Theme.of(context).primaryColor, width: 1.5)
            : null,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3, // For tier name
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tier.displayName,
                  style: GoogleFonts.poppins(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: isCurrentUserInThisTier
                        ? Theme.of(context).primaryColorDark
                        : Colors.black87,
                  ),
                ),
                if (isCurrentUserInThisTier)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(
                      '(Your current rank!)',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 4, // For amounts
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${(tier.percentageOfPool * 100).toStringAsFixed(0)}% of pool',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Total: ${formatter.format(tierTotalReward)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black54,
                  ),
                ),
                if (winnersInTier > 1) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Each: ${formatter.format(rewardPerWinner)}',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.green[700],
                    ),
                  ),
                ] else ...[
                  const SizedBox(height: 4),
                  Text(
                    'Reward: ${formatter.format(rewardPerWinner)}',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.green[700],
                    ),
                  ),
                ]
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class RewardTier {
  final String displayName; // e.g., "Winner", "2nd Place", "3rd - 10th Place"
  final int startRank;
  final int endRank;
  final double percentageOfPool; // e.g., 0.30 for 30%

  RewardTier({
    required this.displayName,
    required this.startRank,
    required this.endRank,
    required this.percentageOfPool,
  }) : assert(percentageOfPool >= 0 && percentageOfPool <= 1.0);

  // Helper to check if a rank falls into this tier
  bool isRankInTier(int rank) {
    return rank >= startRank && rank <= endRank;
  }

  // Helper to calculate number of winners in this tier
  int get numberOfWinnersInTier {
    return (endRank - startRank) + 1;
  }
}

// Modify your existing WinningRules class or create a new specific one
// For this example, I'll assume you add these to WinningRules.
// If WinningRules is strictly for old conditions, create a new class like ChallengeRewardConfig
class RewardRules {
  // --- Old properties (if you still need them elsewhere) ---
  final int winningPoints;
  final int level;
  final int
      rank; // This 'rank' might have been a minimum rank to win *anything*

  // --- New properties for reward distribution ---
  final double? totalRewardPool;
  final List<RewardTier>? rewardDistribution;

  RewardRules({
    // Old params
    required this.winningPoints,
    required this.level,
    required this.rank,
    // New params
    this.totalRewardPool,
    this.rewardDistribution,
  });
}
